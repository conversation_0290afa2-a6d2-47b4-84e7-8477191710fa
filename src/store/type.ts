export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>;
  //当前选中的组织
  treeAct: any;
}

/**
 * 登录接口返回的信息 todo
 */
export interface ILoginRes {
  id: string;
  userName: string;
  loginName: string;
  sysCode: string;
  systemName: string;
  userTelphone: string;
  token: string;
  userType: string;
  photoUrl: string;
  userSex: string;
  uid: string;
  postId: string;
  postName: string;
  orgCode: string;
  orgName: string;
  deptId: string;
  deptName: string;
  unitId: string;
  unitName: string;
  roleIds: string;
  zhId: string;
  zhLogo: string;
  zhName: string;
  resourceVoList: any[];
  logoPicUrl: string;
  iconPicUrl: string;
  zhPlatformUrl: string;
  unitOrgType: string;
}
