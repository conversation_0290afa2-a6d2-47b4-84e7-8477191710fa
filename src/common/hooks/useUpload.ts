import { ref, Ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';

type UploaderResult = [(formData: FormData) => Promise<any>, Ref<boolean>, Ref<number>];

/**
 * 文件上传
 */
export function useUpload(uploadFileURL?: string): UploaderResult {
  const uploadProgress = ref(0);
  const [uploading, submit] = useAutoLoading();
  function uploadFn(formData: FormData): Promise<any> {
    const url = uploadFileURL || api.getUrl(api.type.file, api.name.file.uploadFile);
    return submit(
      $http.post(url, {
        data: formData,
        onUploadProgress: (_progress) => {
          uploadProgress.value = Math.floor((_progress.loaded / _progress.total) * 100);
        },
      })
    );
  }
  return [uploadFn, uploading, uploadProgress];
}
