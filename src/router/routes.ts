import { RouteRecordRaw } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';
import NoAuthLayout from '@/layouts/NoAuthLayout.vue';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'topRoute',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'home',
        redirect: { name: 'drawing' },
        children: [
          {
            path: 'drawing',
            name: 'drawing',
            component: () => import('@/views/drawing/index.vue'),
          },
          {
            path: 'video',
            name: 'video',
            // component: () => import('@/views/video/index.vue'), /video/plan
            children: [
              // 巡检计划管理
              {
                path: 'inspect-plan',
                name: 'inspect-plan',
                children: [
                  {
                    path: '',
                    name: 'inspectPlan',
                    component: () => import('@/views/inspect-plan/index.vue'),
                  },
                  {
                    path: 'update',
                    name: 'inspectPlanUpdate',
                    component: () =>
                      import('@/views/inspect-plan/updatePlan/index.vue'),
                    meta: { menuActiveKey: 'inspectPlan' },
                  },
                  {
                    path: 'detail',
                    name: 'inspectPlanDetail',
                    component: () => import('@/views/inspect-plan/Detail.vue'),
                    meta: { menuActiveKey: 'inspectPlan' },
                  },
                ],
              },
              // 巡检任务管理
              {
                path: 'inspect-task',
                children: [
                  {
                    path: '',
                    name: 'inspectTask',
                    component: () => import('@/views/inspect-task/index.vue'),
                  },
                  {
                    path: 'detail',
                    name: 'inspectTaskDetail',
                    component: () => import('@/views/inspect-task/Detail.vue'),
                    meta: { menuActiveKey: 'inspectTask' },
                  },
                ],
              },
              // 巡检设备
              {
                path: 'inspect-device',
                name: 'inspectDevice',
                component: () => import('@/views/inspect-device/index.vue'),
              },
            ],
          },
          {
            path: 'uav',
            name: 'uav',
            component: () => import('@/views/uav/index.vue'),
          },
          {
            path: 'uav-static',
            name: 'uavStatic',
            component: () => import('@/views/uav_static/index.vue'),
          },
          {
            path: 'robot',
            name: 'robot',
            component: () => import('@/views/robot/index.vue'),
          },
          {
            path: 'robot-static',
            name: 'robotStatic',
            component: () => import('@/views/robot_static/index.vue'),
          },
          {
            path: 'artificial',
            name: 'artificial',
            component: () => import('@/views/artificial/index.vue'),
          },
          {
            path: 'parameter-config',
            name: 'parameterConfig',
            component: () => import('@/views/parameter-config/index.vue'),
          },
        ],
      },

      // 巡检计划管理
      // {
      //   path: 'inspect-plan',
      //   children: [
      //     {
      //       path: '',
      //       name: 'inspectPlan',
      //       component: () => import('@/views/inspect-plan/index.vue'),
      //     },
      //     {
      //       path: 'detail',
      //       name: 'inspectPlanDetail',
      //       component: () => import('@/views/inspect-plan/Detail.vue'),
      //       meta: { menuActiveKey: 'inspectPlan' },
      //     },
      //   ],
      // },
      // // 巡检任务管理
      // {
      //   path: 'inspect-task',
      //   children: [
      //     {
      //       path: '',
      //       name: 'inspectTask',
      //       component: () => import('@/views/inspect-task/index.vue'),
      //     },
      //     {
      //       path: 'detail',
      //       name: 'inspectTaskDetail',
      //       component: () => import('@/views/inspect-task/Detail.vue'),
      //       meta: { menuActiveKey: 'inspectTask' },
      //     },
      //   ],
      // },
      // // 巡检设备
      // {
      //   path: 'inspect-device',
      //   name: 'inspectDevice',
      //   component: () => import('@/views/inspect-device/index.vue'),
      // },

      // 以下demo路由为代码参考示例，后期可删除 ->
      // {
      //   path: '/demo',
      //   name: 'demo',
      //   children: [
      //     {
      //       path: 'jurisdiction',
      //       name: 'jurisdiction',
      //       component: () => import('@/views/configure-mgr/jurisdiction/index.vue'),
      //     },
      //     {
      //       path: 'checklist-conf-list',
      //       name: 'checklistConfList',
      //       component: () => import('@/views/configure-mgr/checklist-conf/index.vue'),
      //     },
      //     {
      //       path: 'check-item-create',
      //       name: 'checkItemCreate',
      //       component: () => import('@/views/configure-mgr/checklist-conf/check-library/item-create/index.vue'),
      //     },
      //     {
      //       path: 'checklist-conf-modify/:id?',
      //       name: 'checklistConfModify',
      //       component: () => import('@/views/configure-mgr/checklist-conf/check-template/modify/Modify.vue'),
      //     },
      //     {
      //       path: 'checklist-conf-detail/:id',
      //       name: 'checklistConfDetail',
      //       component: () => import('@/views/configure-mgr/checklist-conf/check-template/detail/Detail.vue'),
      //     },
      //   ],
      // },
      // <-
    ],
  },
  {
    path: '/newDraw',
    name: 'newDraw',
    component: () => import('@/views/drawing/newIndex.vue'),
  },
];

export default routes;
