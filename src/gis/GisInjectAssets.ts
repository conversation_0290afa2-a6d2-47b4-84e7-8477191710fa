import { ref } from 'vue';

export class GisAssets {
  static loaded = ref(false);

  static async inject() {
    // 缓存key说明: 线上环境使用版本作为key， 本地开发环境每5分钟换一次key
    // const t = window.$SYS_CFG.version.main || (new Date().getTime() / 300e3) >> 0;

    // await window.$_ScriptInjector.inject(window.$SYS_CFG.gisPkgCss + '&t=' + t, {
    //   type: 'css',
    //   isLink: true,
    // });
    // await window.$_ScriptInjector.inject(window.$SYS_CFG.gisPkgJs + '&t=' + t, {
    //   type: 'js',
    //   isLink: true,
    // });

    // 初始化IndoorMap,IndoorThree
    IndoorMap.init();
    IndoorThree.init();
    IndoorThree.initAlarmPlugin();
    IndoorThree.initInspectionPlugin();

    this.loaded.value = true;
  }
}
