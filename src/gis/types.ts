export interface IGisConfig {
  target: string;
  uiScale?: boolean;
  uiZoom?: boolean;
  maxZoom?: number;
  minZoom?: number;
  zoom?: number;
  center: number[];
  tile: boolean;
  tileURL_Normal: string; //底图URL地址配置
  tile_Annotation: boolean; //标注是否可见
  tileURL_Annotation: string; //标注URL地址配置
  requestResData?: boolean;
  loadRegin?: boolean;
  sky: boolean; //开启天空
  gridLoadViewTypes: number[]; // 打开网格
  gridTypeIds: string;
  grid: boolean;
  gridLoad: boolean;
  onLoad: (mapIns: any) => void;
}
