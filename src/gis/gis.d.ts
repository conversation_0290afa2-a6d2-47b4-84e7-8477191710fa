interface Window {
  gisInsMin: any;
  GISShare: any;
  IndoorMap: any;
  ol: any;
  gisIns: any;
  GSMap: any;
  newIndoorService: any;
  CONST_map_satellite_baidu: any;
  CONST_map_annotation_baidu: any;

  CONST_Function_DeviceStateValueConvertFun_Default_3: any;
  CONST_GsSysParams_BC: any;
  IndoorService: any;
  CONST_GSCache: any;
  DicCache: any;
  CONST_GSParams: any;
  CONST_GSOptions: any;
  Cesium: any;
  IndoorGlobe: any;
  SGlobe: any;
  IndoorMap_BC_LayerArray_push: any;
  IndoorMap_BC_LayerArray_idxInit: any;
  CONST_ACS_WMTSLayer_Satellite_TianDiTu: any;
  CONST_ACS_WMTSLayer_Annotation_TianDiTu: any;
  CONST_ACS_MTS_TileLayer_BaiDu_Map: any;
  gisIndoor: any;
  CreateWMSLayer: any;
}

declare const THREE: any;
declare const CameraControls: any;
declare const IndoorThree: any;
declare const CONST_GeoData_China: any;
declare const CONST_GeoData_China_line: any;
declare const CONST_GeoData_Item: any;
declare const CONST_GeoData_Item_inline: any;
declare const CONST_GeoData_Item_outline: any;
