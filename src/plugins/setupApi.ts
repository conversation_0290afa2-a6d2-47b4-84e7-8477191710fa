import { $Config } from '@tanzerfe/http';
import useToastCtx from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store';

export function setupApi() {
  const store = useStore();
  $Config.getToken = () => store.userInfo.token;
  $Config.$toastDark = useToastCtx({ theme: 'dark' });
  $Config.$toastLight = useToastCtx({ theme: 'light' });
  //响应拦截
  $Config.responseHandler = (res, req) => {
    // console.log(res, req, '响应拦截');
    if (res.data.code == '403') {
      //跳转登录页
      setTimeout(() => {
        // location.href = import.meta.env.VITE_APP_BACKLOGINURL;
      }, 1000);
    }
  };
}
