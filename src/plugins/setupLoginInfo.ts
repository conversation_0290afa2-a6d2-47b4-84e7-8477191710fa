import router from '@/router';
import { useStore } from '@/store';
import { getUrlParams } from '@/utils/getParams';
import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';
interface ICheckSysPower {
  hasPower: boolean;
  token: string;
}

function getTokenLoginInfoByTokenApi(query: IObj<any>) {
  const url = api.getUrl(api.type.login, api.name.serve.getTokenLoginInfoByToken, query);
  console.log(url, '=======================');
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
function loginFn() {
  return new Promise(async (resolve, reject) => {
    const ui = useStore();
    const quer = await getUrlParams(location.href);
    console.log(quer, '获取头部登录参数');
    if (!quer || !quer.sysCode || !quer.token) {
      console.log('参数不全----');
      reject(false);
    }
    const curSysCode = quer.sysCode.split('#/');
    console.log(curSysCode, '分离出的syscode');
    //已参数换token
    getTokenLoginInfoByTokenApi({ sysCode: curSysCode[0], token: quer.token })
      .then(async (info) => {
        console.log('登录接口成功');
        await ui.setUser(info.data);
        resolve(ui);
      })
      .catch(() => {
        console.log('登录接口异常了');
        reject();
      });
  });
}
// 定义一个函数来设置favicon
function setFavicon(name: any, title: any) {
  const store = useStore();
  const faviconLink = document.querySelector("link[rel*='icon']") || document.createElement('link');
  (faviconLink as any).type = 'image/x-icon';
  (faviconLink as any).rel = 'shortcut icon';

  (faviconLink as any).href = store.userInfo.logoPicUrl;
  document.getElementsByTagName('head')[0].appendChild(faviconLink);
  //document.title = title;
  document.title = '智能巡检系统';
}

export function setupLoginInfo() {
  const ui = useStore();
  //适配无需登录可以进入巡检一张图新页面，链接地址中有id参数无需登录
  const headerParams = getUrlParams(location.href);
  router.beforeEach((to: any, from: any, next: any) => {
    console.log(from, '获取用户信息----');
    //next();
    //判断该用户有没有登录过
    if (ui.userInfo.id || headerParams.id) {
      console.log('有登录信息了');
      //ui.userInfo.zhName==='yanchang'
      setFavicon(ui.userInfo.zhLogo, ui.userInfo.zhName);
      next();
    } else {
      //跳登录
      loginFn()
        .then((res) => {
          console.log(res, '成功登录啦');
          //location.reload();
          setFavicon(ui.userInfo.zhLogo, ui.userInfo.zhName);
          next();
        })
        .catch(() => {
          console.log('登录失败了');
          //next();
          setTimeout(() => {
            // location.href = import.meta.env.VITE_APP_BACKLOGINURL;
          }, 1000);
        });
    }
  });
}
