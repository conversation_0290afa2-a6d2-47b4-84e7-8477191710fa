/**
 * 通信协议消息类型
 */
export const enum EVENT_TYPE {
  MESSAGE = 'MESSAGE',
}

/**
 * 消息体类型
 */
export const enum BRI_EVENT_TYPE {
  TREE_CHANGE = 'TREE_CHANGE', //组织树更改 左侧整个树的变化
  VIDEO_TASK = 'VIDEO_TASK', //任务列表
  VIDEO_TASK_STATUS = 'VIDEO_TASK_STATUS', //任务状态
  VIDEO_TASK_DETAIL = 'VIDEO_TASK_DETAIL', //任务详情//CHECK_PLAN_VIDEO
  VIDEO_TASK_DETAIL_STATUS = 'VIDEO_TASK_DETAIL_STATUS', //任务详情-任务巡检结果
  VIDEO_TASK_REVIEW = 'VIDEO_TASK_REVIEW', //任务回溯
  VIDEO_TASK_CHECK = 'VIDEO_TASK_CHECK', //点击开始巡检、或单个巡检 对应的就是新建隐患或无异常
  VIDEO_PLAN = 'VIDEO_PLAN', //计划列表
  VIDEO_PLAN_ADD = 'VIDEO_PLAN_ADD', //计划新建、编辑
  VIDEO_PLAN_ADD_LOCATION = 'VIDEO_PLAN_ADD_LOCATION', //计划新建 是巡检视频位置的选择联动
  VIDEO_PLAN_DETAIL = 'VIDEO_PLAN_DETAIL', //计划详情
  VIDEO_PLAN_TREE_CHANGE = 'VIDEO_PLAN_TREE_CHANGE', //计划树结构改变
  VIDEO_EQUIPMENT = 'VIDEO_EQUIPMENT', //设备列表
  VIDEO_EQUIPMENT_DETAIL = 'VIDEO_EQUIPMENT_DETAIL', //设备列表-详情
  VIDEO_EQUIPMENT_ITEMPLAYER = 'VIDEO_EQUIPMENT_ITEMPLAYER', //设备列表-详情视频播放
  DRAWING = 'DRAWING', //是否是巡检一张图的
  DRAWING_TREE_CHANGE = 'DRAWING_TREE_CHANGE', //一张图树结构改变
  VIDEO_EQUIPMENT_LOCATION = 'VIDEO_EQUIPMENT_LOCATION', //设备列表详情-查看位置
}

/**
 * Bridge 消息体协议类型
 */
export interface ISender {
  type: BRI_EVENT_TYPE;
  data?: ISenderData;
  treeActData?: any;
}

export interface ISenderData {
  type?: BRI_EVENT_TYPE | string;
  data?: NonNullable<Record<string, any>>;
  params?: Record<string, any>;
}
