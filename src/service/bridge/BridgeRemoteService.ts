import { BridgeService } from './BridgeService.ts';

export class BridgeRemoteService {
  private static _ins: BridgeService;
  static getIns() {
    if (!this._ins) {
      console.log('BridgeServiceIns实例初始化挂 window');
      window.BridgeServiceIns = this._ins;
      this._ins = new BridgeService('app_ehsInspectMgr', true);
    }

    return this._ins;
  }

  static init() {
    this.getIns();
  }
}
