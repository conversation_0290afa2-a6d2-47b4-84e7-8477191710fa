import { createApp, h, nextTick } from 'vue';
import { POI_POPUP_TPL } from './tpl';
import { useThemeRender } from '@/common/hooks/useThemeRender.ts';

const { theme_h } = useThemeRender();

/**
 * 气泡服务
 */
export class PopupService {
  public readonly popupIns: any; // 气泡实例
  public readonly popupContainerId: string; // 气泡容器

  private tplVue: any; // 气泡模板

  constructor(ins: any, popupId = 'JS_PopupContainer', offset?: [number, number]) {
    this.popupContainerId = popupId;
    this.popupIns = ins.addPopup({
      autoPan: true,
      autoPanAnimation: {
        duration: 250,
      },
      positioning: 'bottom-center',
      offset: offset || [0, -28],
      element: `<div id="${popupId}"></div>`,
    });
  }

  show(data: any, WGS84_XY: number[]) {
    const type = data._ui_popupType;
    const container = this.popupIns.getDOM().querySelector('#' + this.popupContainerId);
    const Comp = theme_h(POI_POPUP_TPL[type], {
      tplData: data,
      handleClose: this.close.bind(this),
    });

    this.tplVue?.unmount();
    this.tplVue = createApp(Comp);
    this.tplVue.mount(container);
    this.popupIns.show(WGS84_XY);
  }

  close() {
    this.tplVue?.unmount();
    this.tplVue = undefined;
  }
}
