<template>
  <div :class="$style.popupWrap">
    <div :class="$style.content">
      <span>{{ tplData.deviceAddress }}</span>
      <span :class="$style['video-btn']" @click="videoOpen"></span>
    </div>
    <VideoDia v-model:show="showVideoDia" :device-id="tplData.deviceId || ''" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import _isFunction from 'lodash-es/isFunction';
import VideoDia from '@/views/inspect-com/InspectVideoDia.vue';

defineOptions({ name: 'gisPopupTplPoitips' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}

const showVideoDia = ref(false);
const videoOpen = () => {
  showVideoDia.value = true;
};
</script>

<style module lang="scss">
.popupWrap {
  background: #10315b;
  box-shadow: inset 0px 3px 7px 0px rgba(16, 176, 243, 0.76);
  border-radius: 6px;
  border: 1px solid #0165b9;
  transform: skewX(-15deg);

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: auto;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--com-color-label);
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    transform: skewX(15deg);
    .video-btn {
      width: 16px;
      height: 16px;
      background: url('@/gis-floor/assets/video-bg.png') 0 0 no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
}
</style>
