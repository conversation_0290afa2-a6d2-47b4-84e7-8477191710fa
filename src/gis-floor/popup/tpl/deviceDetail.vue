<template>
  <div :class="$style.popupWrap">
    <div :class="$style.header">
      <span :class="$style.title">{{ deviceInfo.deviceType || '吸气式感烟火灾探测器' }}</span>
    </div>
    <div :class="$style.content">
      <div :class="$style.location">{{ deviceInfo.location || 'A座5F卫生间' }}</div>
      <div :class="$style.infoItem">
        <span :class="$style.label">设备编号：</span>
        <span :class="$style.value">{{ deviceInfo.deviceCode || '112245214410100003' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">主机回路点位：</span>
        <span :class="$style.value">{{ deviceInfo.hostPoint || '1-236-56' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">品牌型号：</span>
        <span :class="$style.value">{{ deviceInfo.brandModel || '海康威视112DS' }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import _isFunction from 'lodash-es/isFunction';

defineOptions({ name: 'gisPopupTplDeviceDetail' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 设备信息
const deviceInfo = computed(() => {
  return {
    deviceType: props.tplData?.deviceType,
    location: props.tplData?.location,
    deviceCode: props.tplData?.deviceCode,
    hostPoint: props.tplData?.hostPoint,
    brandModel: props.tplData?.brandModel,
  };
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}
</script>

<style module lang="scss">
.popupWrap {
  background: #10315b;
  box-shadow: inset 0px 3px 7px 0px rgba(16, 176, 243, 0.76);
  border-radius: 6px;
  border: 1px solid #0165b9;

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: auto;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--com-color-label);
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
  }
}
</style>
