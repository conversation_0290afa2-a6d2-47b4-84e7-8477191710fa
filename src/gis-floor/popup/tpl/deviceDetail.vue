<template>
  <div :class="$style.popupWrap">
    <div :class="$style.header">
      <span :class="$style.title">{{ deviceInfo.deviceType || '吸气式感烟火灾探测器' }}</span>
      <div :class="$style.location">{{ deviceInfo.location || 'A座5F卫生间' }}</div>
    </div>
    <div :class="$style.content">
      <div :class="$style.infoItem">
        <span :class="$style.label">设备编号：</span>
        <span :class="$style.value">{{ deviceInfo.deviceCode || '11224521441010' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">主机回路点位：</span>
        <span :class="$style.value">{{ deviceInfo.hostPoint || '1-236-56' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">品牌型号：</span>
        <span :class="$style.value">{{ deviceInfo.brandModel || '海康威发送到发' }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import _isFunction from 'lodash-es/isFunction';
import {}

defineOptions({ name: 'gisPopupTplDeviceDetail' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 设备信息
const deviceInfo = computed(() => {
  return {
    deviceType: props.tplData?.deviceType,
    location: props.tplData?.location,
    deviceCode: props.tplData?.deviceCode,
    hostPoint: props.tplData?.hostPoint,
    brandModel: props.tplData?.brandModel,
  };
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}
</script>

<style module lang="scss">
.popupWrap {
  position: relative;
  width: 357px;
  height: 220px;
  background: url('@/assets/3dpop_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 13px 39px;
  color: #ffffff;
  display: grid;
  grid-template-rows: 40% 60%;
  gap: 8px;

  .header {
    font-family:
      Alibaba PuHuiTi 2,
      Alibaba PuHuiTi 20;
    font-weight: 600;
    font-size: 18px;
    .title {
      font-size: 16px;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
    .location {
      font-weight: 400;
      font-size: 14px;
    }
  }

  .content {
    .infoItem {
      display: flex;
      margin-bottom: 5px;
      font-weight: 400;
      font-size: 14px;

      .value {
        text-align: left;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
