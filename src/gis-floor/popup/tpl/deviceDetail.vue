<template>
  <div :class="$style.popupWrap">
    <div :class="$style.header">
      <span :class="$style.title">{{ deviceInfo.deviceType || '吸气式感烟火灾探测器' }}</span>
    </div>
    <div :class="$style.content">
      <div :class="$style.location">{{ deviceInfo.location || 'A座5F卫生间' }}</div>
      <div :class="$style.infoItem">
        <span :class="$style.label">设备编号：</span>
        <span :class="$style.value">{{ deviceInfo.deviceCode || '112245214410100003' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">主机回路点位：</span>
        <span :class="$style.value">{{ deviceInfo.hostPoint || '1-236-56' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">品牌型号：</span>
        <span :class="$style.value">{{ deviceInfo.brandModel || '海康威视112D5' }}</span>
      </div>
    </div>
    <!-- 弹框箭头指向下方 -->
    <div :class="$style.arrow"></div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import _isFunction from 'lodash-es/isFunction';

defineOptions({ name: 'gisPopupTplDeviceDetail' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 设备信息
const deviceInfo = computed(() => {
  return {
    deviceType: props.tplData?.deviceType,
    location: props.tplData?.location,
    deviceCode: props.tplData?.deviceCode,
    hostPoint: props.tplData?.hostPoint,
    brandModel: props.tplData?.brandModel,
  };
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}
</script>

<style module lang="scss">
.popupWrap {
  position: relative;
  width: 320px;
  min-height: 160px;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
  border: 2px solid #60a5fa;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  padding: 16px 20px;
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;

  // 弹框位置在正上方的箭头
  .arrow {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #60a5fa;

    &::before {
      content: '';
      position: absolute;
      top: -12px;
      left: -8px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #3b82f6;
    }
  }

  .header {
    margin-bottom: 12px;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }

  .content {
    .location {
      font-size: 14px;
      color: #e0f2fe;
      margin-bottom: 12px;
      font-weight: 500;
    }

    .infoItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #bfdbfe;
        font-weight: 400;
        min-width: 100px;
      }

      .value {
        color: #ffffff;
        font-weight: 500;
        text-align: right;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
