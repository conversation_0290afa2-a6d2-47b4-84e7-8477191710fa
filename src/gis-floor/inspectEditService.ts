import { FloorGisService } from './floorGisService';
import { IDeviceRow } from './type';

export class InspectEditService {
  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    const _list = FloorGisService.inspectList.value;
    if (_list.includes(data)) {
      const _index = _list.indexOf(data);
      _list.splice(_index, _list.length - _index);
    } else {
      _list.push(data);
    }
    FloorGisService.showInspectList(_list);
  }
}
