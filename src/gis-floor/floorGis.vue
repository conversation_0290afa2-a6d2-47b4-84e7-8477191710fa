<template>
  <n-spin :show="!GisService.isReady" class="w-full h-full" content-class="w-full h-full">
    <div class="map-wrapper">
      <div id="floorGis" class="map"></div>
      <div v-show="GisService.floorLoadFail.value" class="no-gis"></div>
      <!-- floor control -->
      <FloorControl v-if="!isDeviceLoc" class="floor-control" ref="FloorRef" @change="floorChange" />
    </div>
  </n-spin>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import FloorControl from './floorControl.vue';
import { FloorGisService as GisService } from './floorGisService';
import { getDevicesList } from './featch';
import { IDeviceRow } from './type';

import { EGisType, TGisType } from './constant';

defineOptions({ name: 'FloorGisComp' });

interface IProps {
  type: TGisType;
  buildId: string | null;
  floorId: string | null;
  defaultInspectList?: Array<IDeviceRow>;
  deviceList?: Array<IDeviceRow>;
}
const props = withDefaults(defineProps<IProps>(), {
  type: EGisType.INSPECTEDIT,
  buildId: '',
  floorId: '',
  defaultInspectList: () => [],
  deviceList: () => [],
});

// 初始值
if (props.defaultInspectList?.length > 0) GisService.inspectList.value = props.defaultInspectList;
GisService.gisType = props.type;

const isDeviceLoc = ref(GisService.gisType === EGisType.DEVICELOC);

const emits = defineEmits(['on-update:floor', 'on-update:inspect']);

const FloorRef = ref();
const floorChange = (floorId: string) => {
  if (GisService.gisType === EGisType.INSPECTEDIT) {
    emits('on-update:floor', floorId);
  }
  if (GisService.gisType === EGisType.INSPECTDETAIL) {
    GisService.curFloorId.value = floorId;
  }
};

const getFloorPoiList = async () => {
  try {
    const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
    const unitInfo = GisService.curUnitInfo.value;
    const floorId = isMultiple ? '' : GisService.curFloorId.value;
    const res = await getDevicesList({
      deptId: unitInfo?.treeId,
      buildingId: props.buildId,
      floorId,
    });
    const list = res.data.map((item) => ({
      ...item,
      buildId: item.buildingId,
    }));

    GisService.showPoiList(list);
  } catch (error) {}
};

// 绘制巡检路径
const showInspectList = () => {
  if (GisService.inspectList.value.length < 1) return;
  const list = GisService.inspectList.value.map((item) => ({
    ...item,
    buildId: item.buildingId,
  }));

  if (GisService.gisType === EGisType.INSPECTDETAIL) {
    // 需要先撒点
    GisService.showPoiList(list);
  }

  GisService.showInspectList(list);
};

// 巡检路径
watch(
  () => GisService.inspectList.value,
  (nv) => {
    emits('on-update:inspect', nv);
  },
  {
    deep: true,
  }
);

// 根据gisType，监听楼层变化
const watchFloorHandle = async (newV: string | null, oldV: string | null | undefined) => {
  if (!newV) return;

  const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
  if (isMultiple) {
    if (GisService.needRenderGis) {
      await GisService.renderFloorGis();
      // 楼层所有设备
      if (GisService.gisType === EGisType.INSPECTEDIT) await getFloorPoiList();
      // 一次路径
      showInspectList();
    } else {
      await GisService.changeFloor(newV as string);
    }
  } else {
    await GisService.changeFloor(newV as string);
    // 楼层所有设备
    if (GisService.gisType === EGisType.INSPECTEDIT) await getFloorPoiList();
    // 每次路径
    showInspectList();
  }
};
if (GisService.gisType === EGisType.INSPECTEDIT) {
  watch(
    () => [GisService.isReady.value, props.floorId],
    ([isReady, floorId]) => {
      if (isReady && floorId) {
        GisService.curFloorId.value = floorId as string;
        watchFloorHandle(floorId as string, null);
      }
    }
  );
}
if (GisService.gisType === EGisType.INSPECTDETAIL) {
  watch(
    () => [GisService.isReady.value, GisService.curFloorId.value],
    ([isReady, floorId]) => {
      if (isReady && floorId) {
        watchFloorHandle(floorId as string, null);
      }
    },
    { immediate: true }
  );
}

watch(
  () => [GisService.isReady.value, props.buildId],
  async ([isReady, buildId]) => {
    if (isReady && buildId) {
      GisService.curBuildId.value = buildId as string;
      // 需要标记开始重新加载gis
      GisService.needRenderGis = true;

      // 设备位置查看，不需要切换楼层
      if (GisService.gisType === EGisType.DEVICELOC) {
        GisService.curFloorId.value = props.floorId as string;
        await GisService.renderFloorGis();

        if (props.deviceList?.length > 0) {
          const list = props.deviceList.map((item) => ({
            ...item,
            buildId: item.buildingId,
          }));
          GisService.showPoiList(list);
        }
      } else {
        FloorRef.value?.getFloorListData();
      }
    }
  }
);

onMounted(async () => {
  try {
    await GisService.init();
  } catch (e) {
    console.error('GIS PAGE:', e);
  }
});

onBeforeUnmount(() => {
  // GisService.clearGis();
  GisService.destoryGis();
});
</script>

<style scoped lang="scss">
.map-wrapper {
  @apply relative w-full h-full overflow-hidden;
  background-color: var(--skin-bg0);
  border: 1px solid;
  border-color: var(--skin-bd2);

  .map {
    @apply absolute top-0 left-0 w-full h-full;
    z-index: 0;
  }
  .no-gis {
    @apply absolute top-0 left-0 w-full h-full bg-[#fff];
    z-index: 9;
  }

  .floor-control {
    @apply absolute top-[20px] right-[10px];
    z-index: 2;
    transform: translateX(-50%);
  }
}
</style>
