import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj, IPageRes } from '@/types';
import { IDeviceRow } from './type';

export function getBuildingListByUnitId(unitId: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getBuildingListByUnitId, { unitId });
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function getFloorList(query: { unitId: string; buildId: string }) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getFloorList, { ...query });
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function getDevicesList(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, api.name.intelligent.getDeviceList, params);
  return $http.post<IDeviceRow[]>(url, { data: { _cfg: { showTip: true }, ...data } });
}

// 查询电子档案单位信息
export function getErecordUnitInfo(orgCode: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getErecordUnitInfo, { orgCode });
  return $http.post(url, { data: { _cfg: { showTip: true } } });
}

// 获取gis中 设备信息弹框
