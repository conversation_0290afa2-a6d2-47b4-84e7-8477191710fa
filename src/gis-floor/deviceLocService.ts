import { FloorGisService } from './floorGisService';
import { IDeviceRow } from './type';
import { POI_POPUP_TYPE } from './popup/tpl';

export class DeviceLocService {
  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    const _ins = FloorGisService.gisIns;
    const isMultiple = FloorGisService.gisIns.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const _curLyr = isMultiple
      ? _ins.getCurrentLayer().GetFloorInfoIconLayers()
      : _ins.getCurrentLayer().getIconLayer();

    const device_geo = _ins.getGeoObjectByClientXY(_curLyr, e.getX(), e.getY());
    if (!device_geo) return;

    // 摄像头设备
    if (data.deviceTypeId === _ins.options.videoBufferQueryVideoTypeCode) return;

    FloorGisService.devicePopup.show(
      {
        _ui_popupType: POI_POPUP_TYPE.DeviceDetail,
        ...data,
      },
      device_geo.object.getScenePosition()
    );
  }
}
