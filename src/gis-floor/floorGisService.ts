import { ref } from 'vue';
import { GisBase } from '@/gis/GisBase';
import { PopupService } from './popup/gisPopupSerivce';
import { POI_POPUP_TYPE, POI_POPUP_TPL } from './popup/tpl';
import { IDeviceRow, IErecordUnit } from './type';
import { getErecordUnitInfo } from './featch';
import { EGisType, TGisType } from './constant';
import { InspectEditService as InspectEdit } from './inspectEditService';
import { InspectDetailService as InspectDetail } from './inspectDetailService';
import { DeviceLocService as DeviceLoc } from './deviceLocService';

export async function getUnitInfo(id: string) {
  const defaultModel = 5;
  try {
    const res: any = await getErecordUnitInfo(id);
    if (res.code === 'success' && res.data) {
      const { unitId, serviceModelCode, floorMapType } = res.data;

      const _info = {
        treeId: id,
        unitId,
        serviceModelCode,
        floorMapType,
      };

      // 合肥院，强制设置多楼层模式
      if (unitId === 'AHHF_QHHFY_20180408') {
        _info.floorMapType = IndoorMap.ViewType.IndoorAreaVector3DM;
      }
      FloorGisService.curUnitInfo.value = _info;
      return serviceModelCode;
    } else {
      return defaultModel;
    }
  } catch (error: any) {
    console.log(error);
    return defaultModel;
  }
}

export class FloorGisService {
  // 控制地图模式
  static gisType: TGisType | undefined = undefined;

  // 电子档案单位数据
  static curUnitInfo = ref<IErecordUnit | null>(null);

  static curBuildId = ref('');
  static curFloorId = ref('');
  // 巡检点位
  static inspectList = ref<IDeviceRow[]>([]);

  static gisIns: any;
  static isReady = ref(false);
  static needRenderGis = true;
  static floorLoadFail = ref(false);

  static hoverPopup: any;
  static devicePopup: any;

  static async init() {
    window.gisIns = this.gisIns = await GisBase.create('floorGis');
    this.isReady.value = true;
  }

  /**
   * @param buildId
   * @param floorId
   * @param mapType
   * @returns
   */
  static renderFloorGis() {
    //渲染平面图
    return new Promise<{ mapType: number; gisIns: any }>((resolve, reject) => {
      if (!FloorGisService.curFloorId.value) {
        reject({ msg: '没有楼层id' });
      }

      let mapType = FloorGisService.curUnitInfo.value?.floorMapType || 1;

      // 设备位置查看
      if (FloorGisService.gisType === EGisType.DEVICELOC) mapType = 1;

      FloorGisService.gisIns?.showFloorData(
        mapType,
        undefined, //单位id
        FloorGisService.curBuildId.value, //楼栋id
        FloorGisService.curFloorId.value, //楼层id
        undefined, //图纸地址 （用于查询不到室内GIS数据时自动跳转，可缺省(异常则走内部跳转)）
        function (mapType: number, success: boolean, objArgs: any, indoor: any) {
          if (!success) {
            FloorGisService.floorLoadFail.value = true;
            reject({ msg: '加载失败' });
          }

          FloorGisService.floorLoadFail.value = false;

          // 室内楼层模式在此处注册
          FloorGisService.registerGisEvent();

          // 多楼层
          if (mapType === IndoorMap.ViewType.IndoorAreaVector3DM) {
            FloorGisService.needRenderGis = false;
            objArgs.callback = function (indoor: any) {
              indoor.getCurrentLayer().Expand({ interval: 1000 });
            };
          }
          // 单楼层楼层
          if (mapType === IndoorMap.ViewType.IndoorAreaVector) {
          }

          resolve({
            mapType,
            gisIns: indoor,
          });
        }
      );
    });
  }

  static registerGisEvent() {
    // popup注册, 实例化气泡服务
    FloorGisService.hoverPopup = new PopupService(FloorGisService.gisIns);
    FloorGisService.devicePopup = new PopupService(FloorGisService.gisIns);

    // 事件注册
    FloorGisService.gisIns.onMouseMove = this.onHover;
    //设备点位图标 点击事件
    FloorGisService.gisIns.onDeviceSelected = this.onDeviceClick;
    //未拾取到任何数据
    FloorGisService.gisIns.onNullSelected = this.onUnllClick;
  }

  static onHover(e: any) {
    const _ins = FloorGisService.gisIns;
    const isMultiple = FloorGisService.gisIns.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const _curLyr = isMultiple
      ? _ins.getCurrentLayer().GetFloorInfoIconLayers()
      : _ins.getCurrentLayer().getIconLayer();

    const device_geo = _ins.getGeoObjectByClientXY(_curLyr, e.getX(), e.getY());
    if (!device_geo) return;
    const deviceData = device_geo.object.gsData;
    // 摄像头设备
    if (deviceData.deviceTypeId === _ins.options.videoBufferQueryVideoTypeCode) {
      FloorGisService.hoverPopup.show(
        {
          _ui_popupType: POI_POPUP_TYPE.PoiTips,
          ...deviceData,
        },
        device_geo.object.getScenePosition()
      );
    }
  }

  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    if (FloorGisService.gisType === EGisType.INSPECTEDIT) {
      InspectEdit.onDeviceClick(data, e, obj, target);
    }

    if (FloorGisService.gisType === EGisType.INSPECTDETAIL) {
      InspectDetail.onDeviceClick(data, e, obj, target);
    }

    if (FloorGisService.gisType === EGisType.DEVICELOC) {
      DeviceLoc.onDeviceClick(data, e, obj, target);
    }
  }

  static onUnllClick() {
    if (FloorGisService.hoverPopup) FloorGisService.hoverPopup.close();
    if (FloorGisService.devicePopup) FloorGisService.devicePopup.close();
  }

  static showPoiList(poiList: any[]) {
    if (poiList.length < 1) return;

    const _ins = FloorGisService.gisIns;

    const isMultiple = _ins.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    const curLyr = isMultiple ? _ins.getCurrentLayer() : undefined;

    const xyz = _ins.getDeviceFieldNameXYZ(_ins.getViewType());
    _ins.showFloorDataDevice(poiList, xyz[0], xyz[1], xyz[2], undefined, false, undefined, curLyr);
  }

  static showInspectList(list: any[]) {
    FloorGisService.gisIns.AppendInspectionArrayToTargetLayer(
      list,
      undefined,
      undefined,
      undefined,
      false,
      undefined,
      true,
      1
    );
    FloorGisService.gisIns.render();
  }

  static async changeFloor(floordId: string) {
    if (!floordId) return;

    this.onUnllClick();

    const _ins = FloorGisService.gisIns;
    const isMultiple = _ins.getViewType() === IndoorMap.ViewType.IndoorAreaVector3DM;
    if (isMultiple) {
      _ins.getCurrentLayer().SetCurrentFloorNum(floordId, 1000);
    } else {
      await FloorGisService.renderFloorGis();
    }
  }

  static resetData() {
    this.isReady.value = false;
    this.needRenderGis = true;
    this.floorLoadFail.value = false;
    this.curUnitInfo.value = null;
    this.curBuildId.value = '';
    this.curFloorId.value = '';
    this.inspectList.value = [];
  }

  static clearGis() {
    if (this.gisIns) this.gisIns.clearAll();
    this.resetData();
  }

  static destoryGis() {
    this.resetData();
    window.gisIns = this.gisIns = null;
  }
}
