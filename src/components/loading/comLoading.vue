<template>
  <div :class="$style['loadingWrap']">
    <n-spin size="small" :show="show" />
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
const props = defineProps({
  show: {
    type: Boolean,
    default: true,
  },
});
defineOptions({ name: 'ComLoadingA' });
</script>

<style module lang="scss">
.loadingWrap {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
