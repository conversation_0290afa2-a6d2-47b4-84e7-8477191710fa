<template>
  <div :class="$style.treeWrap">
    <div v-if="searchShow" :class="[$style.treeInput]">
      <!-- <n-input v-model:value="pattern" placeholder="请输入组织名称">
        <template #suffix>
          <BySearch :class="$style['icon']" />
        </template>
      </n-input> -->
      <!-- <n-tree-select
        placeholder="请输入组织名称"
        :key-field="props.keyField"
        :label-field="props.labelField"
        v-model:value="pattern"
        :options="treeSelectSearchOpt"
        @update:value="handleUpdateValue"
        :render-switcher-icon="renderLabelIcon"
        filterable
      /> -->
      <n-input v-model:value="pattern" placeholder="搜索">
        <template #suffix>
          <BySearch :class="$style['icon']" />
        </template>
      </n-input>
    </div>
    <div :class="[$style.comTree]">
      <n-scrollbar style="max-height: 100%">
        <!-- :pattern="pattern" :override-default-node-click-behavior="override" :default-expanded-keys="defaultSelectedkeys" 
         :default-selected-keys="defaultSelectedkeys" :expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          :default-selected-keys="selectedKeys"
          :watch-props="['defaultExpandedKeys']"
          -->
        <n-tree
          class="tree-cont"
          :show-irrelevant-nodes="false"
          :pattern="pattern"
          block-line
          default-expand-all
          :data="data"
          :override-default-node-click-behavior="override"
          :default-selected-keys="selectedKeys"
          on-update:selected-keys
          :render-prefix="renderPrefix"
          :render-switcher-icon="renderLabelIcon"
          :default-expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          :key-field="props.keyField"
          :label-field="props.labelField"
          size="large"
          style="
            --n-node-content-height: 44px;
            --n-node-border-radius: 4px;
            --n-node-color-hover: rgba(82, 124, 255, 0.1);
            height: 500px;
          "
        />
      </n-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BySearch } from '@kalimahapps/vue-icons';
import type { TreeInst, TreeOption } from 'naive-ui';
import { computed, h, ref, toRaw, VNode, watch } from 'vue';
import TreeParentIcon from './comp/TreeParentIcon.vue';
import TreeChildIcon from './comp/TreeChildIcon.vue';
import TreeBmIcon from './comp/TreeBmIcon.vue';
import TreeYwdwIcon from './comp/TreeYwdwIcon.vue';
import TreeJgdwIcon from './comp/TreeJgdwIcon.vue';
import { AnFilledCaretRight } from '@kalimahapps/vue-icons';
import { useStore } from '@/store';
import { storeToRefs } from 'pinia';
import { getOrgCogPathAPI } from './fetchData';
const store = useStore();
const emits = defineEmits(['action']);
type listT = {
  data: TreeOption[];
  selKey?: string[]; //默认选中项
  openKey?: string[]; //默认打开项
  labelField?: string;
  keyField?: string;
  searchShow?: boolean;
  selUnId?: string;
};

const props = withDefaults(defineProps<listT>(), {
  labelField: 'text',
  keyField: 'id',
  searchShow: true,
});
const pattern = ref('');

const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

// watch(
//   () => props.data,
//   (v) => {
//     expandedKeys.value = [];
//     selectedKeys.value = [];
//     if (v.length) {
//       let curKey = v[0].id as string;
//       expandedKeys.value = [curKey];
//       setTimeout(() => {
//         selectedKeys.value = [curKey];
//       });
//       if (props.selUnId) {
//         expandedKeys.value = [props.selUnId];
//         setTimeout(() => {
//           selectedKeys.value = [props.selUnId];
//         });
//       }
//       return;
//     }
//   },
//   {
//     immediate: true,
//   }
// );
watch(
  () => props.data,
  (nv) => {
    if (props.data.length) {
      let curKey = props.data[0].id as string;
      expandedKeys.value = [curKey];
      // defaultSelectedkeys.value = [curKey];
      // 本地有 默认选中
      const store = useStore();
      const { treeAct } = storeToRefs(store);
      console.log(treeAct.value, 'treeAct.value------data');
      if (treeAct.value) {
        selectedKeys.value.push(treeAct.value.id);
        getOrgCogPathAPI({ chooseOrgCode: treeAct.value.id, topOrgCode: store.userInfo.orgCode }).then((res) => {
          console.log(res, 'getOrgCogPathAPI');
          treeAct.value.deptIds = res.data.deptIds;
          store.setTreeAct(treeAct.value);
        });
        // emits('action', treeAct.value);
      } else {
        const store = useStore();
        let chooseTree = props.data[0];
        selectedKeys.value = [chooseTree.id as string];
        getOrgCogPathAPI({ chooseOrgCode: chooseTree.id, topOrgCode: store.userInfo.orgCode }).then((res) => {
          console.log(res, 'getOrgCogPathAPI');
          chooseTree.deptIds = res.data.deptIds;
          store.setTreeAct(chooseTree);
        });
      }
    }
  },
  { immediate: true }
);

// function override(keys: string[], option: TreeOption[]) {
//   console.log(option, '点击选择树结构-------');
//   if (!option.length) {
//     return;
//   }
//   pattern.value = '';
//   updateTree(option[0]);
// }
function override({ option }: { option: TreeOption }) {
  const op: any = option;
  console.log(option, '--------------------option');
  selectedKeys.value = [op.id];
  const store = useStore();
  getOrgCogPathAPI({ chooseOrgCode: op.id, topOrgCode: store.userInfo.orgCode }).then((res) => {
    console.log(res, 'getOrgCogPathAPI');
    option.deptIds = res.data.deptIds;
    store.setTreeAct(option);
    emits('action', option);
  });
}
function renderPrefix(info: { option: TreeOption; checked: boolean; selected: boolean }) {
  // if (info.option.level === 1) {
  //   return h(TreeChildIcon);
  // }
  // return h(TreeChildIcon);
  //如果不是单位
  //如果不是单位
  if (info.option.type == '0' && info.option.attributes.orgType == '0') {
    return h(TreeBmIcon);
  }
  //如果是单位并且单位为业务单位
  if (info.option.type == '1' && info.option.attributes.orgType == '1') {
    return h(TreeYwdwIcon);
  }
  //如果是单位并且单位为监管单位
  if (info.option.type == '1' && info.option.attributes.orgType == '2') {
    return h(TreeJgdwIcon);
  }
}
function renderLabelIcon(info: { option: TreeOption; checked: boolean; selected: boolean }) {
  if (info.option.hasChildren) {
    return h(AnFilledCaretRight);
  }
  return h('span');
}

// function handleUpdateValue(value: string, option: TreeOption) {
//   console.log('树结构搜索----', option);
//   updateTree(option);
// }
function updateTree(option: TreeOption) {
  const curKey = option.deptIds as string;
  const keys = curKey.split(',');
  const keyId = option.id as string;
  selectedKeys.value = [keyId];
  expandedKeys.value = keys;
  console.log('树结构改变----', option, selectedKeys.value, expandedKeys.value);
  const store = useStore();
  getOrgCogPathAPI({ chooseOrgCode: option.id, topOrgCode: store.userInfo.orgCode }).then((res) => {
    option.deptIds = res.data.deptIds;
    store.setTreeAct(option);
    emits('action', option);
  });
}
defineExpose({
  updateTree,
});

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.treeWrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.treeInput {
  margin-bottom: 12px;

  .icon {
    color: #c0c4cc;
  }
}

.comTree {
  flex: 1;
}
</style>
<style lang="scss">
.tree-cont {
  &.n-tree {
    .n-tree-node {
      padding-left: 10px !important;
    }
  }

  &.n-tree .n-tree-node-switcher {
    width: 15px !important;
  }

  &.n-tree .n-tree-node-switcher .n-tree-node-switcher__icon {
    width: 12px !important;
    //color: #606266 !important;
  }
}
</style>
