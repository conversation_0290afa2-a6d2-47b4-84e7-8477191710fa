<template>
  <div :class="$style.mapWrap">
    <div id="map-container" :class="$style.mapContainer"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, provide, Ref, ref, onMounted } from 'vue';
interface Props {
  unitId: string;
  aerialViewImg: string;
  markList?: any[];
}
const props = defineProps<Props>();
const emits = defineEmits(['change']);

const INDOORMAP = window.$SYS_MAP;
console.log(window.$SYS_MAP, 'map组件');
const CONST_GSCache = {
  adminCodeDicCache: new INDOORMAP.dicCache(50), //2D & 2.5D & 3DM
  indoorAreaDicCache: new INDOORMAP.dicCache(16), //2D & 2.5D & 3DM
  indoorAreaExtentDicCache: new INDOORMAP.dicCache(32), //2D & 2.5D & 3DM
  gridAreaDicCache: new INDOORMAP.dicCache(16), //2D & 2.5D & 3DM
  ovBuildAreaDicCache: new INDOORMAP.dicCache(16), //2D & 2.5D & 3DM
};
//常规线上服务配置
const CONST_GSOptions = {
  dbService: INDOORMAP.newIndoorService(`${INDOORMAP.url}/api/v3/bw-svc-indoor-gis-service/indoorMap`), //室内GIS服务地址
  dbService_Record: INDOORMAP.newIndoorService(`${INDOORMAP.url}/api/v3/bw-svc-indoor-gis-service/record`), //室内GIS服务地址（电子档案相关）
  unitUrlHeader: `${INDOORMAP.url}/img1/floorImage`, //单位图片地址
  deviceIconUrlHeader: `${INDOORMAP.url}/img1/deviceIcons`, //设备图标地址
  deviceIconAlarmGifUrl: `${INDOORMAP.url}/img1/deviceIcons/gif/alarm.gif`, //报警设备图标
  wmsURL: `${INDOORMAP.url}/geoserver/GS/wms`, //后端渲染地址
};
//初始化地图 加载地图
function mapInit() {
  new INDOORMAP.map(
    //Merge（a, b)将 a的指针添加到 b的指针中
    INDOORMAP.map.Merge([CONST_GSCache, CONST_GSOptions], {
      target: 'map-container', //绑定的标签位置
      tile: false, //底图是否可见
      onLoad: function (sender: any) {
        //加载完成事件
        console.log(sender, '构建完成 sender-------');
        loadFloorData(
          sender,
          props.unitId, //'AHHF_QHHFY_20180408',
          props.aerialViewImg ? props.aerialViewImg : '',
          '340100YYZX201805230001'
        );
      },
    })
  );
}
//加载地图楼层
function loadFloorData(indoor: any, unitId: string, aerialViewImg: string, subCenterCode: string) {
  // document.getElementById('loadingContainer').style.display = 'block'; // 显示加载动画
  indoor.options.gsTag_subCenterCode = subCenterCode;
  console.log(indoor.options.unitUrlHeader, 'indoor.options.unitUrlHeader------');
  //渲染鸟瞰图
  indoor.showFloorData(
    INDOORMAP.map.ViewType.OVUnitImage, //数据类型
    unitId, //单位id
    null, //楼栋id（鸟瞰图不填）
    '', //楼层id（鸟瞰图不填）
    aerialViewImg,
    //indoor.options.unitUrlHeader + '/' + aerialViewImg, //鸟瞰图地址
    onGeoDataLoad //加载完成事件
  );
}
//地图加载完成后的回调
function onGeoDataLoad(mapType: any, success: any, objArgs: any, indoor: any) {
  if (!success) {
    alert('数据加载失败！');
    return;
  }
  console.log('地图加载完成', indoor, `${INDOORMAP.demoUrl}/api/ehscommom/unitmap/point`);
  // TODO 地图创建完成，可根据业务自行请求数据
  // indoor.showOVDataVideo (ovVideoArray, ovVideoFieldNameX, ovVideoFieldNameY, ovVideoFieldNameZ, dataFilterFun, bAppend){

  // }
  indoor.showOVDataVideoOnLoadGeoData(
    mapType, //数据类型
    success,
    `/api/ehscommom/unitmap/point`,
    //'http://192.168.65.172:8888/ehscommom/unitmap/point',
    // `unitId=${props.unitId} `,
    '',
    'POST',
    undefined,
    function (
      data: any,
      markFieldNameX: any,
      markFieldNameY: any,
      markFieldNameZ: any,
      indoor: any,
      idx: any,
      count: any
    ) {
      //过滤器
      console.log('视频数据逐条过滤信息：', data);
      //if (idx % 2 === 1) data.imageStyle = 'vline'; //此处可修改图表样式
    }
  );
  // indoor.showOVDataBuildOnLoadGeoData(
  //   mapType, //数据类型
  //   success,
  //   'https://www.tanzercloud.com/api/v2/operation-management-service/plan/eventWL/getTAndAByUnitId',
  //   'subCenterCode=' + indoor.options.gsTag_subCenterCode,
  //   'POST',
  //   undefined,
  //   function (data, markFieldNameX, markFieldNameY, markFieldNameZ, indoor, idx, count) {
  //     //过滤器,过滤出需要的data
  //     console.log('楼栋数据逐条过滤信息：', data);
  //     if (idx % 2 === 1) data.imageStyle = 'calltheplice'; //此处可修改图表样式
  //   }
  // );
}

function handleUpdateValue(name: string) {
  emits('change', name);
}

onMounted(() => {
  mapInit();
});

defineOptions({ name: 'ComMap' });
</script>

<style module lang="scss">
.mapWrap {
  width: 100%;
  height: 100%;
}
.mapContainer {
  width: 100%;
  height: 100%;
}
</style>
