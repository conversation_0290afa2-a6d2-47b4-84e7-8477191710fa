<template>
  <n-scrollbar :class="$style.ComBoxGroup" x-scrollable content-style="height: 100%">
    <div :class="$style.listGroup">
      <slot></slot>
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
defineOptions({ name: 'ComBoxGroup' });
</script>

<style module lang="scss">
.ComBoxGroup {
  .listGroup {
    display: grid;
    grid-template-columns: repeat(3, minmax(380px, 1fr));
    grid-auto-rows: minmax(315px, 50%);
    gap: vw(20);
    height: 100%;
    min-height: 0; /* 允许网格容器收缩 */

    > div {
      position: relative;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
