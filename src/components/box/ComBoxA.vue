<template>
  <div :class="$style.ComBoxA">
    <slot name="top-r">
      <div v-if="statusLabel" :class="[$style.topR, $style[statusBgClass]]">
        {{ statusLabel }}
      </div>
    </slot>

    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { STATUS_COLORS, type StatusColorType } from '@/views/common/contant';

interface Props {
  statusBgClass?: StatusColorType;
  statusLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  statusBgClass: 'blue',
});

defineOptions({ name: 'ComBoxA' });
</script>

<style module lang="scss">
.ComBoxA {
  width: 100%;
  height: 100%;
  background: url('./assets/bg.png') 0 0 no-repeat;
  background-size: 100% 100%;
  padding: vw(20) vw(15) vw(15);

  > div {
    min-height: 0;
    height: 100%;
  }

  .topR {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 44px;
    padding: 0 vw(5) 0 vw(8);
    height: 20px;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    border-bottom-left-radius: 10px;

    &.blue {
      background: v-bind('STATUS_COLORS.blue');
    }

    &.yellow {
      background: v-bind('STATUS_COLORS.yellow');
    }

    &.green {
      background: v-bind('STATUS_COLORS.green');
    }

    &.gray {
      background: v-bind('STATUS_COLORS.gray');
    }

    &.purple {
      background: v-bind('STATUS_COLORS.purple');
    }
  }
}
</style>
