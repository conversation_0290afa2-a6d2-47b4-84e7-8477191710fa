<template>
  <n-timeline :class="$style.ComBoxC" :icon-size="20">
    <slot />
  </n-timeline>
</template>

<script setup lang="ts">
defineOptions({ name: 'ComBoxC' });
</script>

<style module lang="scss">
.ComBoxC {
  p {
    line-height: 2;
  }

  :global(.n-timeline-item-content__title) {
    font-weight: bold;
    font-size: 16px !important;
  }

  :global(.n-timeline-item-timeline__circle) {
    border: unset !important;
    background: url('data:image/svg+xml;utf8,\
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 27 27">\
      <path fill-rule="evenodd" fill="%230080FF" d="M13.497 26.999C6.055 26.999 0 20.943 0 13.5 0 6.057 6.055.001 13.497.001c7.445 0 13.502 6.056 13.502 13.499 0 7.443-6.057 13.499-13.502 13.499Zm0-25.759c-6.759 0-12.258 5.5-12.258 12.26 0 6.761 5.499 12.26 12.258 12.26 6.762 0 12.264-5.499 12.264-12.26 0-6.76-5.502-12.26-12.264-12.26Zm-.134 12.438-8.609 5.284 8.745-15.15 8.746 15.15-8.882-5.284Z"/>\
    </svg>')
      center/100% no-repeat;
  }

  :global(.n-timeline-item-timeline__line) {
    --n-line-color: #409eff;
    width: 1px !important;
  }
}
</style>
