<template>
  <div :class="$style.ComBoxB">
    <section>
      <div :class="$style.title">
        <n-flex class="flex-1 pr-[35px]" justify="space-between" @click.self="setExpand(!expand)">
          <span>{{ title }}</span>
          <slot name="extra"></slot>
        </n-flex>
        <n-button v-if="enableExpand" text @click.stop="setExpand(!expand)">
          <template #icon>
            <n-icon :size="26" :class="[$style.arrow, !expand && $style.rotate]"><IconArrow class="w-[20px]" /></n-icon>
          </template>
        </n-button>
      </div>
      <div :class="[$style.content, !expand && $style.collapsed]">
        <div :class="$style.inner">
          <template v-if="!isEmpty">
            <slot />
          </template>
          <div v-else :class="$style.empty">
            <img :src="noDataImg" alt="无数据" />
            <p class="text-[#6B7B99] text-[14px]">暂无数据</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { MdExpandLess as IconArrow } from '@kalimahapps/vue-icons';
import { useState } from '@/common/hooks/useState.ts';
import noDataImg from './assets/bg-box-b-no.png';

const props = defineProps({
  title: String,
  enableExpand: {
    type: Boolean,
    default: true,
  },
  defaultExpand: {
    type: Boolean,
    default: true,
  },
  // 判定是否为空数据 用于显示空图 必传
  isEmpty: Boolean,
});

const [expand, setExpand] = useState(props.defaultExpand);

defineOptions({ name: 'ComBoxB' });
</script>

<style module lang="scss">
.ComBoxB {
  --anim-duration: 220ms;
  --anim-ease: cubic-bezier(0.2, 0, 0, 1);
  section {
    background: rgba(24, 40, 71, 0.45);
    border: 1px solid #48669b;
    border-radius: 2px;
    padding: 10px 20px;

    .title {
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;

      .arrow {
        transform-origin: center;
        transition: transform var(--anim-duration) var(--anim-ease);
      }

      .rotate {
        transform: rotate(180deg);
      }
    }

    .content {
      display: grid;
      grid-template-rows: 1fr;
      will-change: grid-template-rows;
      transition: grid-template-rows var(--anim-duration) var(--anim-ease);
      overflow: hidden;
    }

    .collapsed {
      grid-template-rows: 0fr;
    }

    .inner {
      min-height: 0;
      overflow: hidden;
      opacity: 1;
      transition: opacity var(--anim-duration) ease-out;
    }

    .collapsed .inner {
      opacity: 0;
    }

    .empty {
      display: grid;
      place-items: center;
      padding: 20px 0;

      img {
        width: 160px;
        opacity: 0.6;
      }
    }

    p {
      font-size: 14px;
      line-height: 30px;

      &:last-child {
        padding-bottom: 5px;
      }
    }
  }
}
</style>
