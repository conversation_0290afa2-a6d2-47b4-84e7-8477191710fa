<template>
  <div :class="$style['video-player-container']">
    <div ref="videoRef" class="w-full h-full"></div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import Artplayer from 'artplayer';
import { type Option } from 'artplayer/types/option';
import forward from './assets/seek-forward.png';
import backward from './assets/seek-backward.png';

const props = defineProps({
  url: {
    type: String,
    default: () => '',
  },
});
const videoRef = ref();
const art = ref<any>(null);
const icons = {
  forward,
  backward,
};

function initPlayer() {
  const option: Option = {
    container: videoRef.value,
    url: props.url,
    volume: 1, // 音量100
    fullscreenWeb: true, // 网页全屏
    fullscreen: true, // 全屏
    screenshot: true, // 截屏
    controls: [
      {
        name: 'forward',
        index: 11,
        position: 'left',
        html: `<img src="${icons.forward}" alt="" style="width: 18px; height: 18px;"/>`,
        tooltip: '快进',
        click: function () {
          art.value.video.currentTime += 5;
        },
      },
      {
        name: 'backward',
        index: 11,
        position: 'left',
        html: `<img src="${icons.backward}" alt="" style="width: 18px; height: 18px;" />`,
        tooltip: '后退',
        click: function () {
          art.value.video.currentTime -= 5;
        },
      },
      {
        name: 'playbackRate',
        position: 'right',
        html: '1.0x',
        selector: [
          {
            html: '<span data-value="2">2.0x</span>',
          },
          {
            html: '<span data-value="1.5">1.5x</span>',
          },
          {
            default: true,
            html: '<span data-value="1">1.0x</span>',
          },
          {
            html: '<span data-value="0.5">0.5x</span>',
          },
        ],
        onSelect: function (item, $dom) {
          art.value.video.playbackRate = Number($dom.querySelector('span')?.dataset.value);
          console.info(item, $dom);
          return item.html;
        },
      },
    ],
    moreVideoAttr: {
      crossOrigin: 'Anonymous', // 解决跨越无法截图
    },
  };
  art.value = new Artplayer(option);
}

watch(
  () => props.url,
  (val: string) => {
    art.value?.destroy();
    if (val) initPlayer();
  }
);

onMounted(() => initPlayer());

onBeforeUnmount(() => art.value?.destroy());

defineComponent({ name: 'VideoPlayerComp' });
</script>

<style module lang="scss">
.video-player-container {
  @apply w-full h-full;
}
</style>
