<template>
  <div :class="$style['header']">
    <span v-if="title">{{ title }}</span>
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
interface Props {
  title?: string;
}

defineProps<Props>();

defineOptions({ name: 'ComHeaderD' });
</script>

<style module lang="scss">
.header {
  padding: 0 20px 0 35px;
  margin: 15px 0;
  background: url('./assets/bg-com-header-d.png') 0 0 no-repeat;

  height: 38px;
  line-height: 38px;
  font-size: 20px;
  font-family: 'youshe', serif;
  color: #ffffff;
}
</style>
