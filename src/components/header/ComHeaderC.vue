<template>
  <div :class="$style['header']">
    <div :class="$style.shu"></div>
    <div :class="$style.title">
      <span>{{ title }}</span>
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
}

withDefaults(defineProps<Props>(), {
  title: '',
});

defineOptions({ name: 'ComHeaderC' });
</script>

<style module lang="scss">
.header {
  display: flex;
  align-items: center;
  padding: 5px 0;
  position: relative;

  .shu {
    width: 3px;
    height: 16px;
    background: var(--skin-c1);
    border-radius: 2px 2px 2px 2px;
  }

  .title {
    width: 100%;
    font-weight: 700;
    font-size: 16px;
    color: var(--skin-t2);
    padding: 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
