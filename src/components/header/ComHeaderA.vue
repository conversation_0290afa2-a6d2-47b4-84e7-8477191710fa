<template>
  <div :class="$style['header']">
    <img :src="activeIcon ? activePic : normalPic" alt="" />
    <span
      :title="title"
      :class="{ [$style.title]: !normalStyle, [$style.activeColor]: activeColor, [$style.normalStyle]: normalStyle }"
    >
      <slot name="left">{{ title }}</slot>
    </span>
    <div :class="$style['right']">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import normalPicPath from './assets/icon-title-arrow.png';
import activePicPath from './assets/icon-title-arrow3.png';

interface Props {
  title: string;
  activeColor?: boolean;
  activeIcon?: boolean;
  normalStyle?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  activeColor: false,
  activeIcon: false,
  normalStyle: false,
});

const normalPic = computed(() => {
  return normalPicPath;
});

const activePic = computed(() => {
  return activePicPath;
});

defineOptions({ name: 'ComHeaderA' });
</script>

<style module lang="scss">
.header {
  @apply h-[22px] flex items-center relative;
  img {
    @apply w-[17px] h-[12px] mr-[10px];
  }
}

.title {
  @apply text-[22px] text-[#0249B1] truncate;
  font-family: 'youshe', serif;
  &.activeColor {
    @apply text-[#1f2225];
  }
}

.right {
  @apply ml-auto;
}

.normalStyle {
  @apply text-[16px] text-[#1F2225] font-[700];
}
</style>
