<template>
  <div :class="$style['equipmentListWrap']">
    <template v-if="equipmentList && equipmentList.length">
      <div :class="$style['equipmentList']" v-for="(item, index) in equipmentList" :key="index">
        <div :class="$style['header']">
          <p>{{ treeName }}</p>
          <n-button @click="goDetail(item)" width="60" height="32" color="#527CFF" text-color="white" type="info">
            更多
          </n-button>
        </div>
        <div :class="$style['content']">
          <div :class="$style['room']">
            <div :class="$style['roomHeader']">
              <p>防爆充电房</p>
              <!-- <img width="84" height="76" src="@/assets/polling/_fly_a.png" v-if="item.id === '1'" />
              <img width="84" height="76" src="@/assets/polling/_house.png" v-if="item.id === '2'" /> -->
              <img width="84" height="76" src="@/assets/polling/_house.png" />
            </div>
            <div :class="$style['info']">
              <p></p>
              <div>
                设备名称：<span :class="$style['eqName']" :title="item.deviceName">{{
                  item.devicePowerVO.deviceName
                }}</span>
              </div>
              <!-- <div>
                设备型号：<span :class="$style['eqType']" :title="item.category">{{ item.category }}</span>
              </div> -->
              <div>
                设备编号：<span :class="$style['eqNum']" :title="item.category">{{ item.devicePowerVO.deviceId }}</span>
              </div>
            </div>
          </div>
          <div :class="$style['room']">
            <div :class="$style['roomHeader']">
              <p>防爆机器人</p>
              <!-- <img width="84" height="76" src="@/assets/polling/_fly_b.png" v-if="item.id === '1'" />
              <img width="84" height="76" src="@/assets/polling/_robot.png" v-if="item.id === '2'" /> -->
              <img width="84" height="76" src="@/assets/polling/_robot.png" />
            </div>
            <div :class="$style['info']">
              <p :style="{ background: colors[1] }">{{ robotStatusType[item.robotStatus] }}</p>
              <div>
                设备名称：<span :class="$style['eqName']" :title="item.category">{{ item.deviceName }}</span>
              </div>
              <!-- <div>
                设备型号：<span :class="$style['eqType']" :title="item.category">{{ item.category }}</span>
              </div> -->
              <div>
                设备编号：<span :class="$style['eqNum']" :title="item.category">{{ item.deviceName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div :class="$style['empty__box']" v-else>
      <Empty title="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import type { ICardBItem } from './type';
import Empty from '@/components/empty/index.vue';
const robotStatusType = {
  robotOnLine: '在线',
  robotOffLine: '离线',
  faultRobot: '故障',
  chargingRobot: '充电中',
  meetRobot: '遇障',
  standbyRobot: '待机中',
  patrolRobot: '巡检中',
  pauseRobot: '暂停',
} as any;
const roomStatus = {
  DEVICE_STATUS_ON: '在线',
  DEVICE_STATUS_OFF: '离线',
  DEVICE_STATUS_START: '初装',
  DEVICE_STATUS_END: '撤机',
  DEVICE_STATUS_FAULT: '故障',
} as any;

const emits = defineEmits(['gotoDetail']);
const props = defineProps({
  equipmentList: {
    type: Object,
    default: () => {},
  },
  colors: {
    type: Array as PropType<string[]>,
    default: () => ['#F18D00', '#00B578'],
  },
  treeName: {
    type: String,
    default: '',
  },
});
function goDetail(value: any) {
  if (value == 'robotOffLine') {
    console.log('设备已离线');
  } else {
    console.log('前往设备详情');
    emits('gotoDetail', value);
  }
}
</script>

<style module lang="scss">
.equipmentListWrap {
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  .equipmentList {
    width: 550px;
    background: white;
    border: 1px dashed #ccc;
    margin: 10px;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border-bottom: 1px dashed #ccc;
      p {
        font-weight: 700;
        font-size: 16px;
        color: #222222;
      }
    }

    .content {
      display: flex;
      justify-content: space-between;
      .room {
        display: flex;
        width: 275px;
        padding: 16px 17px 17px 12px;
        border-right: 1px dashed #ccc;
        &:nth-child(even) {
          border-right: none;
        }
        .roomHeader {
          p {
            font-weight: bolder;
            font-size: 16px;
            color: #222222;
            margin-bottom: 22px;
          }
        }
        .info {
          color: #222222;
          margin-left: 12px;
          p {
            font-weight: bolder;
            color: white;
            padding: 2px 6px;
            margin-bottom: 22px;
            border-radius: 4px;
            // width: 60px;
            display: inline-block;
          }
          div {
            margin-bottom: 12px;
            display: flex;
            white-space: nowrap;
          }
          .eqName {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .eqNum {
            width: 79px;
            font-weight: 400;
          }
          .eqType {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}
.empty__box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 500px;
}
</style>
