<template>
  <n-drawer class="com-detail-drawer" v-model:show="showModal">
    <n-card
      style="width: 778px; height: 100vh; position: fixed; top: 0px; right: 0px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
      :class="$style['n-card']"
    >
      <div :class="$style['header']">
        <div :class="$style['left']">
          <img style="width: 18px; height: auto; margin-right: 8px" :src="detailTop" />
          <p style="font-weight: 600; font-size: 16px; color: #18191a">设备详情</p>
        </div>
        <div :class="$style['btn-close']" @click="handleClose">
          <IconClose :class="$style['icon']" />
        </div>
      </div>

      <div :class="$style['detailListWrap']">
        <div :class="$style['detailHeader']">
          <div :class="$style['left']">
            <p>防爆充电房</p>
            <div :class="$style['status']">
              <!-- <div style="background-color: #00b578" :class="$style['statusItem']">
                {{ roomStatus[deviceDetailsList.devicePowerVO.status] }}
              </div> -->
              <!-- <div :class="$style['lines']"></div>
              <div :class="$style['statusItem']">当前正常</div> -->
            </div>
          </div>
          <!-- <div :class="$style['right']">
            <p>用户体验改善计划</p>
            <CdChevronRight />
          </div> -->
        </div>
        <div :class="$style['detailList']">
          <div :class="$style['left']">
            <img width="118" height="118" src="@/assets/polling/_house.png" />
            <div :class="$style['status']">
              <!-- <span :class="$style['statusItem']">Dock 2 V000.05.0904</span> -->
              <span :class="$style['statusItem']">设备名称：{{ deviceDetailsList.devicePowerVO.deviceName }}</span>
              <span :class="$style['statusItem']">设备编号：{{ deviceDetailsList.devicePowerVO.deviceId }}</span>
            </div>
            <!-- <div :class="$style['bottom']">  
              <p>保养服务</p>
              <div>128天/1019架次</div>
            </div>
            <div :class="$style['bottom']">
              <p>行业无忧</p>
              <div>未绑定</div>
            </div> -->
          </div>
          <div :class="$style['right']">
            <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.fans] }}</p>
              <div>风扇</div>
            </div>
            <!-- <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.room_CLOSE] }}</p>
              <div>关门驱动器</div>
            </div> -->
            <!-- <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.bottom] }}</p>
              <div>下侧感应器</div>
            </div> -->
            <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.light] }}</p>
              <div>照明灯</div>
            </div>
            <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.air_CONDITIONING] }}</p>
              <div>空调</div>
            </div>
            <!-- <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.top] }}</p>
              <div>上侧感应器</div>
            </div> -->
            <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.heater] }}</p>
              <div>加热器</div>
            </div>
            <div :class="$style['item']">
              <p>{{ chargingRoomFilter[deviceDetailsList.devicePowerVO.charging_PILE] }}</p>
              <div>充电桩</div>
            </div>
            <!-- <div :class="$style['item']">
              <p>54天</p>
              <p>{{weatherList.temperature}}</p>
              <div>温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <p>{{weatherList.humidity}}</p>
              <div>湿度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <p>{{weatherList.windDirection}}</p>
              <div>风速风向</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <p>{{weatherList.pressure}}</p>
              <div>气压</div>
            </div> -->
            <!-- <div :class="$style['item']">
              <p>54天</p>
              <div>舱内温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内湿度</div>
            </div> -->
          </div>
        </div>
        <div :class="$style['detailHeader']">
          <div :class="$style['left']">
            <p>防爆机器人</p>
            <div :class="$style['status']">
              <div style="background-color: #00b578" :class="$style['statusItem']">
                {{ robotStatusType[robotList.robotStatus] }}
              </div>
              <!-- <div :class="$style['lines']"></div>
              <div :class="$style['statusItem']">当前正常</div> -->
            </div>
          </div>
          <!-- <div :class="$style['right']">
            <p>用户体验改善计划</p>
            <CdChevronRight />
          </div> -->
        </div>
        <div :class="$style['detailList']">
          <div :class="$style['left']">
            <!-- <n-image width="118" height="118" src="../../src/assets/noData.png" /> -->
            <img width="118" height="118" src="@/assets/polling/_robot.png" />
            <div :class="$style['status']">
              <!-- <span :class="$style['statusItem']">Dock 2 V000.05.0904</span> -->
              <span :class="$style['statusItem']">设备名称：{{ deviceDetailsList.deviceName || '--' }}</span>
              <span :class="$style['statusItem']">设备编号：{{ deviceDetailsList.deviceName || '--' }}</span>
            </div>
            <!-- <div :class="$style['bottom']">
              <p>保养服务</p>
              <div>128天/1019架次</div>
            </div>
            <div :class="$style['bottom']">
              <p>行业无忧</p>
              <div>未绑定</div>
            </div> -->
          </div>
          <div :class="$style['right']">
            <div :class="$style['item']">
              <p>{{ robotList.odometer }}</p>
              <div>总行驶里程</div>
            </div>
            <div :class="$style['item']">
              <p>{{ robotList.currentTaskTime }}</p>
              <div>本次运行时间</div>
            </div>
            <div :class="$style['item']">
              <p>{{ robotList.currentBattery ? robotList.currentBattery + '%' : '--' }}</p>
              <div>当前电量</div>
            </div>
            <div :class="$style['item']">
              <p :style="{ color: errorColor(robotList.pickupStatus) }">
                {{ filterStatus(robotList.gasInfoStatus, '1') }}
              </p>
              <div>气体传感器</div>
            </div>
            <div :class="$style['item']">
              <p>{{ filterStatus(robotList.proximitySwitch, '2') }}</p>
              <div>接近开关</div>
            </div>
            <div :class="$style['item']">
              <p :style="{ color: errorColor(robotList.pickupStatus) }">
                {{ filterStatus(robotList.infraeredInfoStatus, '1') }}
              </p>
              <div>红外摄像头</div>
            </div>
            <div :class="$style['item']">
              <p :style="{ color: errorColor(robotList.pickupStatus) }">
                {{ filterStatus(robotList.visibleCameraInfoStatus, '1') }}
              </p>
              <div>高清摄像头</div>
            </div>
            <div :class="$style['item']">
              <p>{{ filterStatus(robotList.frontBumperStrip, '3') }}</p>
              <div>前防撞条</div>
            </div>
            <div :class="$style['item']">
              <p>{{ filterStatus(robotList.afterBumperStrip, '3') }}</p>
              <div>后防撞条</div>
            </div>
            <div :class="$style['item']">
              <p :style="{ color: errorColor(robotList.pickupStatus) }">
                {{ filterStatus(robotList.amplifierStatus, '1') }}
              </p>
              <div>扬声器</div>
            </div>
            <div :class="$style['item']">
              <p :style="{ color: errorColor(robotList.pickupStatus) }">
                {{ filterStatus(robotList.pickupStatus, '1') }}
              </p>
              <div>拾音器</div>
            </div>
          </div>
        </div>
      </div>
      <!-- <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <n-button
            type="tertiary"
            color="#dcdfe6"
            text
            @click="handleClose"
            style="margin-right: 15px; width: 88px; color: #606266; border: 1px solid #dcdfe6; border-radius: 2px"
            >取消</n-button
          >
          <n-button type="primary" style="width: 88px">编辑</n-button>
        </div>
      </template> -->
    </n-card>
  </n-drawer>
</template>

<script lang="ts" setup>
import { PropType, ref, watch, computed } from 'vue';
import { CdChevronRight } from '@kalimahapps/vue-icons';
import type { DeviceDetailType } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import detailTop from './assets/detailTop.png';
import close from './assets/close.png';
import { DataTableColumns, NButton, NTag } from 'naive-ui';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { postDeviceListApi, postDeviceDetailApi, postDevicePowerlApi } from '@/views/robot/equipment/fetchData.ts';
import { h } from 'vue';
const [loading, run] = useAutoLoading(false);
let deviceID = ref('');
let showModal = ref(false);
const DevicePowerlPrams = ref({
  //设备ID必填
  deviceId: '110728970',
});
const chargingRoomList = ref<any>();
const robotList = ref<any>();
const weatherList = ref<any>();
const navStatusFilter = ['导航中', '导航完成', '偏离车道线', '规划失败', '机器人遇障'];
const chargingRoomFilter = ['关', '开'];
const roomStatus = {
  DEVICE_STATUS_ON: '在线',
  DEVICE_STATUS_OFF: '离线',
  DEVICE_STATUS_START: '初装',
  DEVICE_STATUS_END: '撤机',
  DEVICE_STATUS_FAULT: '故障',
} as any;
const robotStatusType = {
  robotOnLine: '在线',
  robotOffLine: '离线',
  faultRobot: '故障',
  chargingRobot: '充电中',
  meetRobot: '遇障',
  standbyRobot: '待机中',
  patrolRobot: '巡检中',
  pauseRobot: '暂停',
} as any;
const props = defineProps({
  deviceDetailsList: {
    type: Object,
    default: () => {},
    // {
    //   title: '采气二厂/延919采气大队/6号集气站',
    //   eqName: '6号集气站充电房',
    //   eqType: '七腾防爆充电房',
    //   eqNum: '3289428490324…',
    //   eqName2: '6号集气站巡检...',
    //   eqType2: 'SREX-SGLS-02',
    //   eqNum2: '3289428490324…',
    // },
  },
});
const filterStatus = computed(() => {
  return (val: string, type: string) => {
    if ((val == '2' && type == '1') || (val == '2' && type == '2')) {
      return '正常';
    }
    if ((val == '1' && type == '1') || (val == '1' && type == '3')) {
      return '异常';
    }
    if (val == '1' && type == '2') {
      return '触发';
    }
    if (val == '2' && type == '3') {
      return '未触发';
    }
    if (val == '0') {
      return '服务未调通';
    }
  };
});
// const navStatusFilter = computed(() => {
//   return (val: string) => {
//     if (val == '0') return '导航中';
//     if (val == '1') return '导航完成';
//     if (val == '2') return '偏离车道线';
//     if (val == '3') return '规划失败';
//     if (val == '4') return '机器人遇障';
//   };
// });

// const chargingRoomFilter = computed(()=>{
//   return(val:string)=>{
//     if(val=='1') return '已开启'
//     if(val=='2') return '未开启'
//   }
// })
// const dataTransform = computed(() => {
//   return (minutes:any)=>{
//     if(minutes){
//       let days = Math.floor(minutes / 1440); // 1440 minutes in a day
//       let hours = Math.floor((minutes % 1440) / 60); // 60 minutes in an hour
//       let remainingMinutes = minutes % 60; // remaining minutes
//       if(minutes<60){
//         return `${minutes}分钟`
//       }
//       if(minutes==60){
//         return `${hours}小时`
//       }
//       if(minutes>60 && minutes < 1440){
//         return `${hours}小时${remainingMinutes}分钟`
//       }
//       if(minutes==1440){
//         return `${days}天`
//       }
//       if(minutes > 1440){
//         return `${days}天${hours}小时${remainingMinutes}分钟`
//       }
//     }else{
//       return '--'
//     }
//   }
// })
const errorColor = computed(() => {
  return (val: string) => {
    if (val == '1') return '#fa5151';
    else return '';
  };
});
function init(val: any, depid: any) {
  console.log('initinit', JSON.stringify(val));
  showModal.value = true;
  getRobotList(val.deviceId, depid);
}
function getRobotList(deviceid: any, depid: any) {
  console.log(deviceid);
  run(postDeviceDetailApi({ id: deviceid, deptId: depid })).then((res) => {
    console.log('设备详情数据', JSON.stringify(res));
    robotList.value = res.data;
  });
}
function handleClose() {
  showModal.value = false;
}
function getRobotInfo(val: any) {
  console.log('调用详情了', val);
}
defineExpose({
  init,
  getRobotInfo,
});
defineOptions({ name: 'ComCardCRef' });
</script>

<style module lang="scss">
.n-card :global(.n-card__content) {
  padding: 0 !important;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    padding: 15px 25px;
    .left {
      display: flex;
      align-items: center;
      p {
        margin-left: 8px;
      }
    }
    .btn-close {
      width: 50px;
      height: 50px;
      position: absolute;
      top: 0;
      right: 5px;
      transition: transform 0.3s;
      cursor: pointer;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover {
        opacity: 0.9;
        transform: rotate3d(0, 0, 1, -90deg);
      }
      &:active {
        opacity: 0.95;
      }

      .icon {
        font-size: 20px;
        color: #7a7a7a;
      }
    }
  }
  .detailListWrap {
    .detailHeader {
      display: flex;
      justify-content: space-between;
      margin: 24px 24px 0 24px;
      .left {
        display: flex;
        align-items: center;
        p {
          font-weight: 700;
          font-size: 20px;
          color: #222222;
        }
        .status {
          display: flex;
          margin-left: 16px;
          .statusItem {
            background: #00b578;
            color: white;
            padding: 4px;
            border-radius: 5px;
            padding: 5px;
          }
          .lines {
            height: 30px;
            width: 2px;
            background: #ebeef5;
            margin: 0 12px;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        p {
          color: #527cff;
        }
      }
    }
    .detailList {
      display: flex;
      justify-content: space-between;
      padding: 15px 24px 24px 24px;
      .left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 250px;
        .status {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding-bottom: 13px;
          // border-bottom: 1px solid #e6e9f0;
          .statusItem {
            font-size: 14px;
            color: #666666;
            line-height: 22px;
            margin-bottom: 4px;
          }
        }
        .bottom {
          width: 250px;
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #e6e9f0;
          p {
            font-weight: bold;
            font-size: 14px;
            color: #222222;
            line-height: 22px;
          }
          div {
            font-size: 14px;
            color: #666666;
            line-height: 22px;
          }
        }
      }
      .right {
        width: 456px;
        background: #f5f6fa;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 113px;
          height: 75px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          p {
            font-size: 14px;
            color: #222222;
            line-height: 22px;
          }
          div {
            margin-top: 4px;
            font-size: 14px;
            color: #666666;
            line-height: 22px;
          }
        }
      }
    }
  }
}
</style>
