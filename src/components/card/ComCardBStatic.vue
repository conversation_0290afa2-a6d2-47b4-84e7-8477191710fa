<template>
  <div :class="$style['equipmentListWrap']">
    <div :class="$style['equipmentList']" v-for="(item, index) in equipmentList" :key="index">
      <div :class="$style['header']">
        <p>{{ treeName }}</p>
        <n-button
          @click="goDetail(item.robotStatus)"
          width="60"
          height="32"
          color="#527CFF"
          text-color="white"
          type="info"
        >
          更多
        </n-button>
      </div>
      <div :class="$style['content']">
        <div :class="$style['room']">
          <div :class="$style['roomHeader']">
            <p>防爆充电房</p>
            <!-- <n-image width="84" height="76" src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg" /> -->
            <img width="84" height="76" src="@/assets/polling/_fly_a.png" v-if="item.id === '1'" />
            <img width="84" height="76" src="@/assets/polling/_house.png" v-if="item.id === '2'" />
          </div>
          <div :class="$style['info']">
            <p :style="{ background: colors[0] }">充电中</p>
            <div>
              设备名称：<span :class="$style['eqName']" :title="item.deviceName">{{ item.deviceName }}</span>
            </div>
            <div>
              设备型号：<span :class="$style['eqType']" :title="item.category">{{ item.category }}</span>
            </div>
            <div>
              设备编号：<span :class="$style['eqNum']" :title="item.category">{{ item.deviceId }}</span>
            </div>
          </div>
        </div>
        <div :class="$style['room']">
          <div :class="$style['roomHeader']">
            <p>防爆机器人</p>
            <img width="84" height="76" src="@/assets/polling/_fly_b.png" v-if="item.id === '1'" />
            <img width="84" height="76" src="@/assets/polling/_robot.png" v-if="item.id === '2'" />
          </div>
          <div :class="$style['info']">
            <p :style="{ background: colors[1] }">巡检中</p>
            <div>
              设备名称：<span :class="$style['eqName']" :title="item.category">{{ item.deviceName }}</span>
            </div>
            <div>
              设备型号：<span :class="$style['eqType']" :title="item.category">{{ item.category }}</span>
            </div>
            <div>
              设备编号：<span :class="$style['eqNum']" :title="item.category">{{ item.deviceId }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref } from 'vue';
import type { ICardBItem } from './type';
const emits = defineEmits(['gotoDetail']);
const props = defineProps({
  equipmentList: {
    type: Array as PropType<ICardBItem[]>,
    default: () => [],
  },
  colors: {
    type: Array as PropType<string[]>,
    default: () => ['#F18D00', '#527CFF'],
  },
  treeName: {
    type: String,
    default: '',
  },
});
function goDetail(value: string) {
  if (value == 'robotOffLine') {
    console.log('设备已离线');
  } else {
    console.log('前往设备详情');
    emits('gotoDetail', value);
  }
}
</script>

<style module lang="scss">
.equipmentListWrap {
  border-radius: 4px;
  display: flex;
  padding: 16px 24px 0 24px;
  .equipmentList {
    width: 621px;
    padding: 16px 12px 14px 12px;
    background: white;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 9px;
      p {
        font-weight: 700;
        font-size: 16px;
        color: #222222;
      }
    }

    .content {
      display: flex;
      justify-content: space-between;
      .room {
        display: flex;
        width: 294px;
        background: #edf0f7;
        padding: 16px 17px 17px 12px;
        .roomHeader {
          p {
            font-weight: bolder;
            font-size: 16px;
            color: #222222;
            margin-bottom: 22px;
          }
        }
        .info {
          color: #222222;
          margin-left: 12px;
          p {
            font-weight: bolder;
            color: white;
            padding: 2px 6px;
            margin-bottom: 22px;
            border-radius: 4px;
            // width: 60px;
            display: inline-block;
          }
          div {
            margin-bottom: 12px;
            display: flex;
          }
          .eqName {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .eqNum {
            width: 79px;
            font-weight: 400;
          }
          .eqType {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  .equipmentList:nth-child(odd) {
    margin-right: 16px;
  }
}
</style>
