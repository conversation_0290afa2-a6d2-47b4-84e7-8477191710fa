export interface ICardAItem {
  label: string;
  value: string | number;
  id: string | number;
  onlineState: string | number;
}
export interface ICardAProps {
  list: ICardAItem[];
  colors: string[];
}

export interface ICardBItem {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}
export interface UavICardBItem {
  uavId: string;
  model: string;
  uavName: string;
  uavPicture: string;
  uavFlvUrl: string;
  siteId: string;
  hiveModel: string;
  // siteId: string;
  // province: string;
  // areaName: string;
  // topparent: string;
  // robotStatus: string; // 'robotOffLine' 表示关闭
  // category: string; // 设备型号
  // remark1: string;
  // projectName: string;
  // projectId: string;
  // id: string;
  // name: string;
  // condition: string;
}
export interface ICardBProps {
  list: ICardBItem[];
  colors: string[];
}

export interface DeviceDetailType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}

export interface DevicePowerType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}

export interface DeviceDetailType2 {
  brand: string;
  createTime: string;
  fcsn: string;
  lastOnlineTime: string;
  model: string;
  siteId: string;
  siteMode: string;
  sn: string;
  uavFlvUrl: string;
  uavId: string;
  uavName: string;
  uavPicture: string;
  updateTime: string;
}

export interface DeviceRoomDetailType2 {
  uavId: string;
  model: string;
  uavName: string;
  uavPicture: string;
  uavFlvUrl: string;
  siteId: string;
  hiveModel: string;
}
