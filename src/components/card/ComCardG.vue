<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) in [
        { label: '计划（个）', value: data.allNum || 0 },
        { label: '进行中（个）', value: data.useNum || 0 },
        { label: '待开始（个）', value: data.waitOpenNum || 0 },
        { label: '已停用（个）', value: data.stopNum || 0 },
      ]"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`]]"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

function getData() {
  // todo
}

// init
getData();

defineOptions({ name: 'InspectTaskInfoCardTask' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(4, 290px);
  gap: 10px 20px;
  justify-content: center;
  .card {
    width: 290px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;

    &.bg-1 {
      background-image: url('./assets/bg1.png');
    }
    &.bg-2 {
      background-image: url('./assets/bg2.png');
    }
    &.bg-3 {
      background-image: url('./assets/bg3.png');
    }
    &.bg-4 {
      background-image: url('./assets/bg4.png');
    }

    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
