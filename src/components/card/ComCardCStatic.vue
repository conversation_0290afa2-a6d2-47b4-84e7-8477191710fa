<template>
  <n-drawer class="com-detail-drawer" v-model:show="showModal">
    <n-card
      style="width: 778px; height: 100vh; position: fixed; top: 0px; right: 0px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
      :class="$style['n-card']"
    >
      <div :class="$style['header']">
        <div :class="$style['left']">
          <img style="width: 18px; height: auto; margin-right: 8px" :src="detailTop" preview-disabled />
          <span style="font-weight: 600; font-size: 16px; color: #18191a">机场</span>
          <!-- <p>机场</p> -->
        </div>
        <div :class="$style['right']">
          <!-- <n-image width="35" height="35" :src="close" preview-disabled /> -->
          <div :class="$style['btn-close']" @click="handleClose">
            <IconClose class="icon" />
          </div>
        </div>
      </div>

      <div :class="$style['detailListWrap']">
        <div :class="$style['detailHeader']">
          <div :class="$style['left']">
            <p>机场</p>
            <div :class="$style['status']">
              <div :class="$style['statusItem']">设备空闲中</div>
              <div :class="$style['lines']"></div>
              <div :class="$style['statusItem']">当前正常</div>
            </div>
          </div>
          <div :class="$style['right']">
            <p>用户体验改善计划</p>
            <CdChevronRight />
          </div>
        </div>
        <div :class="$style['detailList']">
          <div :class="$style['left']">
            <!-- <n-image width="118" height="118" preview-disabled :src="deviceDetailRoomList.hiveId" /> -->
            <img width="118" height="118" src="@/assets/polling/_fly_a.png" />
            <div :class="$style['status']">
              <span :class="$style['statusItem']">{{ deviceDetailRoomList.hiveModel }}</span>
              <span :class="$style['statusItem']">机场名称：{{ deviceDetailRoomList.hiveName }}</span>
              <span :class="$style['statusItem']">主控SN：{{ deviceDetailsList.hiveId }}</span>
            </div>
            <div :class="$style['bottom']">
              <p>保养服务</p>
              <div>128天/1019架次</div>
            </div>
            <div :class="$style['bottom']">
              <p>行业无忧</p>
              <div>未绑定</div>
            </div>
          </div>
          <div :class="$style['right']">
            <!-- <div :class="$style['item']">
              <p>{{ deviceDetailsList.hiveId }}</p>
              <div>作业架次</div>
            </div> -->
            <div :class="$style['item']">
              <p>54天</p>
              <div>作业架次</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>机场搜星</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>标定状态</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>网络</div>
            </div>
            <div :class="$style['item']">
              <p style="font-weight: bold">54天</p>
              <div>备降点</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>机场搜星</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>标定状态</div>
            </div>
            <div :class="$style['item']">
              <!-- <p>{{ deviceDetailsList.chargerVoltage }}</p> -->
              <div>蓄电池电压</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>蓄电池温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内湿度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>蓄电池温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内湿度</div>
            </div>
          </div>
        </div>
        <div :class="$style['detailList']">
          <div :class="$style['left']">
            <img width="118" height="118" src="@/assets/polling/_fly_b.png" />
            <div :class="$style['status']">
              <span :class="$style['statusItem']">Dock 2 V000.05.0904</span>
              <span :class="$style['statusItem']">机场名称：测机库2</span>
              <span :class="$style['statusItem']">主控SN：--</span>
            </div>
            <div :class="$style['bottom']">
              <p>保养服务</p>
              <div>128天/1019架次</div>
            </div>
            <div :class="$style['bottom']">
              <p>行业无忧</p>
              <div>未绑定</div>
            </div>
          </div>
          <div :class="$style['right']">
            <!-- <div :class="$style['item']">
              <p>54天</p>
              <div>{{ devicePowerList.currentTaskTime }}</div>
            </div> -->
            <div :class="$style['item']">
              <p>54天</p>
              <div>作业架次</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>机场搜星</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>标定状态</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>网络</div>
            </div>
            <div :class="$style['item']">
              <p style="font-weight: bold">54天</p>
              <div>备降点</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>机场搜星</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>标定状态</div>
            </div>
            <div :class="$style['item']">
              <!-- <p>{{ devicePowerList.chargerVoltage }}</p> -->
              <div>蓄电池电压</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>蓄电池温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内湿度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>蓄电池温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内温度</div>
            </div>
            <div :class="$style['item']">
              <p>54天</p>
              <div>舱内湿度</div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <n-button
            type="tertiary"
            color="#dcdfe6"
            text
            @click="handleClose"
            style="margin-right: 15px; width: 88px; color: #606266; border: 1px solid #dcdfe6; border-radius: 2px"
            >取消</n-button
          >
          <n-button type="primary" style="width: 88px">编辑</n-button>
        </div>
      </template>
    </n-card>
  </n-drawer>
</template>

<script lang="ts" setup>
import { PropType, ref, watch } from 'vue';
import { CdChevronRight } from '@kalimahapps/vue-icons';
import type { DeviceDetailType2, DeviceRoomDetailType2 } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import detailTop from './assets/detailTop.png';
import close from './assets/close.png';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';

const [loading, run] = useAutoLoading(false);
let deviceID = ref('');
let showModal = ref(false);
const DevicePowerlPrams = ref({
  //设备ID必填
  deviceId: '110728970',
});
const props = defineProps({
  deviceDetailsList: {
    type: Array as PropType<DeviceDetailType2[]> as any,
    default: () => [],
  },
  deviceDetailRoomList: {
    type: Array as PropType<DeviceRoomDetailType2[]> as any,
    default: () => [],
  },
});
function init() {
  console.log('initinit');
  showModal.value = true;
}
function handleClose() {
  showModal.value = false;
}
defineExpose({
  init,
});
defineOptions({ name: 'ComCardCRef' });
</script>

<style module lang="scss">
.n-card :global(.n-card__content) {
  padding: 0 !important;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    padding: 15px 25px;
    .left {
      display: flex;
      align-items: center;
      p {
        margin-left: 8px;
        font-size: 18px;
      }
    }
  }
  .com-detail-drawer-footer {
    display: flex;
    justify-content: flex-end;
    .n-button {
      width: 88px;
      padding: 0px;
      &:first-child {
        color: #606266;
        border: 1px solid #dcdfe6;
        margin-right: 12px;
      }
    }
  }
  .btn-close {
    position: absolute;
    right: 16px;
    top: 20px;
    font-size: 20px;
    cursor: pointer;
  }
  .detailListWrap {
    .detailHeader {
      display: flex;
      justify-content: space-between;
      margin: 24px 24px 0 24px;
      .left {
        display: flex;
        align-items: center;
        p {
          font-weight: 700;
          font-size: 16px;
          color: #222222;
        }
        .status {
          display: flex;
          margin-left: 16px;
          .statusItem {
            background: #00b578;
            color: white;
            padding: 4px;
            border-radius: 5px;
          }
          .lines {
            height: 30px;
            width: 2px;
            background: #ebeef5;
            margin: 0 12px;
          }
        }
      }
      .right {
        display: flex;
        align-items: center;
        p {
          color: #527cff;
        }
      }
    }
    .detailList {
      display: flex;
      justify-content: space-between;
      padding: 15px 24px 24px 24px;
      .left {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 250px;
        .status {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-bottom: 13px;
          border-bottom: 1px solid #e6e9f0;
          .statusItem {
            font-size: 14px;
            color: #666666;
            line-height: 22px;
            margin-bottom: 4px;
          }
        }
        .bottom {
          width: 250px;
          display: flex;
          justify-content: space-between;
          padding: 10px 0;
          border-bottom: 1px solid #e6e9f0;
          p {
            font-weight: bold;
            font-size: 14px;
            color: #222222;
            line-height: 22px;
          }
          div {
            font-size: 14px;
            color: #666666;
            line-height: 22px;
          }
        }
      }
      .right {
        width: 456px;
        background: #f5f6fa;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 113px;
          height: 75px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          p {
            font-size: 14px;
            color: #222222;
            line-height: 22px;
          }
          div {
            margin-top: 4px;
            font-size: 14px;
            color: #666666;
            line-height: 22px;
          }
        }
      }
    }
  }
}
</style>
