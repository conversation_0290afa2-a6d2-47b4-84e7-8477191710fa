<template>
  <div :class="$style['cardListWrap']">
    <div :class="$style['background']">
      <div
        v-for="(item, index) in props.list"
        :key="index"
        :class="{ [$style['cardList']]: true, [$style['active']]: curAct === item.id }"
        @click="cardChange(item.id)"
      >
        <div :class="$style['value']">{{ item.value }}</div>
        <div :class="$style['label']">{{ item.label }}</div>
        <n-image :class="$style['icon']" width="84" preview-disabled height="76" :src="backImg" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import backImg from './assets/back.png';
import { PropType, ref, computed } from 'vue';

const emits = defineEmits(['action']);

interface PropsT {
  list: any[];
  colors?: string[];
  act?: string | number;
}

const props = withDefaults(defineProps<PropsT>(), {
  colors: () => ['#F18D00', '#527CFF'],
  act: 0,
});

const curAct = ref<string | number>(props.act);

function cardChange(v: string | number) {
  curAct.value = v;
  emits('action', {
    action: 'CARDCHANGE',
    data: { cardId: v },
  });
}
function imgClick() {
  // console.log(1);
}
defineOptions({ name: 'ComCardA' });
</script>

<style module lang="scss">
.cardListWrap {
  margin-top: 12px;
  margin-left: 18px;
  .background {
    display: flex;
    justify-content: space-around;
    .cardList {
      width: 239px;
      // flex: 1;
      height: 92px;
      padding: 16px 21px;
      margin-right: 16px;
      box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.18);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #c3cee0;
      position: relative;
      background: linear-gradient(180deg, #ffffff 0%, #dce2f5 100%);
      cursor: pointer;
      .value {
        font-weight: 500;
        font-size: 30px;
        line-height: 30px;
        color: #222222;
      }
      .label {
        white-space: nowrap;
        font-size: 16px;
        color: #4d4d4d;
        margin-top: 8px;
      }
      &:hover,
      &.active {
        background: linear-gradient(180deg, #6c90ff 0%, #4a76ff 99%);
        color: white;
        .value,
        .label {
          color: #fff;
        }
      }
      .icon {
        position: absolute;
        bottom: 10px;
        right: 12px;
        display: block;
        width: 42px;
        height: 48px;
        z-index: 10;
      }
    }
  }
}
</style>
