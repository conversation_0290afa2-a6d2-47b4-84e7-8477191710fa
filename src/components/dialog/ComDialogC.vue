<template>
  <n-modal
    ref="modalRef"
    :width="width"
    :height="height"
    preset="card"
    :mask-closable="false"
    :theme-overrides="overrideModalTheme()"
    :style="wrapStyle"
    :closable="false"
    style="--n-color-modal: var(--skin-bg5); --n-action-color: var(--skin-bg5)"
    header-style="padding: 0px; height: 38px; line-height: 22px; font-size: 18px;"
    :content-style="contentStyle"
    @after-enter="useDragModal($event)"
  >
    <template #header>
      <slot name="header">
        <div :class="$style.header">
          <span class="text-[18px]">{{ title }}</span>
          <slot name="header-r" />
          <div :class="$style['btn-close']" @click="handleClose"></div>
        </div>
      </slot>
    </template>

    <n-scrollbar content-class="w-full h-full" :style="{ maxHeight: contentMaxHeight }">
      <div class="w-full h-full">
        <slot></slot>
      </div>
    </n-scrollbar>

    <template #action>
      <slot name="action" />
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
// import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { overrideModalTheme } from './modalTheme';
import { useDragModal } from '@/components/dialog/dragPlugin';

defineOptions({ name: 'ComDialog' }); // 基础弹窗组件支持 n-modal 所有属性

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    default: 890,
  },
  height: {
    type: Number,
    default: 500,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  wrapStyle: {
    type: String,
  },
  contentStyle: {
    type: String,
    default: 'padding: 24px 24px 0 24px',
  },
});

const emits = defineEmits(['handleClose', 'handleNegative', 'handlePositive']);

const modalRef = ref();

const minH = computed(() => Math.max(props.height, 120));
const wrapStyle = computed(() => props.wrapStyle ?? `width: ${props.width}px; height: ${minH.value}px;`);

// 内容区域最大高度，计算公式：弹窗高 - title(-action) - padding
const contentMaxHeight = computed(() => {
  let h = minH.value - 38 - 56;
  return `${h}px`;
});

function close() {
  modalRef.value?.doUpdateShow(false);
}

function handleClose() {
  close();
  emits('handleClose');
}

// Expose fn
defineExpose({
  close,
});
</script>

<style module lang="scss">
.btn-close {
  position: absolute;
  right: 16px;
  width: 14px;
  height: 14px;
  background: url('./assets/close_icon.png') 0 0 no-repeat;
  cursor: pointer;
}

.header {
  position: relative;
  width: 100%;
  height: 38px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  line-height: 1.25;
  padding-left: 24px;
  padding-right: 66px;
  box-sizing: border-box;
  background: url('./assets/header_bg.png') 0 0 no-repeat;
  background-size: 100% 100%;
}
</style>
