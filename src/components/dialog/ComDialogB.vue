<template>
  <n-modal
    ref="modalRef"
    :width="width"
    :height="height"
    preset="card"
    :mask-closable="false"
    :theme-overrides="overrideModalTheme()"
    :style="wrapStyle"
    :closable="false"
    style="--n-color-modal: #fff; --n-action-color: #fff"
    header-style="padding: 0px; height: 52px; line-height: 24px; font-size: 16px;"
    :content-style="contentStyle"
    footer-style=""
    @after-enter="useDragModal($event)"
  >
    <template #header>
      <slot name="header">
        <div :class="$style.header">
          <img class="pl-[24px] text-[18px]" src="./assets/title-icon.png" alt="" />
          <span class="pl-[10px] text-[18px]">{{ title }}</span>
          <div :class="$style['btn-close']" @click="handleClose">
            <IconClose class="icon" />
          </div>
        </div>
      </slot>
    </template>

    <n-scrollbar :style="{ maxHeight: contentMaxHeight }">
      <div class="pb-[24px] pt-[24px]">
        <slot></slot>
      </div>
    </n-scrollbar>

    <template #action>
      <slot name="action" />
    </template>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { overrideModalTheme } from './modalTheme';
import { useDragModal } from '@/components/dialog/dragPlugin';

defineOptions({ name: 'ComDialog' }); // 基础弹窗组件支持 n-modal 所有属性

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    default: 890,
  },
  height: {
    type: Number,
    default: 58,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  wrapStyle: {
    type: String,
  },
  contentStyle: {
    type: String,
    default: 'padding: 0 24px',
  },
});

const emits = defineEmits(['handleClose', 'handleNegative', 'handlePositive']);

const modalRef = ref();

const minH = computed(() => Math.max(props.height, 120));
const wrapStyle = computed(() => props.wrapStyle ?? `width: ${props.width}px; height: ${minH.value}px;`);

// 内容区域最大高度，计算公式：弹窗高 - title(-action) - padding
const contentMaxHeight = computed(() => {
  let h = minH.value - 50 - 80;
  return `${h}px`;
});

function close() {
  modalRef.value?.doUpdateShow(false);
}

function handleClose() {
  close();
  emits('handleClose');
}

// Expose fn
defineExpose({
  close,
});
</script>

<style module lang="scss">
.btn-close {
  position: absolute;
  right: 16px;
  font-size: 20px;
  cursor: pointer;
}

.header {
  position: relative;
  height: 50px;
  display: flex;
  align-items: center;
  background-size: contain;
  border-bottom: 1px solid #e0e0e6;
}
</style>
