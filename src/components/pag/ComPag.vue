<template>
  <div :class="$style.comPag">
    <n-pagination
      :display-order="['pages', 'size-picker', 'quick-jumper']"
      :page="pagination.page"
      :item-count="pagination.itemCount"
      :page-sizes="pagination.pageSizes"
      :page-size="pagination.pageSize"
      :show-quick-jumper="pagination.showQuickJumper"
      :show-size-picker="pagination.showSizePicker"
      :on-update:page="onUpdatePage"
      :on-update:page-size="onUpdatePageSize"
    >
      <template #prefix="{ itemCount }"> 共 {{ itemCount }} 条 </template>
    </n-pagination>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
const emits = defineEmits(['change']);
type propsT = {
  page?: number;
  pageSize?: number;
  itemCount?: number;
  showSizePicker?: boolean;
  pageSizes?: number[];
  showQuickJumper?: boolean;
};

const props = withDefaults(defineProps<propsT>(), {
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: () => [10, 20, 50, 100],
  showQuickJumper: true,
});
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 100,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 40],
  showQuickJumper: true,
});
watch(
  props,
  (nV) => {
    pagination.value = { ...pagination.value, ...nV };
  },
  { immediate: true }
);
//更新页码
function onUpdatePage(page: number) {
  pagination.value.page = page;
  emits('change', { page: page, pageSize: pagination.value.pageSize });
}
//更新每页显示大小
function onUpdatePageSize(pageSize: number) {
  pagination.value.pageSize = pageSize;
  pagination.value.page = 1;
  emits('change', { page: pagination.value.page, pageSize: pageSize });
}

defineOptions({ name: 'ComPag' });
</script>

<style module lang="scss">
.comPag {
  display: flex;
  padding: 20px 24px;
  justify-content: flex-end;
}
</style>
