<template>
  <div v-if="mode === 'detail'">
    <n-image-group show-toolbar-tooltip>
      <n-space>
        <n-image v-for="(item, idx) of valueRef" :key="idx" :src="getFileURL(item.fjCflj)" width="100" />
      </n-space>
    </n-image-group>
  </div>
  <div v-if="mode === 'browse'" class="img-browse-wrap">
    <n-upload
      response-type="json"
      name="file"
      :action="actionURL"
      :default-file-list="defaultFileList"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :max="max"
      list-type="image-card"
      @preview="handlePreview"
    />
    <!-- :data="{ fileType: 'jpeg' }" -->
    <!-- <n-modal v-model:show="showModal" preset="card" style="width: 600px">
      <img :src="previewImageUrl" style="width: 100%" />
    </n-modal> -->
  </div>
  <div v-else class="w-full pt-[15px]">
    <n-upload
      response-type="json"
      name="file"
      :action="actionURL"
      :default-file-list="defaultFileList"
      :on-finish="handleUploadFinish"
      :on-update:file-list="handleUpdate"
      :on-before-upload="handleBeforeUpload"
      :accept="props.accept"
      :max="max"
    >
      <n-button type="primary">点击上传</n-button>
      <div class="mt-[10px] text-[#999999]" v-if="props.tips">{{ props.tips }}</div>
    </n-upload>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { UploadFileInfo } from 'naive-ui';
import { IUploadRes } from '@/components/upload/type';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { api } from '@/api';

defineOptions({ name: 'ImgUpload' });

interface IItem {
  fjMc: string;
  fjCflj: string;
  fjTywysbm: string;
}
interface Props {
  data: IItem[];
  mode?: string; // detail为展示图片 其他为上传图片
  max?: number; // 限制上传数量
  size?: number; // 限制文件大小
  accept?: string; // 文件类型
  tips?: string; // 上传提示
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  data: () => [],
  max: 6,
  accept: '.jpg,.jpeg,.png,.webp,.gif,.bmp,.svg',
});
const emits = defineEmits(['update', 'prev', 'updateBefore']);
const valueRef = computed<any[]>(() => props.data);
const actionURL = api.getUrl(api.type.intelligent, api.name.file.uploadFile);

const showModal = ref(false);
const previewImageUrl = ref('');
const previewFileList = ref<UploadFileInfo[]>([]);
const succseList = ref<any[]>([]);
function handlePreview(file: UploadFileInfo, detail: any) {
  console.log(file, previewFileList.value, succseList.value, '图片浏览');
  let index = 0;
  previewFileList.value.forEach((item, i) => {
    if (item.id === file.id) {
      index = i;
    }
  });
  emits('prev', index);
  //const { url } = file;
  // previewImageUrl.value = getFileURL(succseList.value[index]);
  // showModal.value = true;
}

// 默认文件列表(用于编辑时回显已上传文件)
const defaultFileList = computed<UploadFileInfo[]>(() => {
  const ret: UploadFileInfo[] = [];
  console.log(valueRef.value, 'valueRef.value====');
  for (const item of valueRef.value) {
    ret.push({
      id: item.fjTywysbm,
      name: item.fjMc,
      url: getFileURL(item.fjCflj),
      status: 'finished',
    });
    fileResMap.set(item.fjTywysbm, item);
  }
  return ret;
});
const fileResMap: Map<any, IUploadRes> = new Map(); // 上传结果map

// 上传前校验(文件大小、类型)
function handleBeforeUpload(options: { file: UploadFileInfo }) {
  emits('updateBefore');
  const { file } = options;
  if (!file.file) return false;
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1);
  if (!props.accept.includes(fileExt)) {
    $toast.error(`请上传 ${props.accept} 类型的文件!`);
    return false;
  } else if (props.size && file.file.size / 1024 / 1024 > props.size) {
    $toast.error(`文件不能超过 ${props.size} MB，请重新上传!`);
    return false;
  }
  return true;
}

// 上传完成
function handleUploadFinish(options: { file: UploadFileInfo; event?: ProgressEvent }) {
  const { file, event } = options;
  const data = (<any>event?.target)?.response?.data || {};
  fileResMap.set(file.id, data);
}

// 当文件数组改变时触发的回调函数
function handleUpdate(val: UploadFileInfo[]) {
  const finishedList = val.filter((item) => item.status === 'finished');
  const res: any[] = [];
  const data: any[] = [];
  finishedList.forEach((item) => {
    const _res = fileResMap.get(item.id) || null;
    res.push(_res);
    data.push(Object.assign(item, { res: _res }));
  });
  previewFileList.value = [...val];
  return emits('update', res, data);
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.fileService + filePath;
}
</script>

<style>
.img-browse-wrap {
  width: 100%;
  .n-upload {
    width: 100%;
    .n-upload-file-list {
      display: flex;
      width: 100%;
    }
    .n-upload-dragger {
      border-radius: 4px;
      border: 1px solid #dcdfe6;
    }
    .n-upload-dragger.n-upload-trigger--image-card {
      width: 98px;
      height: 98px;
    }
    .n-upload-trigger.n-upload-trigger--image-card .n-base-icon {
      color: #b7b9bf;
    }
  }
}
</style>
