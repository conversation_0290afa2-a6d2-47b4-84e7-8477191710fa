<template>
  <div class="p-[100px] flex justify-between">
    <div>
      <div>图片上传</div>
      <img-upload :data="imgData" @update="handleUpdate" :size="10" tips="仅支持xxx"></img-upload>
      <img-upload mode="detail" :data="imgRes"></img-upload>
    </div>
    <div>
      <div>视频上传</div>
      <video-upload :data="videoData" @update="handleUpdate" :size="20"></video-upload>
      <video-upload mode="detail" :data="videoRes"></video-upload>
    </div>
    <div>
      <div>文件上传</div>
      <file-upload :data="fileData" @update="handleUpdate" :size="1"></file-upload>
    </div>
  </div>
  <div class="p-[100px]">
    <div>hook: useUpload</div>
    <input type="file" @change="handleUpload" />
    <div>uploadProgress: {{ uploadProgress }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ImgUpload, VideoUpload, FileUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { useUpload } from '@/common/hooks/useUpload.ts';

const imgData = ref([]);
const imgRes = ref<any[]>([
  {
    fjTywysbm: 'd90cd068297f47ceb6fcff6b74364a2c',
    fjMc: '微信截图_20240314094015.png',
    fjCflj: '20240314/d90cd068297f47ceb6fcff6b74364a2c.png',
    fjDx: '656223',
    fjWjhz: '.png',
    fjLxdm: '0',
    ywLx: '',
    ywTywysbm: '',
    ywSj: '',
    cjsj: '2024-03-14 10:05:56',
    cjrWybs: '',
    gxsj: '',
    gxrWybs: '',
    bbh: '',
    scbs: '',
    sortNum: '',
    spSfxyzh: 0,
    spZhqlj: '',
    spZhzt: '',
    spZhKssj: '',
    spZhJssj: '',
    spZhBz: '',
    spZhSc: 0,
    spSzt: '',
  },
  {
    fjTywysbm: 'd90cd068297f47ceb6fcff6b74364a2c',
    fjMc: '微信截图_20240314094015.png',
    fjCflj: '20240314/d90cd068297f47ceb6fcff6b74364a2c.png',
    fjDx: '656223',
    fjWjhz: '.png',
    fjLxdm: '0',
    ywLx: '',
    ywTywysbm: '',
    ywSj: '',
    cjsj: '2024-03-14 10:05:56',
    cjrWybs: '',
    gxsj: '',
    gxrWybs: '',
    bbh: '',
    scbs: '',
    sortNum: '',
    spSfxyzh: 0,
    spZhqlj: '',
    spZhzt: '',
    spZhKssj: '',
    spZhJssj: '',
    spZhBz: '',
    spZhSc: 0,
    spSzt: '',
  },
]);

const videoData = ref([]);
const videoRes = ref<any[]>([
  {
    fjTywysbm: '694a1c3ac51e4862a2bce57b6987d725',
    fjMc: '复盘录像 - 2024-01-16 14_00_34.mp4',
    fjCflj: '20240314/694a1c3ac51e4862a2bce57b6987d725.mp4',
    fjDx: '1543191',
    fjWjhz: '.mp4',
    fjLxdm: '2',
    ywLx: '',
    ywTywysbm: '',
    ywSj: '',
    cjsj: '2024-03-14 10:41:44',
    cjrWybs: '',
    gxsj: '',
    gxrWybs: '',
    bbh: '',
    scbs: '',
    sortNum: '',
    spSfxyzh: 0,
    spZhqlj: '',
    spZhzt: '',
    spZhKssj: '',
    spZhJssj: '',
    spZhBz: '',
    spZhSc: 0,
    spSzt: '',
  },
  {
    fjTywysbm: '694a1c3ac51e4862a2bce57b6987d725',
    fjMc: '复盘录像 - 2024-01-16 14_00_34.mp4',
    fjCflj: '20240314/694a1c3ac51e4862a2bce57b6987d725.mp4',
    fjDx: '1543191',
    fjWjhz: '.mp4',
    fjLxdm: '2',
    ywLx: '',
    ywTywysbm: '',
    ywSj: '',
    cjsj: '2024-03-14 10:41:44',
    cjrWybs: '',
    gxsj: '',
    gxrWybs: '',
    bbh: '',
    scbs: '',
    sortNum: '',
    spSfxyzh: 0,
    spZhqlj: '',
    spZhzt: '',
    spZhKssj: '',
    spZhJssj: '',
    spZhBz: '',
    spZhSc: 0,
    spSzt: '',
  },
]);

const fileData = ref<any[]>([]);

const [uploadFn, uploading, uploadProgress] = useUpload();
function handleUpload(event: any) {
  const files = event.target.files[0]; //获取文件
  const formData = new FormData();
  formData.append('uploadfile', files);
  uploadFn(formData).then((res: any) => {
    console.log(res);
  });
}

// 上传回调
function handleUpdate(res: IUploadRes[]) {
  console.log(res);
  if (!res || !res.length) return;
  const ids = res.map((item) => item.fjTywysbm);
  console.log('ids:', ids.join());
}

defineOptions({ name: 'UploadDemo' });
</script>
<style scoped lang="scss">
:deep(.video-pre) {
  width: 300px;
}
</style>
