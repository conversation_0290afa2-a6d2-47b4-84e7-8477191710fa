<template>
  <n-empty :class="$style['empty']" :theme-overrides="overrideEmptyTheme()" :description="title">
    <template #icon>
      <img src="@/assets/noData.png" alt="no data" />
    </template>
  </n-empty>
</template>

<script lang="ts" setup>
import { overrideEmptyTheme } from './emptyTheme';
const props = defineProps({
  title: {
    type: String,
    default: '暂无数据',
  },
});

defineOptions({ name: 'ComEmpty' });
</script>

<style module lang="scss">
.empty {
  @apply h-full justify-center;
}
</style>
