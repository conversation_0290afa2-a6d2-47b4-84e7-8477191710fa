<template>
  <div :class="$style['radio-tab']">
    <n-tabs
      type="segment"
      animated
      size="large"
      :tab-class="$style['radio-tab-nav']"
      :default-value="tab"
      @update:value="handleUpdateValue"
    >
      <n-tab-pane v-for="tab in tabList" :key="tab.name" :name="tab.name" :tab="tab.label"></n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  tab: string;
  tabList: ITabItem[];
}

const props = defineProps<Props>();
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabD' });
</script>

<style module lang="scss">
.radio-tab {
  @apply bg-[#0249B1] h-[32px] rounded-[6px] overflow-hidden;
  :global(.n-tabs-rail) {
    @apply relative;
    padding: 4px !important;
    background: #0249b1;
  }
  :global(.n-tabs-tab) {
    height: 24px !important;
    padding: 0 10px;
  }
  :global(.n-tabs-capsule) {
    background: #daeaff !important;
  }
}

.radio-tab-nav {
  font-size: 14px !important;
  color: white !important;
  &:global(.n-tabs-tab--active) {
    color: #3460ab !important;
  }
}
</style>
