<template>
  <div :class="$style['radio-tab']">
    <n-tabs
      type="segment"
      animated
      size="large"
      :tab-class="$style['radio-tab-nav']"
      :default-value="tab"
      @update:value="handleUpdateValue"
    >
      <n-tab-pane v-for="tab in tabList" :key="tab.name" :name="tab.name" :tab="tab.label"></n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  tab: string;
  tabList: ITabItem[];
}

const props = defineProps<Props>();
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabB' });
</script>

<style module lang="scss">
.radio-tab {
  @apply bg-[#F4F9FF] h-[40px] rounded-t-[4px];
  :global(.n-tabs) {
    @apply pl-[0];
  }
  :global(.n-tabs-nav) {
    line-height: 1.7 !important;
  }
  :global(.n-tabs-rail) {
    @apply relative;
    padding: 0 !important;
    border: 1px solid #dcdfe6;
    background-color: transparent;
  }
  :global(.n-tabs-capsule) {
    background-color: #0249b1 !important;
  }
}

.radio-tab-nav {
  font-size: 14px !important;
  color: #303133 !important;
  &:global(.n-tabs-tab--active) {
    color: #fff !important;
  }
}
</style>
