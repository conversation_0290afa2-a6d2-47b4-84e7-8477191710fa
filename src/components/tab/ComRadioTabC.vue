<template>
  <ul :class="$style['radio-tab']">
    <li
      v-for="tabItem in tabList"
      :key="tabItem.name"
      :class="[$style['radio-tab-nav'], tab === tabItem.name ? $style['radio-tab-nav_active'] : '']"
      @click="handleUpdateValue(tabItem.name)"
    >
      <span>{{ tabItem.label }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  tab: string;
  tabList: ITabItem[];
}

const props = defineProps<Props>();
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabC' });
</script>

<style module lang="scss">
.radio-tab {
  @apply w-full flex bg-[#F4F9FF] h-[34px] cursor-pointer;
}

.radio-tab-nav {
  @apply flex-1 py-[9px] text-[14px] text-[#303133];
  text-align: center;
  border: 1px solid #dcdcdc;
  line-height: 1;
  user-select: none;

  &.radio-tab-nav_active {
    @apply text-[#0249B1];
    border: 1px solid #0249b1;
  }
}
</style>
