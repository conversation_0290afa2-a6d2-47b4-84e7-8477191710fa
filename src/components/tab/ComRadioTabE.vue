<template>
  <div :class="{ [$style.tabsCorner]: true, [$style.noLine]: isLine }">
    <div
      :class="[$style.tabsCornerItemWrap, item.value === tabAct ? $style.activeWrap : '']"
      v-for="(item, i) in props.tabList"
      :key="i"
      :style="{ 'z-index': 10 - i }"
      @click="tabChange(item.value)"
    >
      <div :class="{ [$style.tabsCornerItem]: true, [$style.tabsCornerItemActive]: item.value === tabAct }">
        <p :class="[$style.tabsCornerItemText]">{{ item.text }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ITabEItem } from './type';
import { h, ref, toRaw, VNode } from 'vue';
const emits = defineEmits(['tabAction']);

type listT = {
  tabList: ITabEItem[];
  tab: string;
  isLine?: boolean;
};
const props = defineProps<listT>();
const tabAct = ref(props.tab);
function tabChange(v: string) {
  tabAct.value = v;
  emits('tabAction', v);
}

defineOptions({ name: 'ComRadioTabE' });
</script>

<style module lang="scss">
.tabsCorner {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding-top: 9px;
  overflow: hidden;
  flex-shrink: 0;
  height: 43px;

  &.noLine {
    &::after {
      background: none;
    }
  }
}
.tabsCornerItemWrap {
  min-width: 119px;
  height: 49px;
  padding: 0 15px;
  line-height: 43px;
  margin-left: -16px;
  position: relative;
  z-index: 10;
  cursor: pointer;

  &.activeWrap {
    //z-index: 99 !important; // 层级凸显
  }

  .tabsCornerItem {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: var(--skin-bg1);
    border-radius: 8px 8px 0 0;
    text-align: center;
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 15px;
      height: 100%;
      background: var(--skin-bg1);
    }
    &::before {
      border-top-left-radius: 6px;
      transform: skew(-20deg);
      left: -8px;
      background: var(--skin-bg1);
    }
    &::after {
      border-top-right-radius: 6px;
      transform: skew(20deg);
      right: -8px;
      background: var(--skin-bg1);
      box-shadow: 3px 18px 4px 0px rgba(0, 0, 0, 0.1);
    }
    &.tabsCornerItemActive {
      background-color: #527cff;
      color: #fff;
      &::before,
      &::after {
        background: #527cff;
      }
    }
  }

  &:first-child {
    margin-left: -7px;
    .tabsCornerItem {
      &::before {
        transform: skew(0deg);
      }
    }
  }
}
</style>
