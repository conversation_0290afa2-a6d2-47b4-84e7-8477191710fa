<template>
  <ul :class="[$style['radio-tab'], props.disabled ? '' : 'cursor-pointer']">
    <li
      v-for="tabItem in tabList"
      :key="tabItem.name"
      :class="[$style['radio-tab-nav'], tab === tabItem.name ? $style['radio-tab-nav_active'] : '']"
      @click="handleUpdateValue(tabItem.name)"
    >
      <span>{{ tabItem.label }}</span>
    </li>
  </ul>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  disabled?: boolean;
  tab: string;
  tabList: ITabItem[];
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  tab: '',
  tabList: () => [],
});
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabF' });
</script>

<style module lang="scss">
.radio-tab {
  @apply flex gap-0;
}

.radio-tab-nav {
  @apply py-[12px] px-[20px] text-[16px] text-[var(--skin-t11)];
  text-align: center;
  line-height: 1;
  user-select: none;
  background: transparent;
  border: 1px solid var(--skin-bd3);

  &.radio-tab-nav_active {
    @apply text-[var(--skin-t1)];
    background: url('./assets/tab-f-active.png') 0 0 no-repeat;
    background-size: 100% 100%;
    border: 1px solid transparent;
  }
}
</style>
