interface Window {
  $SYS: any;
  $SYS_CFG: any;
  $_ScriptInjector: any;
  $SYS_MAP: any;
  GISShare: any;
  IndoorMap: any;
  CONST_GSCache: any;
  CONST_GSOptions: any;
  CONST_GSParams: any;
  newIndoorService: any;
  DicCache: any;
  CONST_Function_DeviceStateValueConvertFun_Default_3: any;
  CONST_StyleInfo_Default_Deep: any;
  CONST_StyleInfo_Default_Deep2: any;
}

declare module '@kalimahapps/vue-icons';
declare const IndoorThree: any;
declare const IndoorMap: any;
