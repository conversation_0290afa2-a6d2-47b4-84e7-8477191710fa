/**
 * 智能巡检--视频轮巡
 */

export default {
  //任务
  taskList: '/video/task/page', //任务列表
  taskDetails: '/video/task', ///video/task/{id}任务详情
  taskRecord: '/video/task/record', //任务列表统计
  taskUpstatus: '/video/task/upstatus', //任务详情页 巡检状态更新
  taskReport: '/ehscommom/hazard/report', //巡检异常上报
  taskReportOk: '/video/task/upvideoresult', //正常上报的接口
  taskRuseltDevice: '/video/task/taskdevice', //巡检结果详情接口
  taskHazardClass: '/ehscommom/hazardClass/details', //巡检上报过程
  taskHazardGrade: '/ehscommom/hazardGrade/list', //隐患上报列表字段
  taskAbnoExp: '', //巡检异常导出 缺少
  //设备
  equiList: '/video/device/page', //设备列表
  equiListRecord: '/video/device/record', //设备列表上的统计接口
  equiListDetail: '/video/device', ///video/device/{id} 设备详情
  equiListVideourl: '/video/device/videourl', //查看视频详情

  //计划
  planList: '/video/plan/page', //计划列表
  planRecord: '/video/plan/record', //计划统计
  planEdit: '/video/plan', //计划保存为post、编辑put、删除delete、详情get 后加IDvideo/plan/{id}
  planUpstatus: '/video/plan/upstatus', //启用、停用
  //公共 视频巡检列表
  videoDeviceList: '/video/device/list',
};
