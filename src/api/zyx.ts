export default {
  intelligent: {
    inspectTaskOverViewStatistics: '/video/task/overViewStatistics', // 巡检任务管理-顶部统计栏
    inspectTaskPagelist: '/video/task/pageViewTask', // 巡检任务管理-分页查询
    taskTopDetail: '/video/task/taskTopDetail', // 任务详情-顶部基本信息
    taskInspectionDetailStatistic: '/video/task/taskInspectionDetailStatistic', // 任务详情-巡检详情-卡片统计
    taskInspectionDetail: '/video/task/taskInspectionDetail', // 任务详情-巡检详情 分页
    taskInspectionPage: '/video/task/taskInspectionPage', // 任务详情-巡检清单
    taskPointDetail: '/video/task/taskPointDetail', // 任务详情-巡检点详情
    taskPointDetailByPositionNo: '/video/task/taskPointDetailByPositionNo', // 任务详情-巡检点详情(上一步下一步)
    getVideoDeviceInfo: '/video/device/getVideoDeviceInfo', // 查询视频终端设备信息
    taskInspectionPageExport: '/video/task/taskInspectionPageExport', // 任务详情-巡检清单导出
  },

  internetMonitor: {
    // 处置事件信息 ->
    getDisposeEventFireInfo: 'device/dispose/getEventRecordInfo', // 火警信息
    getDisposeEventFireRecord: 'device/dispose/record', // 火警信息-处置记录
    getDisposeEventHazardInfo: 'hazardRecord/qeuryEventDetail', // 隐患信息
    getDisposeEventHazardUrgeRecord: '/hazardRecord/getUrgeRecord', // 隐患信息-催促记录
    getDisposeEventHazardRecord: 'hazardRecord/hazardEventRecord', // 隐患信息-处置记录
    // 处置事件信息 <-
  },
};
