/**
 * 智能巡检--公共接口
 */
export default {
  serve: {
    treeList: '/sys/org/tree', //组织机构管理
    unitmap: '/ehscommom/unitmap', //获取单位地图
    unitmapPoint: '/ehscommom/unitmap/point', //获取单位地图撒点
    findIbmInfoByVideoDeviceNum: '/ehscommom/ibmInfo/findIbmInfoByVideoDeviceNum', //获取视频所属智盒信息
    getTokenLoginInfoByToken: '/power/login/v1/getTokenLoginInfoByToken', //通过地址token和系统编码获取登录
    completionRanking: '/inspectionStatistics/completionRanking', //智能巡检任务完成率排名
    taskAll: '/inspectionStatistics/taskAll', //获取智能巡检任务执行情况、智能巡检任务来源、已接入智能设备总数、已完成巡检任务总数
    configList: '/param/algorithm/list', //参数配置列表
    updateConfigList: '/param/algorithm/update', //修改参数配置列表
    todayTask: '/inspectionStatistics/todayTask', //获取今日任务完成情况、今日任务各单位执行情况数据
    hazard: '/inspectionStatistics/hazard', //获取智能巡检异常数据
    hazardClassList: '/ehscommom/hazardClass/list', //获取隐患分类
    hazardRecord: '/ehscommom/hazardClass/hazardRecord', //获取隐患处理记录
    getWeather: '/weather/getRealWeatherInfoByDistinctCode', //获取天气
    receiveList: '/param/recipient/list',
    addReceive: '/param/recipient/add',
    updateReceive: '/param/recipient/update',
    userList: '/sys/org/getOrgUser',
    userSub: '/sys/org/getOrgUserIncludeSub',
  },
  file: {
    uploadFile: '/file/upload', // 上传文件
  },
};
