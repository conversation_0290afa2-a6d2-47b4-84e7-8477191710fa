<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <!--  left  -->
      <div :class="$style['area-l']">
        <a :href="zhPlatformUrl" target="_blank">
          <img v-if="logoSrc" :class="$style['ycsy-logo']" :src="logoSrc" alt="" />
          <span :class="$style['title']">{{ zhName }}</span>
        </a>
      </div>
      <!--  right  -->
      <div :class="$style['area-r']">
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import Avatar from './comp/Avatar.vue';
import { computed, ref } from 'vue';
import anGang from '../../assets/angang.jpg';
import yanChang from '../../assets/yanchang.jpg';
import wych from '../../assets/wych.png';
import hbzdn from '../../assets/hbzdn.png';
import { useStore } from '@/store';
const store = useStore();
const zhName = ref(store.userInfo.zhName || '');
const zhLogo = ref(store.userInfo.zhLogo || '');
const zhPlatformUrl = ref<string>('');
zhPlatformUrl.value = store.userInfo.zhPlatformUrl;
// const logoSrc = computed(() => {
//   let lSrc = '';
//   if (zhLogo.value === 'angang') {
//     lSrc = anGang;
//   } else if (zhLogo.value === 'changhang') {
//     lSrc = wych;
//   } else if (zhLogo.value === 'yanchang') {
//     lSrc = yanChang;
//   } else if (zhLogo.value === 'zhongdanong') {
//     lSrc = hbzdn;
//   }
//   return lSrc;
// });
// const logoSrc = `${store.userInfo.zhLogoUrl}${store.userInfo.zhLogo}.png`;
const logoSrc = store.userInfo.logoPicUrl;
defineOptions({ name: 'MisHeaderComp' });
</script>

<style module lang="scss">
@font-face {
  /* 重命名字体名 */
  font-family: 'YouSheBiaoTiHei';
  /* 引入字体 */
  src: url('@/assets/font/YouSheBiaoTiHei-Bold.TTF');
  font-weight: normal;
  font-style: normal;
}

.header {
  position: relative;
  overflow: hidden;
  background: #252843;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  position: relative;
  font-size: 24px;
  //padding-left: 45px;
  display: flex;
  align-items: center;

  a {
    display: flex;
    align-items: center;
  }
}

.area-r {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 20px;
}

.title {
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  font-size: 30px;
  color: #fff;
  line-height: 30px;
  text-shadow: 0px 2px 8px rgba (41, 47, 58, 0.2016);
}

.ycsy-logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}
</style>
