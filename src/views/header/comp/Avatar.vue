<template>
  <div :class="$style['avatar']">
    <div :class="$style['img']">{{ userInitial }}</div>
    <n-ellipsis v-if="userName" class="!max-w-[60px]" :tooltip="false" :title="userName.length > 5 ? userName : ''">
      {{ userName }}
    </n-ellipsis>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, computed } from 'vue';
import { useStore } from '@/store';
const store = useStore();
const emits = defineEmits(['show-downloadApp']);

defineComponent({ name: 'avatarComp' });

const userName = store.userInfo.userName;
const userInitial = computed(() => {
  const name = store.userInfo.userName || '';
  return name.length >= 2 ? name.slice(-2) : name;
});
</script>

<style module lang="scss">
.avatar {
  display: grid;
  align-items: center;
  grid-template-columns: auto 1fr;
  column-gap: 12px;

  .img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    background-color: #527cff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}
</style>
