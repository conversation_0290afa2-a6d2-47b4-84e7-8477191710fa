<template>
  <div :class="[$style.menu, active ? $style.bgOn : $style.bg]">
    {{ label }}
  </div>
</template>

<script lang="ts" setup>
import { computed, defineComponent } from 'vue';

defineComponent({ name: 'menuComp' });

defineProps({
  label: String,
  active: Boolean,
  scaleX: Number,
});
</script>

<style module lang="scss">
@mixin createBg($bg, $x: 1) {
  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/views/mis-header/assets/' + $bg) center no-repeat;
    transform: scale3d($x, 1, 1);
    z-index: -1;
  }
}

.bg {
  @include createBg('menu-bg.png', v-bind(scaleX));
}

.bgOn {
  @include createBg('menu-bg-on.png', v-bind(scaleX));
}

.menu {
  position: relative;
  width: 140px;
  height: 45px;
  display: grid;
  place-content: center;
  color: #f5fbff;
  text-shadow: 0 3px 18px rgba(0, 70, 117, 0.72);
  font-size: 20px;
  font-style: italic;
  font-weight: 600;
  letter-spacing: 3px;
  background-size: contain;
  transition: all 0.2s;
  transform-origin: center bottom;
  cursor: pointer;
  user-select: none;

  &:hover {
    @apply bgOn scale-105;
  }

  &:active {
    @apply scale-100;
  }
}
</style>
