import { ref } from 'vue';
import { ITreeItem, IPlanBaseInfo, IDeviceRow } from '@/views/inspect-plan/type';

export class PlanService {
  static curTreeNode = ref<ITreeItem | null>(null);

  static curBuildId = ref<string | null>(null);
  static curFloorId = ref<string | null>(null);

  // 更新计划-基本信息
  static initBaseInfoForm() {
    return {
      id: '',
      deptId: null,
      planStartDate: null,
      planEndDate: null,
      planFrequency: 1, //计划巡检频次
      planFrequencyUnit: 2, //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
      planName: '',
    };
  }
  static baseInfoForm = ref<IPlanBaseInfo>(this.initBaseInfoForm());

  // 更新计划-巡检点位
  static inspectDevices = ref<IDeviceRow[]>([]);

  static reset() {
    this.curTreeNode.value = null;
    this.curBuildId.value = null;
    this.curFloorId.value = null;
    this.baseInfoForm.value = this.initBaseInfoForm();
    this.inspectDevices.value = [];
  }
}
