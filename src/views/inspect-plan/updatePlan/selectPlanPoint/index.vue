<template>
  <div class="w-full h-full">
    <template v-if="isOnlyTable">
      <TableComp />
    </template>
    <template v-if="isMultipleUI">
      <div class="switch-btn" @click="switchUIHandle">
        {{ switchText }}
      </div>
      <keep-alive>
        <component :is="curComp"></component>
      </keep-alive>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import TableComp from './table/index.vue';
import ThreeGisComp from './threeGis/index.vue';
import { getUnitInfo } from '@/gis-floor/floorGisService';
import { PlanService } from '../planService';

defineOptions({ name: 'SelectPlanPoint' });

// UI模式 - (serviceModelCode: 5)
const isOnlyTable = ref(false);
const isMultipleUI = ref(false);

// 切换模式(1-gis, 2-列表)
const curUIType = ref<1 | 2>(1);
const switchText = computed(() => (curUIType.value === 1 ? '列表视图' : '点位视图'));

const curComp = computed(() => (curUIType.value === 1 ? ThreeGisComp : TableComp));

const switchUIHandle = () => {
  if (curUIType.value === 1) {
    curUIType.value = 2;
    return;
  }
  if (curUIType.value === 2) curUIType.value = 1;
};

const getData = async () => {
  const _id = PlanService.curTreeNode.value?.id || '';
  const serviceModelCode: any = await getUnitInfo(_id);
  if (serviceModelCode === 5) {
    isOnlyTable.value = true;
  } else {
    isMultipleUI.value = true;
  }
};

getData();
</script>

<style scoped lang="scss">
.switch-btn {
  @apply absolute top-0 right-0 cursor-pointer;
}
</style>
