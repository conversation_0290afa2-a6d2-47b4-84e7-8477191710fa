<template>
  <div class="w-full h-full com-g-col-a1">
    <SelectBuild class="w-[220px]" />
    <div class="map-box">
      <FloorGis
        :type="gisType"
        :build-id="PlanService.curBuildId.value"
        :floor-id="PlanService.curFloorId.value"
        :default-inspect-list="PlanService.inspectDevices.value"
        @on-update:inspect="changeInspect"
        @on-update:floor="changeFloor"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SelectBuild from './selectBuild.vue';
import FloorGis from '@/gis-floor/floorGis.vue';
import { PlanService } from '../../planService';
import { IDeviceRow } from '@/views/inspect-plan/type';
import { EGisType } from '@/gis-floor/constant';

defineOptions({ name: 'SelectPointGis' });

const gisType = EGisType.INSPECTEDIT;

const changeInspect = (list: IDeviceRow[]) => {
  PlanService.inspectDevices.value = list;
};
const changeFloor = (floorId: string) => {
  PlanService.curFloorId.value = floorId;
};
</script>

<style scoped lang="scss">
.map-box {
  width: 100%;
  height: 100%;
  margin-left: 16px;
}
</style>
