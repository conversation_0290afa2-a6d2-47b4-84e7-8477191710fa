<template>
  <n-scrollbar content-class="h-full">
    <ul class="build-list">
      <li
        v-for="item in listData"
        :key="item.buildingId"
        :class="['build-item', curBuildId === item.buildingId ? 'build-item_actived' : '']"
        @click="selectedHandle(item)"
      >
        <div class="build-name">{{ item.buildingName }}</div>

        <div class="device-count">
          视频点位<span class="count-value">{{ item.pointNum || 0 }}</span
          >个
        </div>
      </li>
    </ul>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PlanService } from '../../planService';
import { getBuildingListByUnitId } from '@/views/inspect-plan/fetchData';
import { IBuildingItem } from '@/views/inspect-plan/type';
import { useActionWarning } from '@/common/hooks/comfirm.ts';

defineOptions({ name: 'SelectBuildComp' });

const curBuildId = PlanService.curBuildId;
const listData = ref<IBuildingItem[]>([]);

const selectedHandle = (item: any) => {
  if (PlanService.inspectDevices.value.length > 0) {
    useActionWarning({ msg: '当前填写的信息将不会保存' }).then(() => {
      PlanService.inspectDevices.value = [];
      PlanService.curFloorId.value = null;
      PlanService.curBuildId.value = item.buildingId;
    });
  } else {
    PlanService.curFloorId.value = null;
    PlanService.curBuildId.value = item.buildingId;
  }
};

const getBuildList = () => {
  const unitId = PlanService.curTreeNode.value?.attributes.erecordUnitId || '';
  getBuildingListByUnitId(unitId).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      listData.value = _data;
      if (listData.value.length > 0) {
        if (!PlanService.curBuildId.value) {
          PlanService.curBuildId.value = _data[0].buildingId;
        }
      }
    }
  });
};
getBuildList();
</script>

<style scoped lang="scss">
.build-list {
  @apply flex flex-col gap-[16px];

  .build-item {
    @apply w-[220px] flex flex-col gap-[10px] text-[14px] px-[16px] py-[12px] cursor-pointer;
    line-height: 18px;
    color: var(--skin-t1);
    border-radius: 0px;
    background: linear-gradient(90deg, rgba(22, 63, 135, 0.09) 0%, rgba(24, 79, 173, 0.3) 100%);
    border: 1px solid;
    border-image: linear-gradient(90deg, rgba(87, 149, 255, 0.5), rgba(87, 149, 255, 0)) 1 1;

    &:hover {
      border-image: linear-gradient(90deg, rgba(255, 179, 72, 0.5), rgba(255, 182, 79, 0)) 1 1;
    }
    &.build-item_actived {
      background: linear-gradient(90deg, rgba(135, 90, 22, 0.2) 0%, rgba(173, 111, 24, 0.5) 100%);
      border-image: linear-gradient(90deg, rgba(255, 179, 72, 0.5), rgba(255, 182, 79, 0)) 1 1;
    }

    .count-value {
      font-family: 'dinPro';
      font-size: 18px;
      margin: 0 6px;
    }

    .build-name {
      font-size: 16px;
      line-height: 1;
    }
  }
}
</style>
