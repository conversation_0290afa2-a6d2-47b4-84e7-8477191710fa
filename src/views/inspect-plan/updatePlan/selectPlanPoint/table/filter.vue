<template>
  <n-form inline size="small" label-placement="left">
    <n-form-item label="楼栋:">
      <n-select
        class="!w-[260px]"
        placeholder="请选择楼栋"
        v-model:value="buildId"
        :options="buildOpt"
        label-field="buildingName"
        value-field="buildingId"
      />
      <!-- @click="buildClick" -->
    </n-form-item>
    <n-form-item label="楼层:">
      <n-select
        class="!w-[260px]"
        placeholder="请选择楼层"
        v-model:value="floorId"
        :options="floorOpt"
        label-field="floorName"
        value-field="floorId"
        clearable
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { ref, watch, toRaw, nextTick } from 'vue';
import { getBuildingListByUnitId, getFloorList } from '@/views/inspect-plan/fetchData';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { useActionWarning } from '@/common/hooks/comfirm.ts';
import { IBuildingItem } from '@/views/inspect-plan/type';

const buildOpt = ref<IBuildingItem[]>([]);
const floorOpt = ref([]);

const buildId = PlanService.curBuildId;
const floorId = PlanService.curFloorId;

const getBuildOpt = (unitId: string) => {
  getBuildingListByUnitId(unitId).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      buildOpt.value = _data;
      if (buildOpt.value.length > 0 && !PlanService.curBuildId.value) {
        PlanService.curBuildId.value = _data[0].buildingId;
      }

      getFloorOpt(PlanService.curBuildId.value || '');
    }
  });
};

const getFloorOpt = (buildId: string) => {
  const unitId = PlanService.curTreeNode.value?.attributes.erecordUnitId || '';
  getFloorList({
    unitId,
    buildId,
  }).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      floorOpt.value = _data;
      if (!PlanService.curFloorId.value) {
        PlanService.curFloorId.value = _data[0].floorId;
      }
    }
  });
};

const emits = defineEmits(['search']);

watch(
  () => buildId.value,
  (newV: string | null, oldV: string | null) => {
    if (!newV) return;
    PlanService.curFloorId.value = null;
    if (PlanService.inspectDevices.value.length > 0) {
      useActionWarning({ msg: '当前填写的信息将不会保存' })
        .then(() => {
          PlanService.inspectDevices.value = [];
          getFloorOpt(newV);
        })
        .catch(() => {
          buildId.value = oldV;
        });
    } else {
      getFloorOpt(newV);
    }
  }
);

watch(
  () => floorId.value,
  (newV: string | null) => {
    if (!newV) return;

    emits('search', {
      deptId: PlanService.curTreeNode.value?.id,
      buildingId: buildId.value,
      floorId: newV,
    });
  }
);

watch(
  () => PlanService.curTreeNode.value,
  (newV: any, oldV: any) => {
    if (!newV) return;
    if (newV.id !== oldV?.id) getBuildOpt(newV.attributes.erecordUnitId);
  },
  {
    immediate: true,
  }
);

// getBuildOpt();

defineOptions({ name: 'SelectPointTableFilter' });
</script>

<style scoped lang="scss"></style>
