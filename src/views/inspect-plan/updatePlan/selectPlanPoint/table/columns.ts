import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    type: 'selection',
    width: 55,
  },
  {
    title: '序号',
    key: 'index',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '楼层',
    key: 'floorName',
    width: 150,
  },
  {
    title: '设备位置',
    key: 'deviceAddress',
    ellipsis: {
      tooltip: true,
    },
  },
];
