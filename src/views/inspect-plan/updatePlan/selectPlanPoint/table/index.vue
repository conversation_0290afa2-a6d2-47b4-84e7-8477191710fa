<template>
  <div class="w-full h-full com-g-row-a1" :class="$style.InspectAddPlanTable">
    <Filter @search="getTableDataWarp" />

    <div class="min-h-0">
      <n-data-table
        class="h-full"
        remote
        striped
        :row-key="(rowData: any) => rowData.deviceId"
        v-model:checked-row-keys="defaultCheckedRows"
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :flex-height="flexHeight"
        :pagination="pagination"
        :loading="loading"
        :render-cell="useEmptyCell"
        scroll-x=""
        @update:checked-row-keys="handleCheck"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject, h, Ref } from 'vue';
import { useRoute } from 'vue-router';
import { cols } from './columns';
import { DataTableColumns, NButton } from 'naive-ui';
import Filter from './filter.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { IObj } from '@/types';
import { IPlanBaseInfo } from '@/views/inspect-plan/type';
import { getDevicesListPage } from '@/views/inspect-plan/fetchData';
import { IDeviceRow } from '@/views/inspect-plan/type';
import { PlanService } from '../../planService.ts';

defineOptions({ name: 'SelectPointTable' });

const flexHeight = computed(() => {
  return tableData.value.length === 0 ? false : true;
});
const route = useRoute();

const isEdit = !!route.query.id;

let filterData: IObj<any> = ref({}); // 搜索条件

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const tableData = ref<IDeviceRow[]>([]);
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData.value,
  };
  search(getDevicesListPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWarp(filterForm: any) {
  filterData.value = filterForm;
  getTableData();
}

const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 180,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            color: '#FA5151',
            ghost: true,
            size: 'small',
            onClick: () => {},
          },
          { default: () => '查看视频' }
        ),
      ];
    },
  });
}

// select rows
const defaultCheckedRows = ref<string[]>([]);

const handleCheck = (
  keys: Array<string>,
  rows: IDeviceRow[],
  meta: { row: IObj<any> | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
) => {
  PlanService.inspectDevices.value = rows;
};

watch(
  () => filterData.value.buildingId,
  (newValue, oldValue) => {
    if (oldValue == undefined && isEdit) {
      defaultCheckedRows.value = PlanService.inspectDevices.value.map((item) => item.deviceId);
    } else if (newValue !== oldValue) {
      defaultCheckedRows.value = [];
      PlanService.inspectDevices.value = [];
    }
  }
);

setColumns();
</script>

<style module lang="scss">
.InspectAddPlanTable {
  height: 65vh;
  row-gap: 20px;
  padding: 10px;
}
</style>
