<template>
  <div :class="$style.InspectUpdate" class="com-g-row-a1">
    <ComBread :data="breadData" />
    <div :class="$style.content">
      <ComHeaderD :title="isEdit ? '编辑计划' : '新建计划'" />
      <div :class="$style['stpe-box']">
        <!-- <ComTabF disabled :tab-list="tabList" :tab="curTab" /> -->
        <n-steps style="width: 50%" :current="current" :status="currentStatus">
          <n-step title="计划时间配置" />
          <n-step title="巡检点位选择" />
        </n-steps>
      </div>
      <div>
        <BaseForm ref="baseFormRef" v-if="current == 1" class="!w-[470px]" />
        <SelectPlanPoint v-else />
        <!-- <SelectPlanPoint /> -->
      </div>
      <footer class="w-full">
        <n-space justify="center">
          <n-button @click="handleClose"> 取消 </n-button>
          <n-button v-show="current == 1" type="primary" @click="handleNext"> 下一步 </n-button>
          <!-- <n-button v-show="current == 2" type="primary" @click="handlePrev"> 上一步 </n-button> -->
          <n-button v-show="current == 2" type="primary" :disabled="isSubmitLoading" @click="confirmSave">
            {{ isSubmitLoading ? '提交中...' : '保存' }}
          </n-button>
          <n-button v-show="current == 2" type="primary" :disabled="isSubmitLoading" @click="confirmStart">
            {{ isSubmitLoading ? '提交中...' : '保存并启用' }}
          </n-button>
        </n-space>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, Ref, onBeforeUnmount } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComHeaderD from '@/components/header/ComHeaderD.vue';
import BaseForm from './baseForm.vue';
import SelectPlanPoint from './selectPlanPoint/index.vue';
import { IPlanBaseInfo } from '@/views/inspect-plan/type';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { useActionComfirm, useActionWarning } from '@/common/hooks/comfirm.ts';
import { planAdd, planEdit, planDetailApi } from '@/views/inspect-plan/fetchData';
import { PROVIDE_KEY, ACTION } from '@/views/inspect-plan/constant';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
import { FloorGisService } from '@/gis-floor/floorGisService';

defineOptions({ name: 'InspectPlanUpdate' });
const breadData: IBreadData[] = [{ name: '智能巡检' }, { name: '计划新增' }];

const router = useRouter();
const route = useRoute();
const isEdit = !!route.query.id;

const current = ref<1 | 2>(1);
const currentStatus = ref('process');

const baseFormRef = ref();

const isSubmitLoading = ref(false);

// action from
const handleClose = () => {
  useActionWarning({ msg: '当前填写的信息将不会保存', negaT: '取消' }).then(() => {
    router.push({ path: '/video/inspect-plan' });
  });
};

// const handlePrev = () => {
//   current.value = 1;
// };

const handleNext = () => {
  baseFormRef.value?.validateHandle().then((baseInfo: IPlanBaseInfo) => {
    current.value = 2;
  });
};

const confirmSave = () => {
  if (PlanService.inspectDevices.value.length < 1) {
    $toast.warning('请选择设备');
  } else {
    useActionComfirm({ msg: '保存后，将会生成智能巡检计划，请在巡检计划管理中启用该计划。' }).then(() => {
      handleSave(0);
    });
  }
};
const confirmStart = () => {
  if (PlanService.inspectDevices.value.length < 1) {
    $toast.warning('请选择设备');
  } else {
    useActionComfirm({ msg: '保存后，将会生产智能巡检计划同时启用该计划。' }).then(() => {
      handleSave(1);
    });
  }
};

const handleSave = (planStatus: 0 | 1) => {
  const updatePlan = isEdit ? planEdit : planAdd;

  const relVideos = PlanService.inspectDevices.value.map((item, index) => ({
    deviceId: item.deviceId,
    deviceName: item.deviceName,
    deviceSort: index + 1,
    deviceNum: item.deviceNum,
    loopDeviceNum: item.loopDeviceNum,
    manufacturerCode: item.manufacturerCode,
    buildingName: item.buildingName,
    floorName: item.floorName,
    buildingId: item.buildingId,
    floorId: item.floorId,
    deviceAddress: item.deviceAddress,
    mapX: item.mapX,
    mapY: item.mapY,
    mapZ: item.mapZ,
    latitude: item.videoLatitude,
    longitude: item.videoLongitude,
  }));
  const params = {
    planStatus,
    ...PlanService.baseInfoForm.value,
    relVideos,
  };
  updatePlan(params)
    .then((res) => {
      router.push({ path: '/video/inspect-plan' });
    })
    .finally(() => {
      isSubmitLoading.value = false;
    });
};

// 编辑赋值
const getPlanDetail = async () => {
  let res = await planDetailApi(route.query.id as string);
  PlanService.inspectDevices.value = Array.isArray(res.data.relVideos) ? res.data.relVideos : [];
  PlanService.baseInfoForm.value = {
    id: res.data.id,
    deptId: res.data.deptId,
    planStartDate: res.data.planStartDate,
    planEndDate: res.data.planEndDate,
    planFrequency: res.data.planFrequency,
    planFrequencyUnit: res.data.planFrequencyUnit,
    planName: res.data.planName,
  };
  if (PlanService.inspectDevices.value.length > 0) {
    PlanService.curBuildId.value = PlanService.inspectDevices.value[0].buildingId;
    PlanService.curFloorId.value = PlanService.inspectDevices.value[0].floorId;
  }
  baseFormRef.value?.setRangeData([res.data.planStartDate.slice(0, 10), res.data.planEndDate.slice(0, 10)]);
};

// init
if (isEdit) {
  getPlanDetail();
}

onBeforeUnmount(() => {
  PlanService.reset();
});
</script>

<style module lang="scss">
.InspectUpdate {
  .content {
    position: relative;
    row-gap: 20px;
    display: grid;
    grid-template-rows: auto auto minmax(0, 1fr) auto;
    grid-template-columns: minmax(0, 1fr);
    min-height: 0;
  }

  .stpe-box {
    @apply w-full flex justify-center items-center;
  }
}
.modal-main {
  @apply w-full h-full;
}
</style>
<style scoped lang="scss"></style>
