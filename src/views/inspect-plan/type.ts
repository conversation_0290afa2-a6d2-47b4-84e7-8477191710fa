import type { IObj, IPageRes } from '@/types';
import { ACTION } from './constant';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

//tab 数据
export interface ITabData {
  text: string;
  value: string;
}

//视频巡检视频选类型的列表
export interface IDeviceItem {
  deviceAddress: string;
  deviceId: string;
  deviceName: string;
  deviceTypeId: string;
  latitude: number;
  longitude: number;
  mapX: number;
  mapY: number;
  mapZ: number;
  deviceNum: string;
  manufacturerCode: string;
  onlineState?: string;
  loopDeviceNum?: string;
}

//设备详情 类型
export interface IDeviceDetail {
  allTaskNum: number;
  allDisposeNum: number;
  aerialMapType: number;
  brandId: string;
  buildingId: string;
  buildingName: string;
  cardInfo: unknown;
  channelNum: string;
  createTime: string;
  deviceAddress: string;
  deviceClassification: number;
  deviceId: string;
  deviceName: string;
  deviceNum: string;
  deviceOnlyId: string;
  deviceTask: string;
  deviceTypeId: string;
  deviceTypeName: string;
  deviceTypePid: string;
  deviceTypePname: string;
  floorAreaImg: string;
  floorId: string;
  floorMapType: number;
  floorName: string;
  houseNumber: string;
  installImg: string;
  installInfo: string;
  isAerialMap: number;
  keyPartId: string;
  laLoop: string;
  laMake: string;
  laPoint: string;
  latitude: number;
  longitude: number;
  manufacturerCode: string;
  mapX: number;
  mapY: number;
  mapZ: number;
  monitoringInfo: string;
  nonHostInfo: string;
  obsoleteDate: string;
  ownerId: string;
  ownerType: number;
  produceInfo: string;
  projectId: string;
  serviceModelCode: number;
  serviceModelName: string;
  standardInfo: string;
  status: number;
  subCenterCode: string;
  subCenterName: string;
  subordinateUnits: string;
  timeInterval: number;
  twoCode: string;
  unitAddress: string;
  unitName: string;
  unitPointX: number;
  unitPointY: number;
  updateTime: string;
  useInfo: unknown;
  videoLatitude: string;
  videoLongitude: string;
  videoMapX: string;
  videoMapY: string;
  videoMapZ: number;
  onlineState: string;
}

//当前树结构 类型
export interface ITreeItem {
  attributes: any;
  checked: boolean;
  children: any[];
  deptIds: string;
  hasChildren: boolean;
  hasParent: boolean;
  id: string;
  level: string;
  levelCode: string;
  parentId: string;
  state: string;
  text: string;
  treeName: string;
  type: string;
  typeId: string;
  unitId: string;
  unitIds: string;
  unitStatus: string;
}

// 计划-基本信息
export interface IPlanBaseInfo {
  deptId: string | number | null;
  id?: string | number | null;
  planStartDate: number | string | null;
  planEndDate: number | string | null;
  planFrequency: string | number | null; //计划巡检频次
  planFrequencyUnit: number | string | null; //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
  planName: string | null;
}

// 设备信息
export interface IDeviceRow {
  algoList: any[];
  buildingId: string;
  buildingName: string;
  deviceAddress: string;
  deviceId: string;
  deviceName: string;
  deviceNum: string;
  floorId: string;
  floorName: string;
  latitude: number;
  longitude: number;
  loopDeviceNum: string;
  mapX: number;
  mapY: number;
  mapZ: number;
  onlineState: string;
  videoLatitude: number;
  videoLongitude: number;
  manufacturerCode: string | number;
}
// 巡检设备
export interface IInspectDevice {
  deviceId: string;
  deviceName: string;
  deviceSort: number;
  deviceNum: string | number;
  loopDeviceNum: string | number;
  manufacturerCode: string | number;
  buildingName: string;
  floorName: string;
  buildingId: string;
  floorId: string;
  deviceAddress: string;
}

// buildingItem
export interface IBuildingItem {
  buildingId: string;
  buildingName: string;
  pointNum: string | number;
}
