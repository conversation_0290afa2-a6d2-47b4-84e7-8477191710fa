import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import type { TreeOption } from 'naive-ui';
import { IObj, IPageRes } from '@/types';
import { IDeviceRow } from './type';

export function getVideoDeviceInfoAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/device/getVideoDeviceInfo', query);
  return $http.get<TreeOption[]>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
export function postTreeList(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.treeList, query);
  return $http.get<TreeOption[]>(url, {
    data: { _cfg: { showTip: true, showOkTip: false } },
  });
}
export function getInspectionPointListAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/plan/getInspectionPointList', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//获取事件清单
export function getPageViewTaskDeviceDisposeAPI(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, '/video/task/pageViewTaskDeviceDispose', params);
  return $http.post<IPageRes<any>>(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

//获取巡检计划
export function getDisposeOverViewStatisticsAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/task/disposeOverViewStatistics', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//获取巡检计划
export function getPlanListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planList, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//统计
export function planListRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planRecord, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//启用、停用
export function planUpStatusApi(data: { id: string; planStatus: number }) {
  const param = api.getComParams(api.type.intelligent, api.video.planUpstatus, data);
  return $http.put(param.url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...param.data },
  });
}

//保存新建
export function planAdd(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, api.video.planEdit, params);
  return $http.post(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}
//编辑
export function planEdit(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, api.video.planEdit, params);
  return $http.put(url, {
    data: { _cfg: { showTip: true, showOkTip: true }, ...data },
  });
}
//详情
export function planDetailApi(id: string | number, query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planEdit, query, id);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
export function getBuildingListByUnitId(unitId: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getBuildingListByUnitId, { unitId });
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function getFloorList(query: { unitId: string; buildId: string }) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getFloorList, { ...query });
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function getDevicesListPage(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, api.name.intelligent.getDeviceListPage, params);
  return $http.post<IPageRes<IDeviceRow>>(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}

export function getDeviceInfo(id: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getDevicelnfoByld, { deviceId: id });
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
