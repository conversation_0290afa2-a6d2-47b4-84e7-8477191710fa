<template>
  <div>
    <ComHeaderD title="计划基本信息" />
    <div :class="$style.baseInfo">
      <!-- 原有基本信息保持不变 -->
      <span>所属单位：{{}}</span>
      <span>计划名称：{{ detailInfo?.planName || '--' }}</span>
      <span
        >起止时间：{{
          detailInfo?.planStartDate ? `${detailInfo?.planStartDate} ~ ${detailInfo?.planEndDate}` : '--'
        }}</span
      >
      <span
        >是否重复：{{
          detailInfo?.planFrequency
            ? ` 每隔${detailInfo?.planFrequency}${
                detailInfo?.planFrequencyUnit == 1
                  ? '小时'
                  : detailInfo?.planFrequencyUnit == 2
                    ? '天'
                    : detailInfo?.planFrequencyUnit == 3
                      ? '周'
                      : detailInfo?.planFrequencyUnit == 4
                        ? '月'
                        : detailInfo?.planFrequencyUnit == 5
                          ? '季度'
                          : '年'
              }`
            : '--'
        }}</span
      >
      <span>创建人员：{{ detailInfo?.createdByName || '--' }}</span>
      <span>创建时间：{{ detailInfo?.createdTime || '--' }}</span>
      <span>最近编辑人员：{{ detailInfo?.updatedByName || '--' }}</span>
      <span>最近编辑时间：{{ detailInfo?.updateTime || '--' }}</span>
    </div>
    <div class="!mt-[20px]">
      <ComHeaderD title="计划巡检点位" />
      <div :class="$style.InspectEventTable" class="com-g-row-1a">
        <n-data-table
          class="h-full"
          remote
          striped
          :columns="columns"
          :data="tableData"
          :merged-cells="mergedCells"
          :bordered="false"
          :flex-height="true"
          :pagination="pagination"
          :loading="loading"
          :render-cell="useEmptyCell"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { planDetailApi, getInspectionPointListAPI } from '../fetchData.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { cols } from './PonitColumns';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import ComHeaderD from '@/components/header/ComHeaderD.vue';

const route = useRoute();
const columns = ref<any>([]);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const detailInfo = ref({});
function setColumns() {
  columns.value.push(...cols);
}
setColumns();

const getDetailData = async () => {
  let res = await planDetailApi(route.query.id);
  detailInfo.value = res.data;
};
getDetailData();

const tableData = ref([]);
function getTableData() {
  const params = {
    planId: route.query.id,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };

  search(getInspectionPointListAPI(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
getTableData();
defineOptions({ name: 'InspectPlanPlanInfo' });
</script>

<style module lang="scss">
.baseInfo {
  height: 120px;
  border: 1px solid #0081ff;
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px 0;
  align-items: center;
  padding: 20px 30px;

  > span {
    font-size: 14px;
    white-space: nowrap;
  }
}
.InspectEventTable {
  height: 53vh;
  row-gap: 20px;
  padding: 10px;
}
</style>
