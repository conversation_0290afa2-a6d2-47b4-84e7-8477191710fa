import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '事件编号',
    key: 'id',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '单位名称',
    key: 'deptName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '末次接收时间',
    key: 'receiveTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事件类型',
    key: 'eventTypeName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '事件位置',
    key: 'deviceAddress',
    align: 'left',
  },
  {
    title: '事件状态',
    key: 'disposeStatusName',
    align: 'left',
  },
];
