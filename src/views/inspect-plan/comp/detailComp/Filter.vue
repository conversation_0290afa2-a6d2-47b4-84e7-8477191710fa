<template>
  <div :class="$style['wrap']" style="display: flex; justify-content: space-between">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="6" label="接收时间：">
          <n-date-picker
            class="w-full"
            v-model:value="rangeDateTime"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            @update:change="handleChange"
            clearable
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
    <div :class="$style.selectinfo_btn" class="items-center" @click="exportData()">导出</div>
  </div>
</template>

<script lang="ts" setup>
import { ACTION } from '../../constant';
import { useRoute } from 'vue-router';
import { formatTimestamp } from '@/utils/format.ts';
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { dayjs } from '@/utils/dayjs';
import { IObj } from '@/types';
import { fileDownloader } from '@/utils/fileDownloader';

const props = defineProps({});
const emits = defineEmits(['action']);
const route = useRoute();
const filterForm = ref(initForm());
// const { fbztOpt } = $dict.useFbzt();
const rangeDateTime = ref(null);
function initForm() {
  return {
    startTime: '',
    endTime: '',
  };
}
const handleChange = (value: any) => {
  if (value && value.length === 2) {
    filterForm.value.startTime = dayjs(value[0]).format('YYYY-MM-DD 00:00:00');
    filterForm.value.endTime = dayjs(value[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    filterForm.value.startTime = '';
    filterForm.value.endTime = '';
  }
};

function transform(obj: any) {
  if ((!obj) instanceof Object) {
    throw new Error('transform(): 传入参数不是对象');
  }
  const arr = [];
  for (const item in obj) {
    arr.push(item + '=' + obj[item]);
  }
  return arr.join('&');
}
function exportData() {
  const params = {
    planId: route.query.id,
    ...filterForm.value,
  };
  fileDownloader(
    `${window.$SYS_CFG.apiBaseURL}api/v3/intelligent-inspection-service/video/task/pageViewTaskDeviceDispose/export?${transform(params)}`
  );
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}
function doHandle(action: ACTION, extData?: IObj<any>) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});
watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'InspectTaskFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
.selectinfo_btn {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px 0 24px;
  background: linear-gradient(180deg, #0d1e3b 0%, #2251a1 100%);
  border-radius: 2px;
  border: 1px solid #3371dc;

  &:hover {
    /* 添加悬停效果 */
    background: linear-gradient(180deg, #2251a1 0%, #0d1e3b 100%); /* 反转渐变色 */
    box-shadow: 0 0 5px rgba(51, 113, 220, 0.8); /* 添加发光效果 */
  }
}
</style>
