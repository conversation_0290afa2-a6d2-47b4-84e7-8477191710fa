<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) of [
        { label: '异常事件数', value: cardList?.abnormalTotal || 0 },
        { label: '已处置', value: cardList?.disposedNum || 0 },
        { label: '带处置', value: cardList?.undisposedNum || 0 },
      ]"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`]]"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { getDisposeOverViewStatisticsAPI } from '../../fetchData.ts';

const route = useRoute();
const cardList = ref({});
const getCardList = async () => {
  let res = await getDisposeOverViewStatisticsAPI({ planId: route.query.id });
  cardList.value = res.data;
};
getCardList();
const cards = ref();

function getData() {
  // todo
}

// init
getData();

defineOptions({ name: 'InspectTaskInfoCardTask' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(auto-fill, 291px);
  gap: 10px 20px;

  .card {
    width: 291px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    background-size: 100% 100%;
    background-position: center;

    &.bg-1 {
      background-image: url('./assets/bg1.png');
    }
    &.bg-2 {
      background-image: url('./assets/bg2.png');
    }
    &.bg-3 {
      background-image: url('./assets/bg3.png');
    }

    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
