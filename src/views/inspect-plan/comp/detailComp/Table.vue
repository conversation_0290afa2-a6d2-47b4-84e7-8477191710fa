<template>
  <div :class="$style.InspectEventTable" class="com-g-row-1a">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, h, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { IObj } from '@/types';
import { cols } from './columns';
import { NButton } from 'naive-ui';
import { getPageViewTaskDeviceDisposeAPI } from '../../fetchData.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { ACTION } from '@/views/inspect-plan/constant.ts';

const emits = defineEmits(['action']);
const route = useRoute();
const router = useRouter();
const columns = ref<any>([]);
const [loading, search] = useAutoLoading(true);
const tableData = ref<any[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    render(row: any) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [];
  acList.push(
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',

          class: 'com-action-button1',
          onClick: () => {
            router.push({ name: 'inspectTaskDetail', query: { id: row.taskId } });
          },
        },
        { default: () => '详情' }
      ),
    ],
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',

          class: 'com-action-button1',
          onClick: () =>
            emits('action', {
              action: ACTION.DEVICELOC,
              data: {
                deviceId: row.videoId || '',
              },
            }),
        },
        { default: () => '位置' }
      ),
    ]
  );

  return acList;
}

setColumns();
function getTableData() {
  const params = {
    planId: route.query.id,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  search(getPageViewTaskDeviceDisposeAPI(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

let filterData: IObj<any> = {}; // 搜索条件
function getDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

defineExpose({
  getDataWrap,
  getTableData,
});

defineOptions({ name: 'InspectPlanEventList' });
</script>

<style module lang="scss">
.InspectEventTable {
  height: 65vh;
  row-gap: 20px;
  padding: 10px;
}
</style>
