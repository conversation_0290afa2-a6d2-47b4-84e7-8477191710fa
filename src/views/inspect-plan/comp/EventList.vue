<template>
  <div class="com-g-row-aa1">
    <InfoCardTask />
    <Filter ref="filterCompRef" @action="actionFn" />
    <Table class="com-table-container" ref="listCompRef" @action="actionFn" />
  </div>
</template>

<script setup lang="ts">
import { ACTION, PROVIDE_KEY } from '../constant.ts';
import { ref, provide } from 'vue';
import Filter from './detailComp/Filter.vue';
import InfoCardTask from './detailComp/InfoCardEvent.vue';
import Table from './detailComp/Table.vue';

const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} });
const filterCompRef = ref();
const listCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    listCompRef.value?.getDataWrap(data);
  } else {
    listCompRef.value?.getData();
  }
}

defineOptions({ name: 'InspectPlanEvent' });
</script>

<style module lang="scss"></style>
