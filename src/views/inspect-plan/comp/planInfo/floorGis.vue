<template>
  <FloorGis :type="gisType" :build-id="buildId" :floor-id="floorId" :default-inspect-list="inspectList" />
</template>

<script setup lang="ts">
import { ref, computed, inject, Ref } from 'vue';
import { PROVIDE_KEY } from './constant';
import FloorGis from '@/gis-floor/floorGis.vue';
import { EGisType } from '@/gis-floor/constant';

defineOptions({ name: 'InspectPlanGis' });

const gisType = EGisType.INSPECTDETAIL;

const detailInfo = inject(PROVIDE_KEY.DETAILDATA) as Ref<any>;

const buildId = ref('');
const floorId = ref('');
const inspectList = computed<any[]>(() => detailInfo.value?.relVideos || []);

if (inspectList.value?.length > 0) {
  buildId.value = inspectList.value[0]?.buildingId;
  floorId.value = inspectList.value[0]?.floorId;
}
</script>

<style scoped lang="scss"></style>
