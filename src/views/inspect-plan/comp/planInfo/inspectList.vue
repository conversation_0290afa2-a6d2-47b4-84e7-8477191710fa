<template>
  <div class="w-full h-full">
    <template v-if="isOnlyTable">
      <TableComp />
    </template>
    <template v-if="isMultipleUI">
      <div :class="$style['switch-btn']" @click="switchUIHandle">
        {{ switchText }}
      </div>
      <keep-alive>
        <component :is="curComp"></component>
      </keep-alive>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import TableComp from './tableList.vue';
import FloorGisComp from './floorGis.vue';
import { getUnitInfo } from '@/gis-floor/floorGisService';
import { useRoute } from 'vue-router';

defineOptions({ name: 'inspectListComp' });

const route = useRoute();

// UI模式 - (serviceModelCode: 5)
const isOnlyTable = ref(false);
const isMultipleUI = ref(false);

// 切换模式(1-gis, 2-列表)
const curUIType = ref<1 | 2>(1);
const switchText = computed(() => (curUIType.value === 1 ? '列表视图' : '点位视图'));

const curComp = computed(() => (curUIType.value === 1 ? FloorGisComp : TableComp));

const switchUIHandle = () => {
  if (curUIType.value === 1) {
    curUIType.value = 2;
    return;
  }
  if (curUIType.value === 2) curUIType.value = 1;
};

const getData = async () => {
  const _id = route.query.unitId as string;
  const serviceModelCode: any = await getUnitInfo(_id);
  if (serviceModelCode === 5) {
    isOnlyTable.value = true;
  } else {
    isMultipleUI.value = true;
  }
};

getData();
</script>

<style module lang="scss">
.switch-btn {
  @apply absolute top-0 right-0 cursor-pointer;
  width: 150px;
  height: 48px;
  background: url('../../assets/switch-bg.png') 0 0 no-repeat;
  display: flex;
  align-items: center;
  padding-left: 51px;
  font-size: 14px;
  color: #fff;
}
</style>
