<template>
  <InspectTaskIndex page-mode="NARROW" :plan-id="planId" :unit-id="unitId" class="h-full" />
</template>

<script setup lang="ts">
import InspectTaskIndex from '@/views/inspect-task/index.vue';
import { useRoute } from 'vue-router';
import { computed } from 'vue';

const route = useRoute();
const planId = computed(() => <string>route.query.id || '');
const unitId = computed(() => <string>route.query.unitId || '');

defineOptions({ name: 'InspectPlanTaskView' });
</script>

<style module lang="scss"></style>
