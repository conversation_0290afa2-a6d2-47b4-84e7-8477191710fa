<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="4" label="计划名称:">
          <n-input placeholder="请输入" v-model:value="filterForm.planName" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="6" label="创建时间:">
          <n-date-picker
            class="w-full"
            v-model:value="rangeDateTime"
            type="datetimerange"
            @update:change="handleChange"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="4" label="计划状态:">
          <n-select
            v-model:value="filterForm.planStatus"
            :options="options"
            label-field="label"
            value-field="value"
            clearable
          />
        </n-form-item-gi>
        <n-form-item-gi :span="4" class="flex justify-end">
          <n-button type="primary" @click="toUpdate()">
            <CdAdd />
            {{ ACTION_LABEL.ADD }}
          </n-button></n-form-item-gi
        >
      </n-grid>

      <div class="w-[12%] flex justify-end"></div>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ACTION, ACTION_LABEL } from '../constant.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';
import { CdAdd } from '@kalimahapps/vue-icons';
import { dayjs } from '@/utils/dayjs';
import { useActionWarning } from '@/common/hooks/comfirm.ts';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';

const emits = defineEmits(['action']);
const router = useRouter();
const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);
const rangeDateTime = ref(null);
function initForm() {
  return {
    planName: '',
    planStatus: null,
    startDate: '',
    endDate: '',
  };
}
const toUpdate = () => {
  if (PlanService.curTreeNode.value?.attributes.erecordUnitId !== '') {
    router.push({
      name: 'inspectPlanUpdate',
      query: {
        unitId: PlanService.curTreeNode.value?.id,
      },
    });
  } else {
    useActionWarning({ msg: '当前单位尚未制作电子档案，无法制定计划。', type: 'warning', negaT: '' });
  }
};

const handleChange = (value: any) => {
  if (value && value.length === 2) {
    filterForm.value.startDate = dayjs(value[0]).format('YYYY-MM-DD 00:00:00');
    filterForm.value.endDate = dayjs(value[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    filterForm.value.startDate = '';
    filterForm.value.endDate = '';
  }
};

function clearForm() {
  filterForm.value = {
    planName: '',
    planStatus: null,
    startDate: '',
    endDate: '',
  };
}

const options = ref([
  { label: '待开始', value: 0 },
  { label: '进行中', value: 1 },
  {
    label: '已完成',
    value: 2,
  },
  { label: '已停用', value: 3 },
]);

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}
// onMounted(() => {
//   doHandle(ACTION.SEARCH);
// });

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineExpose({
  clearForm,
});
defineOptions({ name: 'VideoEquiFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
