<template>
  <div class="com-g-row-a1">
    <div class="head">
      <n-ellipsis :class="$style.title">
        {{ data.planName || '--' }}
      </n-ellipsis>
      <p>创建时间：{{ data.createTime || '--' }}</p>
    </div>
    <div class="card" v-show="data.planStatus !== 0">
      <div>
        <p>
          <span>{{ data.inspectionPointCount || 0 }}</span
          >个
        </p>
        <p class="card_p">巡检点位</p>
      </div>
      <div>
        <p>
          <span>{{ data.taskExecuteCount || 0 }}</span
          >次
        </p>
        <p class="card_p">任务执行</p>
      </div>
      <div>
        <p>
          <span>{{ data.abnormalCount || 0 }}</span
          >次
        </p>
        <p class="card_p2">发现异常</p>
      </div>
      <div>
        <p>
          <span>{{ data.planFrequency || 0 }}</span
          >{{
            data.planFrequencyUnit == 1
              ? '小时'
              : data.planFrequencyUnit == 2
                ? '天'
                : data.planFrequencyUnit == 3
                  ? '周'
                  : data.planFrequencyUnit == 4
                    ? '月'
                    : data.planFrequencyUnit == 5
                      ? '季度'
                      : '年'
          }}/次
        </p>
        <p class="card_p">巡检频率</p>
      </div>
    </div>
    <div class="com-g-row-a1" v-show="data.planStatus !== 0">
      <div class="content">
        <div v-for="i in 1" :key="i">
          <n-image :src="itemNoPic" alt="" preview-disabled class="pointer-events-none" />
        </div>
        <div :title="data.lastTaskExecuteTime">
          <p class="text-ellipsis">最近一次任务执行时间：{{ data.lastTaskExecuteTime || '--' }}</p>
          <p>
            巡检结果：<span style="color: red">{{ data.inspectionResult || '--' }}</span>
          </p>
          <p>
            异常情况：<span style="color: red">{{ data.deviceExceptionCount || 0 }}处</span>
          </p>
        </div>
      </div>
    </div>
    <div v-show="data.planStatus == 0" style="padding-top: 20px">
      <img src="../assets/unStart.png" />
    </div>
    <div class="flex justify-end items-center !mt-[5px]">
      <slot name="action-buttons"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import itemNoPic from '@/views/inspect-task/assets/item-no-pic.png';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

defineOptions({ name: 'InspectTaskListItem' });
</script>

<style scoped lang="scss">
.head {
  p {
    font-size: 14px;
    color: #acb9d0;
  }
}
.card {
  width: 100%;
  margin-bottom: 5px;
  display: flex;
  gap: 2px;
  div {
    height: 78px;
    display: flex;
    flex: 1;
    padding: 0 10px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: rgba(97, 125, 167, 0.08);
    span {
      font-size: 24px;
    }
    .card_p {
      width: 68px;
      height: 24px;
      margin-top: 5px;
      line-height: 24px;
      // background: rgba(130, 150, 217, 0.4);
      border-radius: 2px;
    }
    .card_p2 {
      width: 68px;
      height: 24px;
      line-height: 24px;
      margin-top: 5px;
      // background: rgba(255, 72, 72, 0.4);
      border-radius: 2px;
    }
  }
}
.content {
  width: 100%;
  display: flex;
  font-size: 12px;
  margin-bottom: 5px;
  div:nth-child(2) {
    margin-left: 15px;
    p {
      margin-bottom: 10px;
    }
    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }
}
</style>

<style module lang="scss">
.title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
  max-width: 100%;
}
</style>
