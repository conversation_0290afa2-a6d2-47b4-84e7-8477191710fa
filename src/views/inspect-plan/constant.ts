import type { IObj } from '@/types';

export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EDIT = 'EDIT',
  MORE = 'MORE',
  ADD = 'ADD',
  DEL = 'DEL',
  USING = 'USING',
  STOPUSING = 'STOPUSING',
  TREECHANGE = 'TREECHANGE',
  DEVICELOC = 'DEVICELOC',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAILS]: '查看详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.MORE]: '更多',
  [ACTION.ADD]: '新建计划',
  [ACTION.DEL]: '删除',
  [ACTION.USING]: '启用',
  [ACTION.STOPUSING]: '停用',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.DEVICELOC]: '位置',
};
//计划状态 0：待启用，1：开启/使用中， 2：关闭/停用  3：过期
export const PLAN_STATE = {
  LAB: ['待启用', '使用中', '已停用', '已过期'],
  COLOR: ['#F39600', '#527CFF', '#FA5151', '#B2B2B2'],
};
