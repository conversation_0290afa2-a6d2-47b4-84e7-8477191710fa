<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData" />
    <div class="com-g-col-a1">
      <ComOrgTreeWrap @change="handleChange" />
      <div class="com-g-row-aa1">
        <com-card :data="cardData"></com-card>
        <com-filter @action="actionFn" ref="filterCompRef" />
        <List class="com-table-container min-h-0" ref="listCompRef" @action="actionFn" />
      </div>
    </div>

    <!-- <ADDPlanModal v-model:show="addDialogShow" @action="actionFn" @getCardData="getCardData" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, provide, Ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import ComCard from '@/components/card/ComCardG.vue';
import ComFilter from './comp/Filter.vue';
import List from './comp/List.vue';
import { ACTION, PROVIDE_KEY } from './constant';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useStore } from '@/store';
// import ADDPlanModal from './comp/updatePlan/index.vue';
import { IActionData } from './type';
import { IObj, IPageRes } from '@/types';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { planListRecordApi } from './fetchData.ts';

const listCompRef = ref();
const store = useStore();
const breadData = ref<IBreadData[]>([{ name: '智能巡检' }, { name: '巡检计划管理' }]);

const filterCompRef = ref();

const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} });

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'val');
  const { action, data } = val;

  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }

  if (val.action === ACTION.TREECHANGE) {
    handleSearch({});
    getCardData(PlanService.curTreeNode.value?.id || '');
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    listCompRef.value?.getDataWrap(data);
  } else {
    listCompRef.value?.getData();
  }
}

function handleChange(val: any) {
  PlanService.curTreeNode.value = val;

  handleSearch({});
  getCardData(val.id);
}

const cardData = ref({});
// 获取头部card
function getCardData(id: string) {
  planListRecordApi({ deptId: id }).then((res) => {
    cardData.value = res.data;
    console.log(cardData.value, 'cardData');
  });
}
// update plan
const addDialogShow = ref(false);

defineOptions({ name: 'InspectPlanIndex' });
</script>

<style scoped lang="scss">
.map-demo {
  @apply w-full h-[600px] bg-[#333];
}
</style>
