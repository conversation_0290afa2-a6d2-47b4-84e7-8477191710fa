<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="!treeLoading">
      <div class="layer-strenth" v-if="store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <com-card :list="cardList" :act="status" @action="cardHandle"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref, watch, onMounted, reactive } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import ComCard from '@/components/card/ComCardA.vue';
import { jumpToKETApi, getTaskStatisticsApi } from './fetchData';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData } from './type';
import type { ICardAItem } from '@/components/card/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useStore } from '@/store';
import strengthImage from '@/assets/strenth.png';
const store = useStore();
const staticParams = reactive<any>({
  page: 1,
  size: -1,
  orgId: store.userInfo.deptId,
  deptId: store.userInfo.deptId,
});
const status = ref('');
const [loading, run] = useAutoLoading(false);
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
//树结构--操作
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
    if (treeAct.value) {
      staticParams.orgId = treeAct.value.id;
    }
  },
  {
    immediate: true,
  }
);
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
function jumpToKET(id?: string, mode?: number, siteID?: string, missionBatch?: string) {
  run(
    jumpToKETApi({
      mode: mode,
      id: id,
      siteID,
      missionBatch,
      deptId: (treeAct.value && treeAct.value.id) || store.userInfo.deptId,
    })
  ).then((res) => {
    console.log(res);
    // 设置你想要跳转到的URL
    const url = res.data;
    window.open(url, '_blank');
  });
}
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v.id);
  treeAct.value = v;
  tableCompRef.value?.setDeptId(v.id);
  staticParams.orgId = v.id;
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}

const cardHandle = (obj: any) => {
  if (obj.action === 'CARDCHANGE') {
    tableCompRef.value?.getTableDataWrap({ cardId: obj.data?.cardId });
  }
};

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();
const cardList = ref<ICardAItem[]>([
  {
    label: '总数量（个）',
    value: 0,
    id: '',
  },
  {
    label: '待命（个）',
    value: 0,
    id: '1',
  },
  {
    label: '起飞准备中（个）',
    value: 0,
    id: '2',
  },
  {
    label: '执飞中（个）',
    value: 0,
    id: '3',
  },
  {
    label: '降落完成中（个）',
    value: 0,
    id: '4',
  },
  {
    label: '降落完成（个）',
    value: 0,
    id: '5',
  },
]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'actionList-----');
  currentAction.value = val;
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(val.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(val.data);
      getTaskStatistics();
      break;
    case ACTION.DETAILS:
      console.log('详情');
      jumpToKET(val.data.id, val.data.mode, val.data.siteId, val.data.missionBatch);
      isShowAside.value = false;
      break;
    case ACTION.EXPORT:
      console.log('导出');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

const getTaskStatistics = () => {
  staticParams.deptId = (treeAct.value && treeAct.value.id) || store.userInfo.deptId;
  run(getTaskStatisticsApi(staticParams)).then((res: any) => {
    if (res.code === 'success') {
      const { sumCount, dm, qfzbz, zfz, jlwcz, jlwc } = res.data;
      cardList.value[0].value = sumCount || 0;
      cardList.value[1].value = dm || 0;
      cardList.value[2].value = qfzbz || 0;
      cardList.value[3].value = zfz || 0;
      cardList.value[4].value = jlwcz || 0;
      cardList.value[5].value = jlwc || 0;
    }
  });
};

onMounted(() => {
  getTaskStatistics();
});

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
