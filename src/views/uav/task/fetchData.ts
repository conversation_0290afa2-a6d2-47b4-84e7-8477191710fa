import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';
import type { IPageDataRes } from './type';

//获取巡检任务
export function getTaskListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.uav.uavTaskList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}

//获取巡检卡片
export function getTaskStatisticsApi(data: any) {
  const param = api.getComParams(api.type.intelligent, api.uav.uavTaskStatistics, data);
  return $http.post<IPageDataRes>(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}

//跳转
export function jumpToKETApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.uav.jumptoket, query);
  return $http.get<string>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
