import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '航线名称',
    key: 'missionName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '工作站名称',
    key: 'siteName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '无人机起飞时间',
    key: 'uavStartTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      return `${_.uavStartTime ? _.uavStartTime : '--'}`;
    },
  },
  {
    title: '无人机降落时间',
    key: 'uavEndTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      return `${_.uavEndTime ? _.uavEndTime : '--'}`;
    },
  },
  {
    title: '任务下发时间',
    key: 'missionDeliveryTime',
    align: 'left',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      return `${_.missionDeliveryTime ? _.missionDeliveryTime : '--'}`;
    },
  },
  {
    title: '完成动作',
    key: 'finishAction',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      let str = '--';
      if (_.finishAction == 1) {
        str = '自动返航';
      } else if (_.finishAction == 4) {
        str = '终点站';
      }
      return str;
    },
  },
  {
    title: '完成状态',
    key: 'state',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      const status = ['待命', '起飞准备中', '执飞中', '降落完成中', '降落完成'];
      return status[_.state - 1];
    },
  },
  {
    title: '飞行距离',
    key: 'landSpot',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (_: any) => {
      return _.uavFlightMileage ? _.uavFlightMileage + 'm' : '--';
    },
  },
];
