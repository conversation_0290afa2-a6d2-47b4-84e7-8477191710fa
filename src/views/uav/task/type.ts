import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

export interface IJumpData {
  code: string;
  data: string;
  dataType: string;
  message: string;
  status: string;
  token: string;
}

export interface IDetail {
  areaCode: string;
  missionId: string;
  missionName: string;
  siteId: string;
  siteName: string;
  enabled: string;
  CMID: string;
  id: string;
  excuteTime: string;
  isExcute: string;
  isDisabled: string;
  createTime: string;
  updateTime: string;
  planProgress: string;
  abnormalNum: string;
}

export type IPageDataRes = IPageRes<IDetail>;

export type ITgeType = 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export type IProgressStatus = 'default' | 'success' | 'error' | 'warning' | 'info';
