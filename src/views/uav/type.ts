import type { IObj, IPageRes } from '@/types';

//tab 数据
export interface ITabData {
  text: string;
  value: string;
}

//taskList
export interface taskListType {
  text: string;
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EDIT = 'EDIT',
  MORE = 'MORE',
  ADD = 'ADD',
  DEL = 'DEL',
  USING = 'USING',
  STOPUSING = 'STOPUSING',
  TREECHANGE = 'TREECHANGE',
  CARDCHANGE = 'CARDCHANGE',
}
