<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="!treeLoading">
      <!-- <com-filter class="com-table-filter" @action="actionFn" @searchItem="searchItem" /> -->
      <div class="layer-strenth" v-if="store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <div class="layer-cont">
        <com-card-f @gotoDetail="gotoDetail" :treeName="treeName" :equipmentList="equipmentList"></com-card-f>

        <div :class="$style['page_container']" v-if="deviceArr.length">
          <n-pagination
            :item-count="deviceArr.length"
            size="medium"
            :page="currentPage"
            :page-size="pageSize"
            :on-update:page="updatePage"
          />
        </div>
      </div>
    </div>
    <ComCardE ref="ComCardCRef" :deviceDetail="deviceDetail" />
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, onMounted, provide, Ref, ref, watch, reactive } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComCardF from '@/components/card/ComCardF.vue';
import ComCardE from '@/components/card/ComCardE.vue';
import ComFilter from './comp/Filter.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { postDeviceListApi, postDeviceDetailApi } from './fetchData';
import { UavICardBItem } from '@/components/card/type';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import type { IActionData, DeviceListType, DeviceDetailType } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import strengthImage from '@/assets/strenth.png';
const currentPage = ref<number>(1);
const ComCardCRef = ref<InstanceType<typeof ComCardC>>();
const [loading, run] = useAutoLoading(false);
const deviceArr = ref([]);
const equipmentList = ref([]);
import { useStore } from '@/store';
const store = useStore();
const params = reactive({
  uavId: '',
  uavName: '',
  deptId: store.userInfo.orgCode,
  page: 1,
  size: -1,
});
const pageSize = 12;
//树结构--操作
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
let treeName: any = ref('');
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
    treeName.value = list[0]?.treeName;
    postDeviceList();
  },
  {
    immediate: true,
  }
);
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  treeName.value = v.treeName;
  params.deptId = v.id;
  postDeviceList();
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}

let deviceDetail = ref<DeviceDetailType[]>([]);
// 设备列表
function postDeviceList() {
  run(postDeviceListApi(params)).then((res: any) => {
    const { hive, device } = res.data;
    deviceArr.value = [...hive, ...device];
    if (deviceArr.value.length >= pageSize) {
      equipmentList.value = deviceArr.value.slice(0, pageSize);
    } else {
      equipmentList.value = deviceArr.value;
    }
  });
}

function gotoDetail(obj: any) {
  console.log('list 传过来的 value 是', obj);
  ComCardCRef.value?.init();
  deviceDetail.value = obj;
  // run(postDeviceDetailApi({ id })).then((res: any) => {
  //   deviceDetail.value = res.data;
  //   ComCardCRef.value?.init();
  // });
}
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();

const updatePage = (page: number) => {
  currentPage.value = page;
  console.log(
    deviceArr.value.length,
    (page - 1) * pageSize,
    page * pageSize,
    deviceArr.value.length > (page - 1) * pageSize
  );
  if (deviceArr.value.length > (page - 1) * pageSize) {
    console.log(1);
    equipmentList.value = deviceArr.value.slice((page - 1) * pageSize, page * pageSize);
  } else {
    console.log(2);
    equipmentList.value = [];
  }

  console.log(equipmentList.value, deviceArr.value, 'equipmentList');

  // postDeviceList();
};

const searchItem = (data: any) => {
  const { uavId, uavName } = data;
  params.uavId = uavId;
  params.uavName = uavName;
  currentPage.value = 1;
  params.page = 1;
  postDeviceList();
};

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索', currentAction.value.data);
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.EDIT:
      console.log('详情');
      isShowAside.value = true;
      break;
    case ACTION.VIDEO:
      console.log('查看视频');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

onMounted(() => {
  // 获取设备列表
  postDeviceList();
});

defineOptions({ name: 'VideoEquiIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
  background-color: #eef7ff;
}

.page_container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding: 20px;
  margin: 10px auto;
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  right: 0;
}
</style>
