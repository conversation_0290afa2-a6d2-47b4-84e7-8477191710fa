import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';
import type { IPageDataRes } from './type';

//获取巡检计划
export function getPlanListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.uav.uavPlanList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}

//跳转
export function jumpToPlanAPI(query: any) {
  const url = api.getUrl(api.type.intelligent, api.uav.jumpToPlan, query);
  return $http.get<string>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

export function uavPost(data: any) {
  const param = api.getComParams(api.type.uavPost, api.uav.uavJumt, data);
  console.log('paramparam', JSON.stringify(param));
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}
