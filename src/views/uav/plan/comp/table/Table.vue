<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IButType, IDetail } from '../../type';
import { ACTION, ACTION_LABEL, TASK_STATE } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, VNode, defineEmits, reactive } from 'vue';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { getPlanListApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { paramsFn } from '@/views/common/utils.ts';
import { useStore } from '@/store';

const store = useStore();
const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const filterData = reactive<any>({
  orgId: store.userInfo.orgCode,
  deptId: store.userInfo.orgCode,
  planName: '',
  page: 1,
  size: 10,
});

function getTableData() {
  // filterData.orgId = store.treeAct?.id ? store.treeAct?.id : store.userInfo?.orgCode;
  filterData.page = pagination.page;
  search(getPlanListApi(filterData)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  if (data.v1 !== undefined) {
    filterData.planName = data.v1;
  }
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  const stateT = ['未生效', '生效中', '已生效'];
  const butTColor = ['#FA5151', '#527CFF', '#00B578'];

  //状态栏
  columns.value.push({
    title: '任务状态',
    key: 'state',
    align: 'left',
    width: 150,
    render(row: any) {
      const index = row.state as number;
      return h(
        NButton,
        {
          size: 'small',
          color: butTColor[index - 1],
        },
        stateT[index - 1]
      );
    },
  });

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: { mode: 2, id: row.missionId } }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
}
function setDeptId(id: string) {
  filterData.orgId = id;
  filterData.deptId = id;
}
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
  setDeptId,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
