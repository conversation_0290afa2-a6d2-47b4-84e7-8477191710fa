import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '航线名称',
    key: 'missionName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '工作站名称',
    key: 'siteName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '开始日期',
    key: 'startDateValue',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '结束日期',
    key: 'endDateValue',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '执行模式',
    key: 'mode',
    align: 'left',
    render(row: any) {
      const arr = ['单次', '每天', '每周', '每月'];
      return arr[row.mode - 1];
    },
    ellipsis: {
      tooltip: true,
    },
  },
];
