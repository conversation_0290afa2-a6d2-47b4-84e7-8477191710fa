import type { TreeOption } from 'naive-ui';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import type { taskListType } from './type';

// 组织机构管理
// export function postTreeList(data: { needChildUnit: string; needself: string; orgCode: string }) {
//   const url = api.getUrl(api.name.intelligent.treeList);
//   return $http.post<ITreeData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }
export function postTreeList(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.treeList, query);
  return $http.get<TreeOption[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// export function postTaskListApi(query: IObj<any>) {
//   const url = api.getUrl(api.type.intelligent, api.name.task.task, query);
//   return $http.get<taskListType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
// }
