// import type { AbnormalList } from './type';
import type { TreeOption } from 'naive-ui';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 组织机构管理;
export function postTreeList(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.treeList, query);
  return $http.get<TreeOption[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// //异常列表
// export function postAbnormalList(query: IObj<any>) {
//   const url = api.getUrl(api.robot.abnormal, query);
//   return $http.get<AbnormalList[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
// }
