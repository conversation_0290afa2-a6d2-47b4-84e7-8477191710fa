<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="!treeLoading">
      <div class="layer-strenth" v-if="store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <com-card :list="cardList" :act="status" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
      <risk />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import risk from './comp/risk/risk.vue';
import ComCard from '@/components/card/ComCardA.vue';
import { jumpToqtApi, taskRecordApi } from './fetchData';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData, TaskPlanListType } from './type';
import type { ICardAItem } from '@/components/card/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useStore } from '@/store';
import strengthImage from '@/assets/strenth.png';
const store = useStore();
console.log('store-=-=-=-=-=-=-=', store);
const status = ref('');
const [loading, run] = useAutoLoading(false);
const prams = ref({
  mode: '3',
  deptId: store.userInfo.deptId,
});
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
//树结构--操作
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
    if (treeAct.value) {
      listRecord(treeAct.value.id);
    }
  },
  {
    immediate: true,
  }
);
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
function jumpToqt() {
  run(jumpToqtApi(prams.value)).then((res: any) => {
    window.open(res.data);
  });
}
function listRecord(id?: string) {
  const params = {
    deptId: id || '',
  };
  taskRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.total;
    cardList.value[1].value = res.data.executing;
    cardList.value[2].value = res.data.stopped;
    cardList.value[3].value = res.data.finished;
  });
}
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  tableCompRef.value?.setDeptId(v.id);
  prams.value.deptId = v.id;
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);
const isShowAside = ref(false);
const tableCompRef = ref();
const cardList = ref<ICardAItem[]>([
  {
    label: '总数量（个）',
    value: 0,
    id: '',
  },
  {
    label: '正在执行（个）',
    value: 0,
    id: 'EXECUTING',
  },
  {
    label: '终止（个）',
    value: 0,
    id: 'STOPPED',
  },
  {
    label: '完成（个）',
    value: 0,
    id: 'FINISHED',
  },
]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'actionList-----');
  // currentAction.value = val;
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    taskState: status.value == 0 ? '' : status.value,
    deptId: treeAct.value?.id || store.userInfo.deptId,
    deptName: treeAct.value?.text || '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      listRecord(currentAction.value.data.deptId);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      jumpToqt();
      isShowAside.value = false;
      break;
    case ACTION.EXPORT:
      console.log('导出');
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片切换筛选');
      status.value = val.data.cardId;
      currentAction.value.data.status = val.data.cardId == 0 ? '' : val.data.cardId;
      console.log('currentAction.value.data', JSON.stringify(currentAction.value.data));
      handleSearch(currentAction.value.data);
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  console.log('巡检任务', JSON.stringify(data));
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
