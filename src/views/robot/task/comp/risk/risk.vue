<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <n-modal class="n-modal" v-model:show="showModal" transform-origin="center">
    <n-card
      style="width: 600px; height: 100vh; position: fixed; top: 0px; right: 0px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
      :class="$style['n-card']"
    >
      <div>
        <div :class="$style['header']">
          <div :class="$style['left']">
            <n-image width="25" height="24" :src="detailTop" />
            <p>新增隐患</p>
          </div>
          <div :class="$style['right']">
            <n-image width="25" height="24" :src="close" />
          </div>
        </div>
        <div :class="$style['planItemWrap']">
          <div :class="$style['planItem']">
            <p><span>*</span>隐患来源</p>
            <div :class="$style['div']" v-if="riskType == 1">
              <n-space>
                <n-select v-model:value="planType" :options="planTypeOptions" />
              </n-space>
            </div>
            <div :class="$style['div']" v-if="riskType == 2">
              <n-input type="text" placeholder="请输入" v-model:value="planType" />
            </div>
          </div>
          <div :class="$style['planItem']">
            <p><span>*</span>上报设备</p>
            <div :class="$style['div']" v-if="riskType == 1">
              <n-space>
                <n-select v-model:value="planType" :options="planTypeOptions" />
              </n-space>
            </div>
            <div :class="$style['div']" v-if="riskType == 2">
              <n-input type="text" placeholder="请输入" v-model:value="planType" />
            </div>
          </div>
          <div :class="$style['planItem']">
            <p><span>*</span>上报时间</p>
            <n-date-picker
              v-model:formatted-value="planDate"
              value-format="yyyy.MM.dd HH:mm:ss"
              type="date"
              clearable
            />
          </div>
          <div :class="$style['planItem1']">
            <p><span>*</span>巡检结果</p>
            <div>
              <n-space>
                <n-radio
                  :checked="checkedValue === '全部正常'"
                  value="全部正常"
                  name="basic-demo"
                  @change="handleChange"
                >
                  全部正常
                </n-radio>
                <n-radio :checked="checkedValue === '有异常'" value="有异常" name="basic-demo" @change="handleChange">
                  有异常
                </n-radio>
              </n-space>
            </div>
          </div>
          <div :class="$style['planItem']">
            <p><span>*</span>隐患位置</p>
            <div>
              <n-input type="text" placeholder="请输入" v-model:value="planApply" />
            </div>
          </div>
          <div :class="$style['planItem']">
            <p><span>*</span>隐患描述</p>
            <div>
              <n-input type="text" placeholder="请输入" v-model:value="planApply" />
            </div>
          </div>
          <div :class="$style['planItem1']">
            <p><span>*</span>隐患等级</p>
            <div>
              <n-space>
                <n-radio
                  :checked="checkedValue1 === '一般隐患'"
                  value="一般隐患"
                  name="basic-demo"
                  @change="handleChange1"
                >
                  一般隐患
                </n-radio>
                <n-radio
                  :checked="checkedValue1 === '重大隐患'"
                  value="重大隐患"
                  name="basic-demo"
                  @change="handleChange1"
                >
                  重大隐患
                </n-radio>
              </n-space>
            </div>
          </div>
          <div :class="$style['upLoad']">
            <p><span>*</span>现场照片</p>
          </div>
          <div>
            <n-divider />
            <n-upload
              action="https://www.mocky.io/v2/5e4bafc63100007100d8b70f"
              list-type="image-card"
              @preview="handlePreview"
            />
          </div>
        </div>
      </div>
    </n-card>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, watch, defineComponent } from 'vue';
// import { postDeviceList, postDeviceDetail, postDevicePowerl } from '../fetchData';
// import type { DeviceListType, DeviceDetailType, DevicePowerType } from '../type';
import type { UploadFileInfo } from 'naive-ui';
import detailTop from './assets/detailTop.png';
import close from './assets/close.png';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

const props = defineProps({
  riskType: {
    type: Number,
    default: 1,
  },
});
const [loading, run] = useAutoLoading(false);
let showModal = ref(false);
let planTypeOptions = [
  {
    label: '滨海湾金沙，新加坡',
    value: 'marina bay sands',
  },
  {
    label: '布朗酒店，伦敦',
    value: "brown's hotel, london",
  },
  {
    label: '亚特兰蒂斯巴哈马，拿骚',
    value: 'atlantis nahamas, nassau',
  },
  {
    label: '比佛利山庄酒店，洛杉矶',
    value: 'the beverly hills hotel, los angeles',
  },
];
let planExplain = ref('');
let planExplainOptions = [
  {
    label: '滨海湾金沙，新加坡',
    key: 'marina bay sands',
  },
  {
    label: '布朗酒店，伦敦',
    key: "brown's hotel, london",
  },
  {
    label: '亚特兰蒂斯巴哈马，拿骚',
    key: 'atlantis nahamas, nassau',
  },
  {
    label: '比佛利山庄酒店，洛杉矶',
    key: 'the beverly hills hotel, los angeles',
  },
];
let planApply = ref('');
let planApplyOptions = [
  {
    label: '滨海湾金沙，新加坡',
    key: 'marina bay sands',
  },
  {
    label: '布朗酒店，伦敦',
    key: "brown's hotel, london",
  },
  {
    label: '亚特兰蒂斯巴哈马，拿骚',
    key: 'atlantis nahamas, nassau',
  },
  {
    label: '比佛利山庄酒店，洛杉矶',
    key: 'the beverly hills hotel, los angeles',
  },
];
function handleSelect(key: string | number) {
  console.log(String(key));
}
function handleSelect2(key: string | number) {
  console.log(String(key));
}
let planType = ref('');
let planDate = ref('2007.06.30 12:08:55');
let planFile = ref('');
let checkedValue = ref('');
let checkedValueRef = ref<string | null>(null);
function handleChange(e: Event) {
  checkedValue.value = (e.target as HTMLInputElement).value;
}

let checkedValue1 = ref('');
let checkedValueRef1 = ref<string | null>(null);
function handleChange1(e: Event) {
  checkedValue1.value = (e.target as HTMLInputElement).value;
  console.log('checkedValue1', checkedValue1.value);
}

const showModalRef = ref(false);
const previewImageUrlRef = ref('');
function handlePreview(file: UploadFileInfo) {
  const { url } = file;
  previewImageUrlRef.value = url as string;
  showModalRef.value = true;
}
</script>

<style module lang="scss">
.n-card :global(.n-card__content) {
  padding: 0 !important;
  width: 520px !important;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
    padding: 15px 25px;
    .left {
      display: flex;
      align-items: center;
      p {
        margin-left: 8px;
      }
    }
  }
  .planItemWrap {
    padding: 20px 24px 0 24px;
    .planItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;
      position: relative;
      p {
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 22px;
        display: flex;
        align-items: center;
        span {
          font-size: 14px;
          color: #fa5151;
          line-height: 20px;
          padding-top: 4px;
          padding-right: 4px;
        }
      }
      div {
        width: 395px;
        height: 32px;
      }
      :global(.n-base-icon) {
        display: none !important;
      }
    }
    .planItem1 {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;
      position: relative;
      p {
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 22px;
        display: flex;
        align-items: center;
        margin-right: 12px;
        span {
          font-size: 14px;
          color: #fa5151;
          line-height: 20px;
          padding-top: 4px;
          padding-right: 4px;
        }
      }
    }
    .upLoad {
      display: flex;
      align-items: flex-start;
      padding-bottom: 21px;
      p {
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 22px;
        display: flex;
        align-items: center;
        span {
          font-size: 14px;
          color: #fa5151;
          line-height: 20px;
          padding-top: 4px;
          padding-right: 4px;
        }
      }
      div {
        width: 298px;
        height: 32px;
        margin-left: 3px;
        margin-right: 20px;
      }
    }
    .risk {
      display: flex;
      align-items: center;
      padding-bottom: 21px;
      margin-top: 18px;
      .p1 {
        margin-right: 12px;
      }
      .p2 {
        margin-left: 12px;
      }
    }
  }
}
</style>
