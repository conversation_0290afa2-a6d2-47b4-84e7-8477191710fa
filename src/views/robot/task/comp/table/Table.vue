<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IPageData, IProgressStatus, IButType, IActionData, IDetail } from '../../type';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, toRaw, VNode, inject, Ref, PropType } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import type { TaskPlanListType } from '../../type';
// import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { postTaskListApi, taskDetailApi } from '../../fetchData';
import { useStore } from '@/store';
const store = useStore();
const emits = defineEmits(['action']);
const [loading, run] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([]);
// const tableData = ref<IDetail[]>([
//   {
//     planName: '机器人自动巡检',
//     deptName: '延899采气大队',
//     taskPlanStartTime: '2024-10-01',
//     taskStartTime: '2024-10-01',
//     taskEndTime: '2024-10-09',
//     recordPoint: '',
//     planProgress: '',
//     isExcute: 1,
//   },
//   {
//     planName: '机器人自动巡检',
//     deptName: '延819采气大队',
//     taskPlanStartTime: '2024-10-01',
//     taskStartTime: '2024-10-01',
//     taskEndTime: '2024-10-09',
//     recordPoint: '',
//     planProgress: '',
//     isExcute: 1,
//   },
//   {
//     planName: '机器人自动巡检',
//     deptName: '延879采气大队',
//     taskPlanStartTime: '2024-10-01',
//     taskStartTime: '2024-10-01',
//     taskEndTime: '2024-10-09',
//     recordPoint: '',
//     planProgress: '',
//     isExcute: 1,
//   },
// ]);
// tableData.value = [{ planName: '11' }];
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
// const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const params = {
  pageNo: pagination.page,
  pageSize: pagination.pageSize,
  // createdBy: '', //创建人
  // createTime: '', //创建时间
  // delFlag: 1, //逻辑删除标志1-是，0-否
  deptId: store.userInfo.deptId, //所属单位
  // deptName: '', //所属单位名称
  // id: '', //主键
  // planId: '', //计划ID
  // planName: '', //计划名称
  // recordPoint: '', //巡检点总数量/已巡数量/未巡检点
  // taskEndTime: '', //任务实际结束时间
  // askPlanEndTime: '', //任务计划结束时间
  // taskPlanStartTime: '', //任务计划开始时间
  // taskStartTime: '', //任务实际开始时间
  // taskStatus: '', //任务状态 0：待开始，1：未执行，2：进行中，3，已完成
  // updatedBy: '', //修改人
  // updateTime: '', //修改时间
  // zhId: '', //租户id
  // ...filterData,
};
function getTableData() {
  // const queryParams = {...params,...filterData}
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  console.log('getTableData', JSON.stringify(params));
  run(postTaskListApi(params)).then((res) => {
    console.log('robot = ', res);
    // tableData.value.push();
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  console.log('data', JSON.stringify(data));
  filterData = Object.assign({}, paramsFn(data)) || {};
  console.log('filterData', JSON.stringify(filterData));
  pagination.page = 1;
  getTableData();
}
//参数筛选
function paramsFn(data: IObj<any>) {
  const param = ['pageNo', 'pageSize', 'status', 'deptId', 'patrolTaskName'];
  let curData = { ...data };
  //Object.keys(data).forEach
  for (let key in data) {
    if (!param.includes(key)) {
      delete curData[key];
    }
  }
  console.log('curData', JSON.stringify(curData));
  return curData;
}
function setColumns() {
  columns.value.push(...cols);
  //const tagType: ITgeType[] = ['default', 'primary', 'info', 'success', 'warning', 'error'];
  //任务状态 0：待开始，1：未执行，2：进行中，3，已完成
  const stateT = ['完成', '生效中', '已执行'];
  const butTColor = ['#FA5151', '#527CFF', '#00B578'];
  const proStatus: IProgressStatus[] = ['default', 'success', 'error', 'warning', 'info'];
  //状态栏
  columns.value.push({
    title: '执行结果',
    key: 'status',
    align: 'left',
    width: 120,
    render(row) {
      if (row.status == 'FINISHED') return '完成';
      if (row.status == 'STOPPED') return '终止';
      if (row.status == 'EXECUTING') return '正在执行';
    },
  });

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
  //加线
  //return useActionDivider(acList);
}

// on created
setColumns();
function setDeptId(id: string) {
  params.deptId = id;
}

defineExpose({
  getTableDataWrap,
  getTableData,
  setDeptId,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
