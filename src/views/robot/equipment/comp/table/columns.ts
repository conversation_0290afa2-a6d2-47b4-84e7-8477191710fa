import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '设备编号',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '设备位置',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '设备品牌',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '设备状态',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '累计巡检任务数',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '累计发现隐患数',
    key: 'areaCode',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
