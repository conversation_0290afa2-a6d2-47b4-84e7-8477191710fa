<template>
  <n-form :show-feedback="false" label-placement="left" style="background-color: #eef7ff">
    <n-flex :size="[20, 10]">
      <n-form-item label="设备名称:">
        <n-input placeholder="请输入" v-model:value="filterForm.deviceName" clearable class="!w-[260px]" />
      </n-form-item>
      <n-form-item label="设备编号:">
        <n-input placeholder="请输入" v-model:value="filterForm.deviceId" clearable class="!w-[260px]" />
      </n-form-item>
      <!-- <n-form-item label="设备状态:">
        <n-select
          class="!w-[260px]"
          v-model:value="filterForm.id1"
          :options="unitOptions"
          placeholder="请输入"
          label-field="unitName"
          value-field="id"
          :loading="loading"
          clearable
          filterable
          remote
          @search="searchUnit"
        />
      </n-form-item> -->
      <!-- <img src="@/assets/jiqiren.png" alt="" /> -->
      <!-- <div style="margin-left: auto">
        <n-button type="primary" @click="doHandle(ACTION.EXPORT)">
          {{ ACTION_LABEL.EXPORT }}
        </n-button>
      </div> -->
    </n-flex>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);

function initForm() {
  return {
    deviceName: '',
    deviceId: '',
    id1: null,
    id2: null,
  };
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function searchUnit(value: string) {}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'VideoEquiFilter' });
</script>

<style module lang="scss"></style>
