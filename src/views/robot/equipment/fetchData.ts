import type { IDetail, IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import type { DeviceListType, DeviceDetailType, DevicePowerType } from './type';

//获取设备列表
export function postDeviceListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.deviceList, query);
  return $http.get<DeviceListType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//获取设备详情
export function postDeviceDetailApi(query: any) {
  const url = api.getUrl(api.type.intelligent, api.robot.deviceDetail, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//获取充电房详情
export function postDevicePowerlApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.devicePower, query);
  return $http.get<DevicePowerType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
//获取气象站数据
export function postWeatherStationApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.weatherStation, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
