import { ACTION } from './constant';
import type { IObj } from '@/types';

export interface TaskListType {
  //入参
  appno: string;
  beginDate: string;
  businessType: string; //requiered
  deptId: string;
  deviceId: string;
  endDate: string;
  mapName: string;
  pageNo: string;
  pageSize: string;
  patrolTaskName: string;
  projectId: string;
  resultStates: string;
  status: string;
  userId: string; //requiered
}

export interface TaskPlanListType {
  //返参
  appno: string;
  endTime: string;
  id: string;
  mapName: string;
  patrolPointTotal: string;
  patrolTaskId: string;
  patrolTaskName: string;
  resultStates: string;
  robot: string;
  startTime: string;
  status: string;
  sysProjectName: string;
  taskStartTime: string;
  updateDate: string;
  rows: [];
}

export interface jumpToqtType {
  code: string;
  data: string;
  dataType: string;
  message: any;
  status: string;
  token: string;
}

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}
export interface IPageData {
  current: number;
  rows: IDetail[];
  total: number;
}
export interface IDetail {
  areaCode: string;
}
export type ITgeType = 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export type IProgressStatus = 'default' | 'success' | 'error' | 'warning' | 'info';
