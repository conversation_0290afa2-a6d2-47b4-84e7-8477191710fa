import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'planName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'deptName',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划开始日期',
    key: 'taskPlanStartTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '实际开始日期',
    key: 'taskStartTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '实际结束日期',
    key: 'taskEndTime',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '发现异常数量',
    key: 'recordPoint',
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
];
