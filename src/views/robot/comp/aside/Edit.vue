<template>
  <div :class="$style.wrap">
    <n-spin :show="loading" :delay="500">
      <p><span :class="$style['red']">*</span>请选择该单位管辖范围：</p>
      <p>...</p>

      <p :class="$style['red']">注：默认全选，如去掉某类勾选，系统将不再接收该单位对应数据，请谨慎选择。</p>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import type { IActionData } from '../../type';
import { computed, inject, ref, Ref } from 'vue';
import { getDetail, postUpdate } from '../../fetchData';
import { PROVIDE_KEY } from '../../constant';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

const emits = defineEmits(['submitted']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const actionData = computed(() => currentAction.value.data);
const [loading, run] = useAutoLoading(true);
const jurChecked = ref<string[]>([]);

function getData() {
  const id = actionData.value.id;

  if (id) {
    run(getDetail(id)).then((res) => {
      const jurStr = res.data?.jurisdiction || '';
      jurChecked.value = jurStr ? jurStr.split(',') : [];
    });
  }
}

function handleSubmit() {
  const params = {
    id: actionData.value.id,
    jurisdiction: jurChecked.value.join(','),
  };

  run(postUpdate(params)).then(() => {
    emits('submitted');
  });
}

// init
getData();

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'DemoJurisdictionEdit' });
</script>

<style module lang="scss">
.wrap {
  padding: 24px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: 20px;
  padding: 0 10px;
  margin: 20px 0 30px;
  min-height: 107px;
}

.red {
  color: #a30014;
}
</style>
