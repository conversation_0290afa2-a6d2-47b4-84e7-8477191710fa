import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import type { PlanListType, jumpToqtType } from './type';

// 组织机构管理;
// export function postTreeList(data: { needChildUnit: string; needself: string; orgCode: string }) {
//   const url = api.getUrl(api.type.intelligent, api.name.intelligent.treeList);
//   return $http.post<ITreeData>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }

//获取巡检任务
export function postPlanListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.plan, query);
  return $http.get<PlanListType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//跳转
export function jumpToqtApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.jumptoqt, query);
  return $http.get<jumpToqtType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
//新建计划跳转
export function jumpToOssApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.jumptoqt, query);
  return $http.get<jumpToqtType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
