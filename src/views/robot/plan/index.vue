<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri">
      <div class="layer-strenth" v-if="store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" :planlist="planlist" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import ComCard from '@/components/card/ComCardA.vue';
import type { ICardAItem } from '@/components/card/type';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { jumpToqtApi, jumpToOssApi } from './fetchData';
import type { PlanListType, PostPlanListType, jumpToqtType } from './type';
import { useStore } from '@/store';
import strengthImage from '@/assets/strenth.png';
const store = useStore();
let planlist = ref<PlanListType[]>([]);
let postPlanListPrams = ref<PostPlanListType>();
const [loading, run] = useAutoLoading(false);
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
});
const prams = ref({
  mode: '',
  deptId: store.userInfo.deptId,
});
// postPlanListPrams.value = {
//   deviceInfoId: '',
//   pageNo: 1,
//   pageSize: 10,
//   status: '',
//   sysProjectId: '',
//   taskName: '',
//   taskStatus: '',
//   taskType: '',
//   userId: '',
//   deptId: '0dedd98538d448df826e5066830b276a',
// };
// function planList() {
//   run(postPlanList(postPlanListPrams.value)).then((res) => {
//     console.log(res);
//     planlist.value = res.data.records;
//   });
// }
// planList();

// function JumpToqt() {
// const JumpToqtPrams = {
//   id: '110900723',
//   mode: 2,
// };
//   run(jumpToqt(JumpToqtPrams)).then((res) => {
//     console.log(res);
//   });
// }
// JumpToqt();
//树结构--操作
const treeData = computed(() => {
  let list = props.treeList as TreeOption[];
  return list;
});
const treeAct = ref();
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  // tableCompRef.value?.setDeviceID(v.id);
  prams.value.deptId = v.id;
  actionFn({ action: ACTION.TREECHANGE, data: {} });
}
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'actionList-----');
  // currentAction.value = val;
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id || store.userInfo.deptId,
    deptName: treeAct.value?.text || '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      jumpToqt();
      isShowAside.value = false;
      break;
    case ACTION.EDIT:
      console.log('编辑');
      isShowAside.value = true;
      break;
    case ACTION.ADD:
      console.log('添加');
      jumpToOss();
      break;
    case ACTION.DEL:
      console.log('删除');
      break;
    case ACTION.USING:
      console.log('启用');
      break;
    case ACTION.STOPUSING:
      console.log('停用');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
function jumpToqt() {
  prams.value.mode = '2';
  run(jumpToqtApi(prams.value)).then((res: any) => {
    window.open(res.data);
  });
}
function jumpToOss() {
  prams.value.mode = '1';
  run(jumpToOssApi(prams.value)).then((res: any) => {
    window.open(res.data);
  });
}
defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
