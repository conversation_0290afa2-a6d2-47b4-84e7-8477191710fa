<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IPageData, IButType } from '../../type';
import { ACTION, ACTION_LABEL } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode } from 'vue';
// import { pageData } from '../../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
// import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import tabMore from './more.vue';
import { postPlanListApi } from '../../fetchData';
import { useStore } from '@/store';
const store = useStore();
const emits = defineEmits(['action']);
const [loading, run] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const stateT = ['待开始', '未执行', '进行中', '已完成'];
const tableData = ref<any>();
interface ITablecolumn {
  id: string;
  planName: string; //计划名称
  unit: string; //所属单位
  startTime: string; //开始日期
  endTime: string; //结束日期
  frequency: string; //巡检频次
  state: string | number; //计划状态
}
// const tableData = ref<ITablecolumn[]>([
//   {
//     id: '',
//     planName: '机器人自动巡检计划',
//     unit: '延899采气大队',
//     startTime: '2024-10-01',
//     endTime: '2024-10-09',
//     frequency: '--',
//     state: 1,
//   },
//   {
//     id: '',
//     planName: '机器人自动巡检计划',
//     unit: '延819采气大队',
//     startTime: '2024-10-01',
//     endTime: '2024-10-09',
//     frequency: '--',
//     state: 1,
//   },
//   {
//     id: '',
//     planName: '机器人自动巡检计划',
//     unit: '延879采气大队',
//     startTime: '2024-10-01',
//     endTime: '2024-10-09',
//     frequency: '--',
//     state: 1,
//   },
// ]);
const { pagination, updateTotal } = useNaivePagination(getTableData);
let filterData: IObj<any> = {}; // 搜索条件

// const params = {
//   pageNo: pagination.page,
//   pageSize: pagination.pageSize,
//   // deptId: store.userInfo.deptId,
//   ...filterData,
// };
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  run(postPlanListApi(params)).then((res: any) => {
    console.log(res);
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
  // updateTotal(20);
}

function getTableDataWrap(data: IObj<any>) {
  console.log('计划的搜索', JSON.stringify(data));
  filterData = Object.assign({}, paramsFn(data)) || {};
  pagination.page = 1;
  getTableData();
}
//参数筛选
function paramsFn(data: IObj<any>) {
  const param = ['pageNo', 'pageSize', 'status', 'deptId', 'taskName'];
  let curData = { ...data };
  //Object.keys(data).forEach
  for (let key in data) {
    if (!param.includes(key)) {
      delete curData[key];
    }
  }
  console.log('curData', JSON.stringify(curData));
  return curData;
}
enum STATUS {
  UNEXECUTED = '未执行',
  EXECUTING = '正在执行',
  PAUSING = '暂停中',
  PAUSED = '暂停',
  STOPPED = '终止',
  FINISHED = '完成',
  RESUME = '恢复',
}
function setColumns() {
  columns.value.push(...cols);
  const butType: IButType[] = ['primary', 'error', 'info', 'success'];
  const butTColor = ['#527CFF', '#FA5151', '#BABBBF', '#00B578'];
  //状态栏
  columns.value.push({
    title: '执行状态',
    key: 'status',
    align: 'left',
    width: 100,
    render(row) {
      // const index = row.state as number;
      // return h(
      //   NButton,
      //   {
      //     type: butType[index],
      //     size: 'small',
      //     color: butTColor[index],
      //   },
      //   stateT[index]
      // );
      if (row.status == 'UNEXECUTED') return '未执行';
      if (row.status == 'EXECUTING') return '正在执行';
      if (row.status == 'PAUSING') return '暂停中';
      if (row.status == 'PAUSED') return '暂停';
      if (row.status == 'STOPPED') return '终止';
      if (row.status == 'FINISHED') return '完成';
      if (row.status == 'RESUME') return '恢复';
    },
  });
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
    // ,
    // [
    //   h(
    //     NButton,
    //     {
    //       color: 'rgba(82,124,255,0.1)',
    //       size: 'small',
    //       disabled: row.state ? true : false,
    //       class: 'com-action-button1',
    //       onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
    //     },
    //     { default: () => ACTION_LABEL.EDIT }
    //   ),
    // ],
    // [
    //   h(tabMore, {
    //     state: row.state,
    //     onSelect: (key: string) => emits('action', { action: key, data: toRaw(row) }),
    //   }),
    // ],
  ];
  return acList;
  //加线
  //return useActionDivider(acList);
}
function setDeviceID(id: string) {
  params.deptId = id;
}
// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
  setDeviceID,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
