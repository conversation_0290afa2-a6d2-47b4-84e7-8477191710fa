import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '任务名称',
    key: 'taskName',
    align: 'left',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务类型',
    key: 'taskType',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if (row.taskType == 'IMMEDIATELY') return '立即执行';
      if (row.taskType == 'CYCLE') return '周期执行';
      if (row.taskType == 'TIMING') return '定时执行';
      if (row.taskType == 'TEMPORARY') return '临时执行';
    },
  },
  {
    title: '执行间隔',
    key: 'intervalTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    render: (row: any) => {
      const intervalUnit = row.intervalUnit == 'HOUR' ? '小时' : row.intervalUnit == 'MINUTE' ? '分钟' : '天';
      // return row.intervalTime + row.intervalUnit == 'HOUR' ? '小时' : row.intervalUnit == 'MINUTE' ? '分钟' : '天';
      if (row.intervalTime) {
        return row.intervalTime + intervalUnit;
      } else {
        return '--';
      }
    },
  },
  {
    title: '执行设备',
    key: 'deviceInfoIdName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '预计开始时间',
    key: 'startTiming',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 180,
  },
  {
    title: '最近执行完成时间',
    key: 'lastTaskFinishedDate',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
    width: 180,
  },
  // {
  //   title: '任务状态',
  //   key: 'taskStatus',
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
  // {
  //   title: '计划状态',
  //   key: 'areaCode5',
  //   align: 'center',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
];
