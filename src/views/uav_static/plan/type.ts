import { ACTION } from './constant';
import type { IObj } from '@/types';
import { IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

export interface IDetail {
  areaCode: string;
  missionId: string;
  missionName: string;
  siteId: string;
  siteName: string;
  enabled: string;
  CMID: string;
  state: string;
  startDate: string;
  endDate: string;
  mode: number;
  min: number;
  hour: number;
  day: number[];
  weekDay: number[];
}

export type IPageDataRes = IPageRes<IDetail>;
export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';
