<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" style="width: 327px">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri">
      <com-filter class="com-table-filter" @action="actionFn" />
      <div class="layer-cont">
        <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { uavPost } from './fetchData';
import type { IActionData } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
// import axios from 'axios';

const [loading, run] = useAutoLoading(false);
function jumpToKET(id?: string, mode?: number) {
  // run(jumpToKETApi({ mode: mode, id: id })).then((res) => {
  //   // 设置你想要跳转到的URL
  //   const url = res.data;
  //   window.open(url, '_blank');
  // });
}

const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
});
//树结构--操作
const treeData = computed(() => {
  let list = props.treeList as TreeOption[];
  return list;
});
const treeAct = ref();
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  actionFn({ action: ACTION.TREECHANGE, data: {} });
}

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'actionList-----');
  currentAction.value = val;
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(val.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(val.data);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      jumpToKET(val.data.id, val.data.mode);
      isShowAside.value = false;
      break;
    case ACTION.EDIT:
      console.log('编辑');
      isShowAside.value = true;
      break;
    case ACTION.ADD:
      console.log('添加---');
      jumpToUav();
      // jumpToKET('1', 1);
      break;
    case ACTION.DEL:
      console.log('删除');
      break;
    case ACTION.USING:
      console.log('启用');
      break;
    case ACTION.STOPUSING:
      console.log('停用');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
function jumpToUav() {
  run(
    uavPost({
      userName: 'test',
    })
  ).then((res: any) => {
    console.log('第一个res', JSON.stringify(res));
    jumpToKET('1', 1);
  });
  // axios({
  //   method: 'post',
  //   url:'http://*************:9099',
  //   headers: {
  //     'Content-Type': 'application/x-www-form-urlencoded'
  //   },
  //   data: {
  //     userName:'test'
  //   },
  // }).then((response:any) => {

  // });
}
defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
