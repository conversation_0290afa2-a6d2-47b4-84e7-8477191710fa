<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IButType, IDetail } from '../../type';
import { ACTION, ACTION_LABEL, TASK_STATE } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, VNode } from 'vue';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { getPlanListApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { paramsFn } from '@/views/common/utils.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([
  {
    id: 1,
    missionName: '无人机巡检计划',
    siteName: '站点1',
    startDateValue: '2024-11-09',
    endDateValue: '2024-11-09',
    mode: 1,
    isExcute: 1,
  },
  {
    id: 2,
    missionName: '无人机巡检计划',
    siteName: '站点2',
    startDateValue: '2024-11-09',
    endDateValue: '2024-11-09',
    mode: 2,
    isExcute: 1,
  },
  {
    id: 3,
    missionName: '无人机巡检计划',
    siteName: '站点3',
    startDateValue: '2024-11-09',
    endDateValue: '2024-11-09',
    mode: 1,
    isExcute: 0,
  },
  {
    id: 4,
    missionName: '无人机巡检计划',
    siteName: '站点4',
    startDateValue: '2024-11-09',
    endDateValue: '2024-11-09',
    mode: 2,
    isExcute: 0,
  },
]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    page: pagination.page,
    size: pagination.pageSize,
    ...filterData,
  };

  // search(getPlanListApi(params)).then((res) => {
  //   tableData.value = res.data.rows || [];
  //   updateTotal(res.data.total || 0);
  // });
}

function getTableDataWrap(data: IObj<any>) {
  filterData =
    Object.assign(
      {},
      paramsFn(data, ['pageNo', 'pageSize', 'taskStatus', 'deptId', 'deptName', 'planName', 'planType'])
    ) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  const butType: IButType[] = ['error', 'success'];
  const butTColor = ['#FA5151', '#00B578'];
  //状态栏
  columns.value.push({
    title: '任务状态',
    key: 'isExcute',
    align: 'left',
    width: 150,
    render(row) {
      const index = row.isExcute as number;
      return h(
        NButton,
        {
          type: butType[index],
          size: 'small',
          color: butTColor[index],
        },
        TASK_STATE.LAB[index]
      );
    },
  });

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: { mode: 2, id: row.missionId } }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
}

setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
