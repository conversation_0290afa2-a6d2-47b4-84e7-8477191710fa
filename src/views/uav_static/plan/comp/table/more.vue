<template>
  <n-dropdown placement="bottom-start" trigger="click" size="small" :options="options" @select="handleSelect">
    <n-button icon-placement="right" size="small" color="rgba(82,124,255,0.1)" class="com-action-button1">
      <template #icon>
        <CdChevronRight />
      </template>
      {{ ACTION_LABEL.MORE }}
    </n-button>
  </n-dropdown>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue';
import { h, ref, toRaw, VNode, inject, Ref, computed } from 'vue';
import { CdChevronRight } from '@kalimahapps/vue-icons';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from '../../constant';
import { IActionData } from '../../type';
const emits = defineEmits(['select']);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject

const props = defineProps({
  state: {
    type: Boolean,
    default: true,
  },
});

const options = computed(() => {
  //const state = currentAction.value.data.areaCode;
  let opt = [
    {
      label: props.state ? ACTION_LABEL.STOPUSING : ACTION_LABEL.USING,
      key: props.state ? ACTION.STOPUSING : ACTION.USING,
    },
    {
      label: ACTION_LABEL.DEL,
      key: ACTION.DEL,
    },
  ];
  return opt;
});
function handleSelect(key: string) {
  console.log(key, 'more-sel------');
  emits('select', key);
}

defineComponent({ name: 'PlanTabMore' });
</script>

<style module lang="scss"></style>
