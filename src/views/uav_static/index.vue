<template>
  <div :class="{ [$style.videoWrap]: true }">
    <com-bread :data="breadData"></com-bread>
    <com-tab @tab-action="tabChange" :tab="tabAct" :tab-list="tabData"></com-tab>
    <div :class="{ [$style.container]: true }">
      <equipment v-if="tabAct === 'equipment'" :tree-list="treeData" :tree-loading="loading"></equipment>
      <plan v-if="tabAct === 'plan'" :tree-list="treeData" :tree-loading="loading"></plan>
      <task v-if="tabAct === 'task'" :tree-list="treeData" :tree-loading="loading"></task>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref, onMounted } from 'vue';
import ComTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import equipment from './equipment/index.vue';
import plan from './plan/index.vue';
import task from './task/index.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { tabData } from './setData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);
import { postTreeList } from '../common/fetchData';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();
const breadData: IBreadData[] = [{ name: '智能巡检' }, { name: '无人机演示' }];
const tabAct = ref('');
import { useStore } from '@/store';
const store = useStore();
tabAct.value = route.query.tab ? (route.query.tab as string) : 'task';

function tabChange(v: string) {
  tabAct.value = v;
  router.replace(`uav-static?tab=${v}`);
}

const treeParam = ref({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  // orgCode: '10000',
  orgCode: store.userInfo.orgCode, //机构id=10000,顶级是-1
  // //机构来源，1，内部 2 外部
  // orgRes: 1,
  // //去除的机构部门id,(下级,本身)
  // removeOrgCode: '',
});
// const loading1 = ref(true);
const treeData = ref<TreeOption[]>([]);
function treeList() {
  run(postTreeList(treeParam.value))
    .then((res) => {
      treeData.value = res.data || [];
    })
    .catch(() => {});
  // setTimeout(() => {
  //   loading1.value = false;
  //   treeData.value = [
  //     {
  //       id: '10000',
  //       text: '10000',
  //       state: '10000',
  //       checked: false,
  //       attributes: {},
  //       type: '10000',
  //       typeId: '10000',
  //       children: [
  //         {
  //           id: '10001',
  //           text: '10000',
  //           state: '10000',
  //           checked: false,
  //           attributes: {},
  //           type: '10000',
  //           typeId: '10000',
  //           children: [],
  //           parentId: '10000',
  //           level: 2,
  //           treeName: '10000',
  //           hasParent: false,
  //           hasChildren: false,
  //           label: '10000',
  //           key: '10000',
  //         },
  //       ],
  //       parentId: '10000',
  //       level: 1,
  //       treeName: '10000',
  //       hasParent: false,
  //       hasChildren: false,
  //       label: '10000',
  //       key: '10000',
  //     },
  //   ];
  // }, 2000);
}
treeList();
onMounted(() => {
  console.log(route.query, 'query');
});
defineOptions({ name: 'VideoIndex' });
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;
  .container {
    flex: 1;
    background: #eef7ff;
  }
}
</style>
