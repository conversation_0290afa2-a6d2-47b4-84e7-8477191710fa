<template>
  <n-form :show-feedback="false" label-placement="left">
    <n-flex :size="[20, 10]">
      <n-form-item label="单位名称:">
        <n-select
          class="!w-[220px]"
          v-model:value="filterForm.unitId"
          :options="unitOptions"
          placeholder="请输入"
          label-field="unitName"
          value-field="id"
          :loading="loading"
          clearable
          filterable
          remote
          @search="searchUnit"
        />
      </n-form-item>
    </n-flex>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION } from '../type';
import { onMounted, ref, watch } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);

function initForm() {
  return {
    unitId: null, // 单位id
  };
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function searchUnit(value: string) {
  // search(
  //   comGetUnitByName({
  //     monitorStatus: '1',
  //     unitType: '0',
  //     fireproofStatus: '0',
  //     unitName: value,
  //   })
  // ).then((res) => {
  //   unitOptions.value = res.data || [];
  // });
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'DemoJurisdictionFilter' });
</script>

<style module lang="scss"></style>
