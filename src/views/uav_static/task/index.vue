<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" style="width: 327px">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="!treeLoading">
      <com-card :list="cardList"></com-card>
      <com-filter class="com-table-filter" @action="actionFn" />
      <div class="layer-cont">
        <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import ComCard from '@/components/card/ComCardA.vue';
import { jumpToKETApi } from './fetchData';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData } from './type';
import type { ICardAItem } from '@/components/card/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

const [loading, run] = useAutoLoading(false);
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
//树结构--操作
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
  },
  {
    immediate: true,
  }
);
function jumpToKET(id?: string, mode?: number) {
  run(jumpToKETApi({ mode: mode, id: id })).then((res) => {
    console.log(res);
    // 设置你想要跳转到的URL
    const url = res.data;
    window.open(url, '_blank');
  });
}
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  tableCompRef.value?.setDeptId(v.id);
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();
const cardList = ref<ICardAItem[]>([
  {
    label: '总数量（个）',
    value: 4,
    id: 1,
  },
  {
    label: '进行中（个）',
    value: 0,
    id: 2,
  },
  {
    label: '待开始（个）',
    value: 2,
    id: 3,
  },
  {
    label: '已完成（个）',
    value: 2,
    id: 4,
  },
  {
    label: '已逾期（个）',
    value: 0,
    id: 5,
  },
]);

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, 'actionList-----');
  currentAction.value = val;
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(val.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(val.data);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      jumpToKET(val.data.id, val.data.mode);
      isShowAside.value = false;
      break;
    case ACTION.EXPORT:
      console.log('导出');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
