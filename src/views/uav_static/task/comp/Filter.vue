<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <n-form-item label="计划名称:">
        <n-input placeholder="请输入" v-model:value="filterForm.v1" clearable class="!w-[260px]" />
      </n-form-item>
      <!-- <div class="w-[12%] flex justify-end">
        <n-button type="primary" @click="doHandle(ACTION.EXPORT)">
          {{ ACTION_LABEL.EXPORT }}
        </n-button>
      </div> -->
    </div>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';
import { CdAdd } from '@kalimahapps/vue-icons';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);

function initForm() {
  return {
    v1: '',
  };
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function searchUnit(value: string) {}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'VideoEquiFilter' });
</script>

<style module lang="scss"></style>
