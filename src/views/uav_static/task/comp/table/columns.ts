import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'missionName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'siteName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '实际开始日期',
    key: 'startDate',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '实际结束日期',
    key: 'endDate',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '执行时间',
    key: 'excuteTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
];
