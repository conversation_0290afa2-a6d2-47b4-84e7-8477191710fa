<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IPageData } from '../../type';
import { ACTION, ACTION_LABEL } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode } from 'vue';
// import { pageData } from '../../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
// import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  tableData.value = [
    {
      areaCode: '11111',
    },
    {
      areaCode: '2222',
    },
    {
      areaCode: '33333',
    },
    {
      areaCode: '2222',
    },
  ];
  updateTotal(20);
  // search(pageData(params)).then((res) => {
  //   tableData.value = res.data.rows || [];
  //   updateTotal(res.data.total || 0);
  // });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          disabled: row.areaCode ? true : false,
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.VIDEO, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.VIDEO }
      ),
    ],
  ];
  return acList;
  //加线
  //return useActionDivider(acList);
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
