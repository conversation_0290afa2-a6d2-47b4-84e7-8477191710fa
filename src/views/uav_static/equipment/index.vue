<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" style="width: 327px">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="!treeLoading">
      <!-- <com-filter class="com-table-filter" @action="actionFn" /> -->
      <div class="layer-cont">
        <com-card-b @gotoDetail="gotoDetail" :treeName="treeName" :equipmentList="equipmentList"></com-card-b>
      </div>
    </div>
    <ComCardC ref="ComCardCRef" :deviceDetailsList="deviceDetail" />
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, onMounted, provide, Ref, ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComCardB from '@/components/card/ComCardUav.vue';
import ComCardC from '@/components/card/ComCardUavE.vue';
import ComFilter from './comp/Filter.vue';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import { postDeviceListApi, postDeviceDetailApi } from './fetchData';
import { UavICardBItem } from '@/components/card/type';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import type { IActionData, DeviceListType, DeviceDetailType } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

const ComCardCRef = ref<InstanceType<typeof ComCardC>>();
const [loading, run] = useAutoLoading(false);
//树结构--操作
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
let treeName: any = ref('123');
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
    treeName.value = list[0]?.treeName;
    // postDeviceList(list[0]?.id);
  },
  {
    immediate: true,
  }
);
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeAct.value = v;
  treeName.value = v.treeName;
  // postDeviceList(v.id);
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}

// let equipmentList = ref<UavICardBItem[]>([]);
let equipmentList = ref<any>([
  {
    id: '1',
    name: '大疆x代机库',
    deviceName: '6号集气站充电房',
    eqType: '七腾防爆充电房',
    deviceId: '328490324…',
    eqName2: '6号集气站巡检...',
    category: 'Dock 2',
  },
]);
let deviceDetail = ref<DeviceDetailType[]>([]);
function postDeviceList(id: any) {
  let prams = {
    deviceId: id,
    paseNo: 1,
    paseSize: 10,
  };
  run(postDeviceListApi(prams)).then((res: any) => {
    // equipmentList.value = res.data.rows;
    console.log(res.data, '1qwe');
  });
}

function gotoDetail(value: string) {
  console.log('list 传过来的 value 是', value);
  ComCardCRef.value?.init();
  // run(postDeviceDetailApi(value)).then((res) => {
  //   console.log('res');
  //   // deviceDetail.value = res.data;
  //   ComCardCRef.value?.init();
  // });
}
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.EDIT:
      console.log('详情');
      isShowAside.value = true;
      break;
    case ACTION.VIDEO:
      console.log('查看视频');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'VideoEquiIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
