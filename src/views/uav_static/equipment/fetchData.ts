import type { IDetail, IPageDataRes } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';
import type { DeviceListType, DeviceDetailType } from './type';

// // 获取分页
// export function pageData(query: IObj<any>) {
//   const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionPageList, query);
//   return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
// }

// // 获取详情
// export function getDetail(id: string) {
//   const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionDetail, { id });
//   return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
// }

// // 更新
// export function postUpdate(data: { id: string; jurisdiction: string }) {
//   const url = api.getUrl(api.type.demo, api.name.demo.jurisdictionUpdate);
//   return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }

//获取设备列表
export function postDeviceListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.uav.deviceList, query);
  return $http.get<DeviceListType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//获取设备详情
export function postDeviceDetailApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.uav.deviceDetail, query);
  return $http.get<DeviceDetailType[]>(`${url}`, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
