import type { IMenu } from '@/views/menu/type';

/**
 * 菜单 - 支队、大队 rq 以前icon
 */
export const menuDataList: IMenu[] = [
  {
    label: '智能巡检一张图',
    key: 'drawing',
    routeName: 'drawing',
    icon: 'znxjyzt',
  },
  {
    label: '智能视频轮巡',
    key: 'video',
    routeName: 'video',
    icon: 'znspxj',
  },
  {
    label: '巡检计划管理',
    key: 'inspectPlan',
    routeName: 'inspectPlan',
    icon: 'znspxj',
  },
  {
    label: '任务管理',
    key: 'inspectTask',
    routeName: 'inspectTask',
    icon: 'znspxj',
  },
  {
    label: '巡检设备',
    key: 'inspectDevice',
    routeName: 'inspectDevice',
    icon: 'znspxj',
  },
  {
    label: '无人机自动巡检',
    key: 'uav',
    routeName: 'uav',
    icon: 'wrjzdxj',
  },
  {
    label: '机器人自动巡检',
    key: 'robot',
    routeName: 'robot',
    icon: 'jqrzdxj',
  },
  {
    label: '无人机智能巡检',
    key: 'uavStatic',
    routeName: 'uavStatic',
    icon: 'wrjzdxj',
  },
  {
    label: '机器人智能巡检',
    key: 'robotStatic',
    routeName: 'robotStatic',
    icon: 'jqrzdxj',
  },
  {
    label: '人工智能巡检',
    key: 'artificial',
    routeName: 'artificial',
    icon: 'rgznxj',
  },
  {
    label: '参数配置',
    key: 'parameterConfig',
    routeName: 'parameterConfig',
    icon: 'cspz',
  },
  // {
  //   label: '配置管理（示例）',
  //   key: 'pzgl',
  //   icon: 'pzgl',
  //   children: [
  //     {
  //       label: '检查清单配置',
  //       key: 'checklistConfList',
  //       routeName: 'checklistConfList',
  //     },
  //     {
  //       label: '管辖范围配置',
  //       key: 'jurisdiction',
  //       routeName: 'jurisdiction',
  //     },
  //   ],
  // },
];
