import { iconBase } from '@/utils/svg.ts';

export const icon_expand = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="a"><rect width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#a)"><path d="M1.115 10.388l4.228 3.027c.**************.285.02a.277.277 0 0 0 .147-.249v-6.05a.277.277 0 0 0-.147-.248.271.271 0 0 0-.285.02L1.115 9.935a.28.28 0 0 0 0 .452zM8.125 6.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm0 4.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm-6.333 4.8h16.416q.792 0 .792.8t-.792.8H1.792Q1 18 1 17.2t.792-.8zm0-14.4h16.416Q19 2 19 2.8t-.792.8H1.792Q1 3.6 1 2.8t.792-.8z" fill="#606266"/></g></svg>`
  );

export const icon_sy = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><g><g><path d="M11.5,17L11.5,13.6766C11.5,13.1521,11.05229,12.72701,10.5,12.72701L9.5,12.72701C8.94771,12.72701,8.5,13.1521,8.5,13.6766L8.5,17L4,17C3.447715,17,3,16.5749,3,16.0504L3,8.2812C3.0000698853,7.98449,3.1462,7.70487,3.395,7.52536L9.395,3.193495C9.75258,2.9355018,10.24742,2.9355018,10.605,3.193495L16.605,7.52536C16.8539,7.705,17,7.98462,17,8.2812L17,16.0504C17,16.5749,16.552300000000002,17,16,17L11.5,17Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );

export const icon_pzgl = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_8384"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_8384)"><g><path d="M17.26914,13.38107L11.730858,13.38107C11.324826,13.38107,11,13.07417,11,12.690536999999999C11,12.306905,11.324826,12,11.730858,12L17.26914,12C17.67517,12,18,12.306905,18,12.690536999999999C18,13.07417,17.67517,13.38107,17.26914,13.38107ZM17.26914,15.69821L11.730858,15.69821C11.324826,15.69821,11,15.391300000000001,11,15.007670000000001C11,14.62404,11.324826,14.31714,11.730858,14.31714L17.26914,14.31714C17.67517,14.31714,18,14.62404,18,15.007670000000001C18,15.391300000000001,17.67517,15.69821,17.26914,15.69821ZM17.26914,18L11.730858,18C11.324826,18,11,17.693089999999998,11,17.30946C11,16.92583,11.324826,16.61893,11.730858,16.61893L17.26914,16.61893C17.67517,16.61893,18,16.92583,18,17.30946C18,17.693089999999998,17.67517,18,17.26914,18Z" fill="#545A67" fill-opacity="1"/></g><g><path d="M17.8602,7.19424L16.4621,4.82156C16.1857,4.37268,15.6005,4.19633,15.1291,4.4368099999999995L13.9911,4.88569C13.5684,4.56506,13.0969,4.30855,12.593,4.11617L12.4304,2.945865C12.3979,2.4007899999999998,11.95895,2,11.42248,2L8.593779999999999,2C8.0573,2,7.61836,2.4007899999999998,7.58585,2.929834L7.42328,4.10014C6.93557,4.30855,6.46412,4.56506,6.02519,4.86966L4.88721,4.42077C4.415760000000001,4.1803,3.8305100000000003,4.35665,3.5541400000000003,4.80553L2.1397909999999998,7.19424C1.87968,7.64313,2.0259924,8.2363,2.481185,8.50884L3.44034,9.230260000000001C3.37531,9.743269999999999,3.37531,10.27231,3.44034,10.78533L2.464929,11.50675C2.00973549,11.77929,1.863423,12.3725,2.1397909999999998,12.8213L3.5541400000000003,15.194C3.8305100000000003,15.6429,4.415760000000001,15.8193,4.90346,15.5788L6.02519,15.1299C6.44787,15.4505,6.91932,15.707,7.40702,15.8994L7.56959,17.069699999999997C7.60211,17.5988,8.041039999999999,18.0156,8.57752,17.9996L9.69925,17.9996C9.86182,17.9996,9.97561,17.871299999999998,9.991869999999999,17.727L9.991869999999999,17.117800000000003L9.991869999999999,13.2542C8.18736,13.2702,6.70798,11.81135,6.70798,10.01581C6.70798,8.22027,8.18736,6.76139,10.00813,6.76139C11.8289,6.76139,13.3083,8.22027,13.3083,10.01581C13.3083,10.25628,13.2758,10.49676,13.227,10.73723L16.3808,10.73723C16.4946,10.73723,16.5922,10.64104,16.6084,10.52882C16.640900000000002,10.09597,16.640900000000002,9.67915,16.5759,9.24629L17.5351,8.52487C17.990299999999998,8.2363,18.1366,7.64313,17.8602,7.19424Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );
export const icon_rq = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_46_44089"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_46_44089)"><g><path d="M14.3026,15.7753L5.69867,15.7753C5.18783,15.7753,4.7722999999999995,16.1628,4.7722999999999995,16.637999999999998C4.7722999999999995,17.1125,5.18783,17.5,5.69867,17.5L14.3051,17.5C14.8159,17.5,15.2308,17.1125,15.2308,16.637999999999998C15.2308,16.1628,14.8159,15.7753,14.3026,15.7753ZM16.2301,1.250000256782L3.77243,1.250000256782C2.38008,1.250000256782,1.25,2.30199,1.25,3.59757L1.25,12.3663C1.25,13.6631,2.3794500000000003,14.7145,3.77243,14.7145L16.227600000000002,14.7145C17.6205,14.7145,18.75,13.6631,18.75,12.3663L18.75,3.59631C18.75,2.3001,17.6218,1.25,16.2301,1.250000256782ZM15.9516,10.17709L12.8915,10.17709C12.6666,10.17709,12.4511,10.09568,12.2891,9.95117L9.3493,7.3335L7.98997,9.67476C7.83734,9.93791,7.54205,10.10199,7.22246,10.10199L4.26582,10.10199C3.78426,10.10199,3.39366,9.73913,3.39366,9.28981C3.39366,8.840489999999999,3.78426,8.47762,4.26582,8.47762L6.70228,8.47762L8.35877,5.62456C8.49639,5.39617,8.72859,5.2430699999999995,8.99047,5.20806C9.26022,5.169560000000001,9.53308,5.24782,9.72994,5.42388L13.2441,8.55398L15.9528,8.55398C16.4344,8.55398,16.825,8.91685,16.825,9.36553C16.825,9.81422,16.4332,10.17709,15.9516,10.17709Z" fill="#FFFFFF" fill-opacity="1"/></g></g></svg>`
  );
export const icon_znxjyzt = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32154"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_115_32154)"><g><path d="M3,3L9.22222,3L9.22222,10.77778L3,10.77778L3,3ZM10.77778,9.22222L17,9.22222L17,17L10.77778,17L10.77778,9.22222ZM3,12.33333L9.22222,12.33333L9.22222,17L3,17L3,12.33333ZM10.77778,3L17,3L17,7.66667L10.77778,7.66667L10.77778,3Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
export const icon_znspxj = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32209"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_115_32209)"><g><path d="M17.346701525878906,12.3562C17.553301525878908,12.5146,17.778801525878904,12.7306,17.945801525878906,13.105C18.114501525878907,13.4218,18.152401525878908,13.8754,18.021501525878907,14.2696C17.890701525878907,14.6854,17.646301525878904,15.0184,17.365701525878904,15.2974C17.078201525878907,15.5728,16.763201525878905,15.8176,16.429201525878906,16.026400000000002C15.079601525878907,16.874200000000002,14.654401525878907,17.1082,13.094801525878907,17.6644C12.518101525878906,17.8696,12.528401525878905,17.8822,12.024101525878907,17.9416L12.024101525878907,15.823C12.454401525878906,15.8302,12.397601525878907,15.85,12.852101525878906,15.7888C14.208601525878906,15.6088,14.688801525878906,15.5116,15.942001525878906,15.0202C16.560001525878906,14.7628,17.140201525878908,14.428,17.384601525878907,13.9744C17.506801525878906,13.762,17.541301525878907,13.5046,17.479301525878906,13.2652C17.401801525878906,13.0258,17.281301525878906,12.8044,17.123001525878905,12.6136C16.863001525878907,12.313,16.566901525878905,12.0484,16.243301525878906,11.8252Q16.000601525878906,11.6668,15.868001525878906,11.5876Q16.01780152587891,11.6272,16.279501525878906,11.746C16.542801525878907,11.8648,16.935301525878906,12.0214,17.346701525878906,12.3562ZM7.018141525878907,15.6808C7.388251525878906,15.7006,7.739421525878906,15.7186,8.069941525878907,15.7186L8.069941525878907,14.6512L10.476501525878906,16.8256L8.073381525878906,19L8.073381525878906,17.758C7.639581525878906,17.6968,7.195451525878906,17.6626,6.777141525878906,17.5258C5.565251525878907,17.128,4.8835715258789065,16.7482,3.5511815258789063,15.9148C3.2241115258789064,15.7132,2.919411525878906,15.472,2.645705525878906,15.1966C2.3685555258789064,14.905,2.108618525878906,14.5756,1.9795113258789063,14.1868C1.8572896258789062,13.8124,1.8762254258789062,13.402,2.0345975258789064,13.042C2.1826405258789063,12.7324,2.4047055258789065,12.5182,2.607834525878906,12.3436C3.033031525878906,12.016,3.4031315258789063,11.8594,3.6613515258789064,11.7424Q3.9212915258789063,11.6254,4.067611525878906,11.5876Q3.919561525878906,11.665,3.6975015258789066,11.8198C3.456501525878906,11.9962,3.122541525878906,12.2266,2.826456525878906,12.5956C2.6698055258789064,12.7792,2.5561915258789063,12.9988,2.4942195258789064,13.2346C2.4201985258789063,13.4704,2.4546265258789064,13.7278,2.5871775258789063,13.933C2.847113525878906,14.3596,3.4203515258789063,14.7088,4.031461525878907,14.941C5.272611525878906,15.4288,5.740841525878906,15.6034,7.018141525878907,15.6808ZM9.999661525878906,1.00000801336C7.827221525878906,0.99640801,5.816581525878906,2.20601,4.730361525878907,4.1734100000000005C3.6424115258789063,6.14081,3.6424115258789063,8.5654,4.730361525878907,10.5328C5.818301525878907,12.5002,7.827221525878906,13.7098,9.999661525878906,13.7062C13.354701525878907,13.7062,16.074601525878904,10.8622,16.074601525878904,7.35401C16.074601525878904,3.84401,13.354701525878907,1.00000860735,9.999661525878906,1.00000801336ZM9.999661525878906,10.5292C8.321271525878906,10.5292,6.961331525878906,9.10721,6.961331525878906,7.35221C6.961331525878906,5.5972,8.321271525878906,4.17521,9.999661525878906,4.17521C11.678061525878906,4.17521,13.038001525878906,5.59721,13.038001525878906,7.35221C13.038001525878906,9.1072,11.678061525878906,10.5292,9.999661525878906,10.5292Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M9.999998474121094,8.2000244140625C9.502549474121095,8.2000244140625,9.099998474121094,7.7974744140625,9.099998474121094,7.3000244140625C9.099998474121094,6.8025754140625,9.502549474121095,6.4000244140625,9.999998474121094,6.4000244140625C10.497448474121093,6.4000244140625,10.899998474121094,6.8025754140625,10.899998474121094,7.3000244140625C10.899998474121094,7.7959444140625,10.497448474121093,8.2000244140625,9.999998474121094,8.2000244140625Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
export const icon_wrjzdxj = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32195"><rect x="0" y="20" width="20" height="20" rx="0"/></clipPath></defs><g transform="matrix(1,0,0,-1,0,40)" clip-path="url(#master_svg0_115_32195)"><g><path d="M10.051229820898438,32.6015625C10.038143920898438,32.60292969,10.024862720898437,32.60351562,10.011581420898438,32.60390625Q10.045272820898438,32.60390625,10.078964220898438,32.60332031C10.069784520898438,32.603125,10.060409520898437,32.602539062,10.051229820898438,32.6015625Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g transform="matrix(1,0,0,-1,0,56)"><path d="M17.256,28.622147C17.4601,28.750365,17.6828,28.925206,17.8477,29.22827C18.0143,29.4847,18.0517,29.851869999999998,17.9225,30.17096C17.793300000000002,30.50753,17.5519,30.77708,17.2747,31.00291C16.9908,31.225839999999998,16.6797,31.42399,16.349800000000002,31.59301C15.0169,32.27926,14.597,32.46867,13.0566,32.91889C12.487,33.08499,12.4972,33.09519,11.99908,33.14327L11.99908,31.42836C12.4241,31.43419,12.368,31.45022,12.8169,31.40068C14.1566,31.25498,14.631,31.1763,15.8687,30.77853C16.479100000000003,30.57018,17.052,30.29917,17.2934,29.93201C17.4142,29.76008,17.4482,29.55172,17.387,29.35794C17.3104,29.16416,17.1914,28.984945,17.035,28.8305C16.7783,28.587178,16.4859,28.372997,16.1662,28.192325Q15.9265,28.0641079,15.7956,28Q15.9435,28.0320547,16.201900000000002,28.128218C16.4621,28.224381,16.8497,28.351141,17.256,28.622147ZM7.05496,31.31326C7.42049,31.32929,7.76733,31.34386,8.09377,31.34386L8.09377,30.47985L10.47062,32.23992L8.09717,34L8.09717,32.994659999999996C7.66872,32.94512,7.23007,32.91744,6.81693,32.8067C5.62,32.484700000000004,4.9467300000000005,32.17727,3.63079,31.502670000000002C3.30776,31.33949,3.00683,31.14425,2.736498,30.92132C2.4627689999999998,30.68528,2.206042,30.41865,2.0785282,30.10393C1.9578154,29.80088,1.9765174,29.46868,2.132934,29.17727C2.27915,28.926665,2.498473,28.75328,2.699094,28.611948C3.11904,28.346772,3.4845800000000002,28.220011,3.7396000000000003,28.125305Q3.99633,28.0305988,4.14085,28.00000154391Q3.99463,28.0626527,3.77531,28.187957C3.53728,28.330745,3.2074499999999997,28.517243,2.9150169999999997,28.815931C2.760301,28.964547,2.648089,29.1423,2.586882,29.33317C2.513775,29.52404,2.547778,29.7324,2.678692,29.8985C2.935419,30.24381,3.5015799999999997,30.52647,4.1051400000000005,30.71442C5.330970000000001,31.10928,5.793419999999999,31.25061,7.05496,31.31326Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M6.18155,33.63749C6.2910900000000005,33.63749,6.45448,33.63749,6.56401,33.69133C6.330360000000001,34.38966,5.93841,35.02451,5.4187899999999996,35.54625C5.20083,35.77532,5.20083,36.1351,5.4187899999999996,36.3642L5.80033,36.6913C6.02847,36.8553,6.33586,36.8553,6.56401,36.6913C7.0547699999999995,36.0912,7.70892,35.709649999999996,8.30923,35.4367C8.363389999999999,35.6004,8.363389999999999,35.709649999999996,8.363389999999999,35.81919C8.3628,37.024,7.38579,38.0004,6.18103,38C4.976417,37.9998,4,37.022999999999996,4,35.81813C4.000221129,34.61305,4.97687,33.636250000000004,6.18155,33.636250000000004L6.18155,33.63749ZM13.8726,33.58209C15.0175,33.63749,15.999,34.61878,15.999,35.81919C15.9959,37.022,15.0201,37.995400000000004,13.81734,37.995400000000004C12.61462,37.995400000000004,11.638760000000001,37.022,11.63565,35.81919C11.63565,35.709649999999996,11.63565,35.54625,11.691040000000001,35.4367C12.38899,35.67092,13.02357,36.062799999999996,13.54552,36.5821C13.74604,36.7929,14.0795,36.8012,14.2903,36.6007Q14.2998,36.5916,14.3089,36.5821L14.6363,36.2547C14.8469,36.054,14.855,35.7205,14.6543,35.50985Q14.6455,35.50061,14.6363,35.491820000000004C14.0904,34.94594,13.70888,34.34681,13.49104,33.637209999999996C13.59965,33.637209999999996,13.76311,33.58214,13.87256,33.58214L13.8726,33.58209ZM13.8726,27.8001L14.1997,28.12751C12.07325,30.30921,12.07325,33.74579,14.1997,35.87335L13.92675,36.2005C11.74523,34.073859999999996,8.30927,34.073859999999996,6.18155,36.2005L5.80001,35.92751C7.92678,33.74579,7.92678,30.30957,5.80001,28.18167L6.18155,27.85456C8.277239999999999,29.96397,11.68614,29.97473,13.79506,27.87918C13.82114,27.85315,13.84699,27.8269,13.8726,27.80042L13.8726,27.8001ZM10,30.85484C9.38195,30.84057,8.86935,31.33008,8.855080000000001,31.94818Q8.854479999999999,31.973959999999998,8.855080000000001,31.999859999999998C8.855080000000001,32.6553,9.34554,33.14579,10,33.14579C10.61807,33.16009,11.130690000000001,32.67058,11.144960000000001,32.052459999999996Q11.14556,32.02669,11.144960000000001,32.00078C11.159220000000001,31.38268,10.66976,30.87004,10.05171,30.85577Q10.025929999999999,30.85517,10.00004,30.85577L10,30.85484ZM13.81845,26C15.0233,26.000000186465,16,26.976794,16,28.18173C16,29.38667,15.0233,30.36346,13.81845,30.36346C13.70891,30.36346,13.59968,30.36346,13.43598,30.30961Q13.76368,29.327840000000002,14.5812,28.50978C14.7923,28.30953,14.801,27.97609,14.6008,27.76502Q14.5913,27.75499,14.5812,27.74541L14.2538,27.41796C14.0531,27.20731,13.71972,27.19924,13.50908,27.39993Q13.49986,27.40874,13.49107,27.41796C12.97684,27.93377,12.36493,28.34187,11.691130000000001,28.61836C11.63574,28.45465,11.63574,28.34541,11.63574,28.18172C11.63962,26.977942,12.61477,26.00323787,13.81845,26ZM6.18155,26C7.38477,26.00388257,8.359200000000001,26.978399,8.36309,28.18172C8.36309,28.34541,8.36309,28.45465,8.30923,28.5642C7.65094,28.340040000000002,7.053570000000001,27.96634,6.56401,27.47242C6.36359,27.26152,6.03018,27.25303,5.8192900000000005,27.45345Q5.80956,27.462699999999998,5.80033,27.47242L5.41817,27.74451C5.20032,27.963,5.14525,28.34454,5.41817,28.56243Q6.236940000000001,29.38124,6.56309,30.36287L6.18155,30.36287C4.976895,30.36287,4.000329084,29.38623,4.000329084,28.18147C4.000329084,26.976717999999998,4.976895,26.0000715093,6.18155,26.0000716026L6.18155,26Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
export const icon_jqrzdxj = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32184"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_115_32184)"><g><path d="M12,4Q12,4.09825,11.99037,4.19603Q11.98074,4.293810000000001,11.96157,4.39018Q11.9424,4.486549999999999,11.913879999999999,4.58057Q11.88536,4.67459,11.847760000000001,4.76537Q11.81016,4.85614,11.76384,4.9427900000000005Q11.71753,5.029450000000001,11.662939999999999,5.11114Q11.60835,5.19284,11.54602,5.26879Q11.48369,5.34474,11.41421,5.414210000000001Q11.34474,5.48369,11.26879,5.54602Q11.19284,5.60835,11.111139999999999,5.66294Q11.02945,5.71753,10.94279,5.76384Q10.85614,5.81016,10.76537,5.84776Q10.67459,5.88536,10.58057,5.91388Q10.48655,5.9424,10.39018,5.96157Q10.29381,5.98074,10.19603,5.99037Q10.09825,6,10,6Q9.90175,6,9.80397,5.99037Q9.70619,5.98074,9.60982,5.96157Q9.51345,5.9424,9.41943,5.91388Q9.32541,5.88536,9.23463,5.84776Q9.14386,5.81016,9.05721,5.76384Q8.970554,5.71753,8.888859,5.66294Q8.807164,5.60835,8.731213,5.54602Q8.655262,5.48369,8.585786,5.414210000000001Q8.516311,5.34474,8.453979,5.26879Q8.391648,5.19284,8.337061,5.11114Q8.282474,5.029450000000001,8.236157,4.9427900000000005Q8.189841,4.85614,8.152241,4.76537Q8.114641,4.67459,8.0861193,4.58057Q8.0575978,4.486549999999999,8.0384294,4.39018Q8.0192611,4.293810000000001,8.00963055,4.19603Q8,4.09825,8,4Q8,3.90175,8.00963055,3.80397Q8.0192611,3.7061900000000003,8.0384294,3.60982Q8.0575978,3.5134499999999997,8.0861193,3.41943Q8.114641,3.3254099999999998,8.152241,3.23463Q8.189841,3.14386,8.236157,3.05721Q8.282474,2.970554,8.337061,2.888859Q8.391648,2.807164,8.453979,2.731213Q8.516311,2.655262,8.585786,2.585786Q8.655262,2.516311,8.731213,2.453979Q8.807164,2.391648,8.888859,2.337061Q8.970554,2.282474,9.05721,2.236157Q9.14386,2.189841,9.23463,2.152241Q9.32541,2.114641,9.41943,2.0861193Q9.51345,2.0575978,9.60982,2.0384294Q9.70619,2.0192611,9.80397,2.00963055Q9.90175,2,10,2Q10.09825,2,10.19603,2.00963055Q10.29381,2.0192611,10.39018,2.0384294Q10.48655,2.0575978,10.58057,2.0861193Q10.67459,2.114641,10.76537,2.152241Q10.85614,2.189841,10.94279,2.236157Q11.02945,2.282474,11.111139999999999,2.337061Q11.19284,2.391648,11.26879,2.453979Q11.34474,2.516311,11.41421,2.585786Q11.48369,2.655262,11.54602,2.731213Q11.60835,2.807164,11.662939999999999,2.888859Q11.71753,2.970554,11.76384,3.05721Q11.81016,3.14386,11.847760000000001,3.23463Q11.88536,3.3254099999999998,11.913879999999999,3.41943Q11.9424,3.5134499999999997,11.96157,3.60982Q11.98074,3.7061900000000003,11.99037,3.80397Q12,3.90175,12,4ZM10.6,4Q10.6,3.9409099999999997,10.588470000000001,3.88295Q10.57694,3.8249899999999997,10.55433,3.77039Q10.53171,3.71579,10.49888,3.6666600000000003Q10.46605,3.61752,10.42426,3.5757399999999997Q10.382480000000001,3.53395,10.33334,3.5011200000000002Q10.28421,3.46829,10.229610000000001,3.44567Q10.17501,3.42306,10.117049999999999,3.41153Q10.05909,3.4,10,3.4Q9.94091,3.4,9.88295,3.41153Q9.82499,3.42306,9.770389999999999,3.44567Q9.71579,3.46829,9.66666,3.5011200000000002Q9.61752,3.53395,9.57574,3.5757399999999997Q9.53395,3.61752,9.50112,3.6666600000000003Q9.46829,3.71579,9.44567,3.77039Q9.42306,3.8249899999999997,9.411529999999999,3.88295Q9.4,3.9409099999999997,9.4,4Q9.4,4.059089999999999,9.411529999999999,4.11705Q9.42306,4.17501,9.44567,4.22961Q9.46829,4.28421,9.50112,4.33334Q9.53395,4.38248,9.57574,4.42426Q9.61752,4.46605,9.66666,4.49888Q9.71579,4.53171,9.770389999999999,4.55433Q9.82499,4.5769400000000005,9.88295,4.58847Q9.94091,4.6,10,4.6Q10.05909,4.6,10.117049999999999,4.58847Q10.17501,4.5769400000000005,10.229610000000001,4.55433Q10.28421,4.53171,10.33334,4.49888Q10.382480000000001,4.46605,10.42426,4.42426Q10.46605,4.38248,10.49888,4.33334Q10.53171,4.28421,10.55433,4.22961Q10.57694,4.17501,10.588470000000001,4.11705Q10.6,4.059089999999999,10.6,4Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g><g><path d="M6.25,5.07143L6.25,9.357140000000001C6.25,9.948879999999999,6.75368,10.42857,7.375,10.42857L12.625,10.42857C13.24632,10.42857,13.75,9.948879999999999,13.75,9.357140000000001L13.75,5.07143C13.75,4.4796949999999995,13.24632,4,12.625,4L7.375,4C6.75368,4,6.25,4.4796949999999995,6.25,5.07143ZM4,6.142860000000001L4,7.928570000000001C4,8.32306,4.335786,8.642859999999999,4.75,8.642859999999999C5.16421,8.642859999999999,5.5,8.32306,5.5,7.928570000000001L5.5,6.142860000000001C5.5,5.7483699999999995,5.16421,5.42857,4.75,5.42857C4.335786,5.42857,4,5.7483699999999995,4,6.142860000000001ZM14.5,6.142860000000001L14.5,7.928570000000001C14.5,8.32306,14.8358,8.642859999999999,15.25,8.642859999999999C15.6642,8.642859999999999,16,8.32306,16,7.928570000000001L16,6.142860000000001C16,5.7483699999999995,15.6642,5.42857,15.25,5.42857C14.8358,5.42857,14.5,5.7483699999999995,14.5,6.142860000000001ZM8.5,7.571429999999999C8.91421,7.571429999999999,9.25,7.2516300000000005,9.25,6.857139999999999C9.25,6.46265,8.91421,6.142860000000001,8.5,6.142860000000001C8.08579,6.142860000000001,7.75,6.46265,7.75,6.857139999999999C7.75,7.2516300000000005,8.08579,7.571429999999999,8.5,7.571429999999999ZM12.25,6.857139999999999C12.25,7.2516300000000005,11.91421,7.571429999999999,11.5,7.571429999999999C11.08579,7.571429999999999,10.75,7.2516300000000005,10.75,6.857139999999999C10.75,6.46265,11.08579,6.142860000000001,11.5,6.142860000000001C11.91421,6.142860000000001,12.25,6.46265,12.25,6.857139999999999ZM5.5,11.857140000000001L5.5,13.28571C5.5,13.6802,5.83579,14,6.25,14L13.75,14C14.1642,14,14.5,13.6802,14.5,13.28571L14.5,11.857140000000001C14.5,11.46265,14.1642,11.142859999999999,13.75,11.142859999999999L6.25,11.142859999999999C5.83579,11.142859999999999,5.5,11.46265,5.5,11.857140000000001Z" fill-rule="evenodd" fill="#FFFFFF" fill-opacity="1"/></g><g><path d="M17.256,12.622147C17.4601,12.750365,17.6828,12.925206,17.8477,13.22827C18.0143,13.4847,18.0517,13.85187,17.9225,14.170960000000001C17.793300000000002,14.50753,17.5519,14.77708,17.2747,15.00291C16.9908,15.22584,16.6797,15.42399,16.349800000000002,15.59301C15.0169,16.27926,14.597,16.46867,13.0566,16.91889C12.487,17.08499,12.4972,17.09519,11.99908,17.14327L11.99908,15.42836C12.4241,15.434190000000001,12.368,15.45022,12.8169,15.40068C14.1566,15.25498,14.631,15.1763,15.8687,14.77853C16.479100000000003,14.57018,17.052,14.29917,17.2934,13.93201C17.4142,13.76008,17.4482,13.55172,17.387,13.35794C17.3104,13.16416,17.1914,12.984945,17.035,12.8305C16.7783,12.587178,16.4859,12.372997,16.1662,12.192325Q15.9265,12.0641079,15.7956,12Q15.9435,12.0320547,16.201900000000002,12.128218C16.4621,12.224381,16.8497,12.351141,17.256,12.622147ZM7.05496,15.31326C7.42049,15.32929,7.76733,15.34386,8.09377,15.34386L8.09377,14.479849999999999L10.47062,16.239919999999998L8.09717,18L8.09717,16.99466C7.66872,16.94512,7.23007,16.91744,6.81693,16.8067C5.62,16.4847,4.9467300000000005,16.17727,3.63079,15.50267C3.30776,15.33949,3.00683,15.14425,2.736498,14.92132C2.4627689999999998,14.68528,2.206042,14.41865,2.0785282,14.10393C1.9578154,13.80088,1.9765174,13.468679999999999,2.132934,13.17727C2.27915,12.926665,2.498473,12.75328,2.699094,12.611948C3.11904,12.346772,3.4845800000000002,12.220011,3.7396000000000003,12.125305Q3.99633,12.0305988,4.14085,12.00000154391Q3.99463,12.0626527,3.77531,12.187957C3.53728,12.330745,3.2074499999999997,12.517243,2.9150169999999997,12.815930999999999C2.760301,12.964547,2.648089,13.1423,2.586882,13.333169999999999C2.513775,13.52404,2.547778,13.7324,2.678692,13.8985C2.935419,14.24381,3.5015799999999997,14.52647,4.1051400000000005,14.71442C5.330970000000001,15.10928,5.793419999999999,15.25061,7.05496,15.31326Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
export const icon_rgznxj = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32174"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_115_32174)"><g><path d="M17.256,12.622147C17.4601,12.750365,17.6828,12.925206,17.8477,13.22827C18.0143,13.4847,18.0517,13.85187,17.9225,14.170960000000001C17.793300000000002,14.50753,17.5519,14.77708,17.2747,15.00291C16.9908,15.22584,16.6797,15.42399,16.349800000000002,15.59301C15.0169,16.27926,14.597,16.46867,13.0566,16.91889C12.487,17.08499,12.4972,17.09519,11.99908,17.14327L11.99908,15.42836C12.4241,15.434190000000001,12.368,15.45022,12.8169,15.40068C14.1566,15.25498,14.631,15.1763,15.8687,14.77853C16.479100000000003,14.57018,17.052,14.29917,17.2934,13.93201C17.4142,13.76008,17.4482,13.55172,17.387,13.35794C17.3104,13.16416,17.1914,12.984945,17.035,12.8305C16.7783,12.587178,16.4859,12.372997,16.1662,12.192325Q15.9265,12.0641079,15.7956,12Q15.9435,12.0320547,16.201900000000002,12.128218C16.4621,12.224381,16.8497,12.351141,17.256,12.622147ZM7.05496,15.31326C7.42049,15.32929,7.76733,15.34386,8.09377,15.34386L8.09377,14.479849999999999L10.47062,16.239919999999998L8.09717,18L8.09717,16.99466C7.66872,16.94512,7.23007,16.91744,6.81693,16.8067C5.62,16.4847,4.9467300000000005,16.17727,3.63079,15.50267C3.30776,15.33949,3.00683,15.14425,2.736498,14.92132C2.4627689999999998,14.68528,2.206042,14.41865,2.0785282,14.10393C1.9578154,13.80088,1.9765174,13.468679999999999,2.132934,13.17727C2.27915,12.926665,2.498473,12.75328,2.699094,12.611948C3.11904,12.346772,3.4845800000000002,12.220011,3.7396000000000003,12.125305Q3.99633,12.0305988,4.14085,12.00000154391Q3.99463,12.0626527,3.77531,12.187957C3.53728,12.330745,3.2074499999999997,12.517243,2.9150169999999997,12.815930999999999C2.760301,12.964547,2.648089,13.1423,2.586882,13.333169999999999C2.513775,13.52404,2.547778,13.7324,2.678692,13.8985C2.935419,14.24381,3.5015799999999997,14.52647,4.1051400000000005,14.71442C5.330970000000001,15.10928,5.793419999999999,15.25061,7.05496,15.31326Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M9.99985,8C8.27413,8,6.87487,6.65686,6.87487,5C6.87487,3.34314,8.27413,2,10.00015,2C11.72587,2,13.12483,3.34314,13.12483,5C13.12483,6.65686,11.72587,8,9.99985,8ZM9.99985,14C7.23845,14,5,13.338,5,11.65219C5,9.9664,7.23845,8.6,9.99985,8.6C12.76124,8.6,15,9.9664,15,11.65219C15,13.338,12.76124,14,9.99985,14Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
export const icon_cspz = (props: any) =>
  iconBase(
    props,
    `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_115_32166"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_115_32166)"><g><path d="M7.82196,18C6.92754,17.7431,6.0841,17.3291,5.329689999999999,16.7768L5.36355,15.1285C5.37807,14.414,4.88968,13.7908,4.2020800000000005,13.6462L2.613508,13.3119C2.253274,12.4401,2.0456433,11.51075,2,10.56604L3.28673,9.56551C3.8448700000000002,9.131630000000001,4.019270000000001,8.35416,3.70144,7.71675L2.967777,6.24559C3.41404,5.41587,3.9981999999999998,4.67112,4.69434,4.044420000000001L6.26543,4.44464C6.94663,4.6181,7.65245,4.27217,7.94393,3.6219799999999998L8.61679,2.121176C9.073699999999999,2.0403538,9.5366,1.999811497,10.00036,2.000000660875C10.47187,2.000000660875,10.93392,2.0415042,11.38358,2.121176L12.0564,3.6219799999999998C12.3479,4.27217,13.0537,4.6181,13.7349,4.44464L15.3057,4.044420000000001C16.0018,4.67112,16.586,5.41587,17.0322,6.24559L16.2989,7.71675C15.981,8.35408,16.1553,9.13153,16.7133,9.56551L18,10.56604C17.9544,11.51075,17.7467,12.4401,17.386499999999998,13.3119L15.7983,13.6458C15.1107,13.7904,14.6223,14.4137,14.6368,15.1281L14.6703,16.7768C13.9159,17.329,13.0724,17.742800000000003,12.178,17.9996L10.93137,16.9443C10.39162,16.487299999999998,9.60838,16.487299999999998,9.068629999999999,16.9443L7.82196,18ZM10,13.4876C11.80978,13.4876,13.2769,11.99442,13.2769,10.15249C13.2769,8.310559999999999,11.80978,6.81738,10,6.81738C8.19022,6.81738,6.7231,8.310559999999999,6.7231,10.15249C6.7231,11.99442,8.19022,13.4876,10,13.4876Z" fill="#FFFFFF" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>`
  );
