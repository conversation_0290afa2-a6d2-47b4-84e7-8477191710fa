import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import type { IMapMark } from '@/views/drawing/type';

//异常上报
export function equiVideoApi(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiListVideourl, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
//获取参数配置列表
export function configList(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.configList, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}
export function updateConfigList(data: { needChildUnit: string; needself: string; orgCode: string }) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.updateConfigList);
  return $http.post<IMapMark>(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
}

export function queryRecipient(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.receiveList, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
export function addRecipient(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.addReceive, query);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function updateRecipient(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.updateReceive, query);
  return $http.post(url, { data: { _cfg: { showTip: true }, ...query } });
}
