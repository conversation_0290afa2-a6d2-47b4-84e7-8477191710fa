<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri">
    <com-loading v-if="loading"></com-loading>
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri">
      <!-- <div :class="$style.receiveItem">
        <div :class="$style.abnormalItemName">智能视频轮巡异常上报接收人</div>
        <template v-if="videoType.hazardType == '0'">
          <div :class="$style.receiveBtn" @click="updateOradd('EDIT', 0)">修改</div>
          <div :class="$style.abnormalItemDesc">
            已设置接收人：{{ videoType.userName }}{{ `(${videoType.postName || '--'})` }}
          </div>
        </template>
        <template v-else>
          <div :class="$style.receiveBtn" @click="updateOradd('ADD', 0)">设置</div>
          <div :class="$style.abnormalItemDesc">当前未设置智能视频轮巡异常上报接收人</div>
        </template>
      </div> -->
      <div :class="$style.receiveItem">
        <div :class="$style.abnormalItemName">无人机自动巡检异常上报接收人</div>
        <template v-if="rootType.hazardType == '1'">
          <div :class="$style.receiveBtn" @click="updateOradd('EDIT', 1)">修改</div>
          <div :class="$style.abnormalItemDesc">
            已设置接收人：{{ rootType.userName }}{{ `(${rootType.postName || '--'})` }}
          </div>
        </template>
        <template v-else>
          <div :class="$style.receiveBtn" @click="updateOradd('ADD', 1)">设置</div>
          <div :class="$style.abnormalItemDesc">当前未设置无人机自动巡检异常上报接收人</div>
        </template>
      </div>
      <div :class="$style.receiveItem">
        <div :class="$style.abnormalItemName">机器人自动巡检异常上报接收人</div>
        <template v-if="uavType.hazardType == '2'">
          <div style="" :class="$style.receiveBtn" @click="updateOradd('EDIT', 2)">修改</div>
          <div :class="$style.abnormalItemDesc">
            已设置接收人：{{ uavType.userName }}{{ `(${uavType.postName || '--'})` }}
          </div>
        </template>
        <template v-else>
          <div style="" :class="$style.receiveBtn" @click="updateOradd('ADD', 2)">设置</div>
          <div :class="$style.abnormalItemDesc">当前未设置机器人自动巡检异常上报接收人</div>
        </template>
      </div>
    </div>
    <UserDialog
      ref="userDialogRef"
      title="选择人员"
      v-model:show="isDialogShow"
      @setReceiveUser="setReceiveUser"
      :dataList="receiveList"
      :hazardType="currentType"
      :type="actionType"
    ></UserDialog>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch, computed } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComLoading from '@/components/loading/comLoading.vue';
import { queryRecipient } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useStore } from '@/store';
import UserDialog from './user-dialog.vue';
import type { receiveData } from '../type';
import { ACTION } from '@/views/incident-bulletin/constant';
const store = useStore();
const [loading, run] = useAutoLoading(false);
const videoType = ref<receiveData>({});
const rootType = ref<receiveData>({});
const uavType = ref<receiveData>({});
//   const receiveDataList = ref<receiveData[]>();
const isDialogShow = ref(false);
const receiveList = ref<receiveData>({});
const actionType = ref();
const currentType = ref();
const userDialogRef = ref<any>(null);
//树结构--操作
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
});
const treeAct = ref<any>(null);
const treeData = ref<TreeOption[]>([]);
const userParam = ref({
  pageSize: 9999,
  orgCode: store.userInfo.orgCode ? store.userInfo.orgCode : store.treeAct.id,
  pageNo: 1,
});
// const userName = computed(() => store.treeAct.id);
// watch(userName, (newValue, oldValue) => {
//   if (newValue) {
//     userParam.value.orgCode = newValue;
//   }
// });
watch(
  () => props.treeList,
  (v) => {
    treeData.value = [...(v as TreeOption[])];
  },
  {
    immediate: true,
  }
);

function treeChange(v: TreeOption) {
  console.log('接收到tree v', v.id);
  console.log('treeOption', JSON.stringify(v));
  getRecipient(v.id);
  treeAct.value = v;
  userDialogRef.value?.setOrgTreeParms({ deptId: v.id, deptName: v.text });
  // actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id, deptName: v.text } });
}
function setReceiveUser(id: string) {
  getRecipient(id ? id : store.userInfo.orgCode);
}
function updateOradd(type: string, hazardType: any) {
  isDialogShow.value = true;
  actionType.value = type;
  currentType.value = hazardType;
  console.log('actionType.value', actionType.value);
  console.log('currentType.value', currentType.value);
  if (type == 'EDIT' && hazardType == '0') {
    receiveList.value = videoType.value;
    userDialogRef.value?.setCheckedUser(videoType.value.userName);
  }
  if (type == 'EDIT' && hazardType == '1') {
    receiveList.value = rootType.value;
    userDialogRef.value?.setCheckedUser(rootType.value.userName);
  }
  if (type == 'EDIT' && hazardType == '2') {
    receiveList.value = uavType.value;
    userDialogRef.value?.setCheckedUser(uavType.value.userName);
  }
  if (type == 'ADD') {
    receiveList.value = {};
    userDialogRef.value?.setCheckedUser('');
  }
  //当前页已选中的树，用于人员列表初始化请求人员数据
  userDialogRef.value?.getUser();
}
function getRecipient(id: any) {
  // let param = {deptId:}
  run(
    queryRecipient({ deptId: id }).then((res: any) => {
      console.log('获取列表 = ', JSON.stringify(res));
      videoType.value = {};
      rootType.value = {};
      uavType.value = {};
      // receiveDataList.value = res.data;
      res.data.forEach((item: any) => {
        if (item.hazardType == 0) {
          videoType.value = item;
        } else if (item.hazardType == 1) {
          rootType.value = item;
        } else if (item.hazardType == 2) {
          uavType.value = item;
        }
      });
    })
  );
}
getRecipient(store.userInfo.orgCode);

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
  padding: 20px;
}
.receiveItem {
  width: 100%;
  min-height: 100px;
  background: linear-gradient(180deg, #ffffff 0%, #dfe7ff 100%);
  box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2);
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #dfe7ff;
  margin: 0 10px 16px 10px;
  padding: 16px;
  font-weight: 400;
  font-size: 14px;
  color: #494a4d;
  // display: flex;
  // justify-content: space-between;
}
.abnormalItemName {
  flex: 1;
  text-align: left;
  font-weight: 600;
  font-size: 16px;
  color: #222222;
}
.abnormalItemDesc {
  padding-top: 10px;
  line-height: 24px;
}
.receiveBtn {
  float: right;
  color: #527cff;
  margin-right: 20px;
  font-size: 16px;
  &:hover {
    cursor: pointer;
  }
}
// .abnormalWrap {
//   position: relative;
//   .abnormalList {
//     padding: 8px 14px;
//     display: flex;
//     flex-wrap: wrap;
//   }
//   .abnormalItem {
//     width: 381px;
//     min-height: 156px;
//     background: linear-gradient(180deg, #ffffff 0%, #dfe7ff 100%);
//     box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2);
//     border-radius: 10px 10px 10px 10px;
//     border: 1px solid #dfe7ff;
//     margin: 0 10px 16px 10px;
//     padding: 16px;
//     font-weight: 400;
//     font-size: 14px;
//     color: #494a4d;
//   }
//   .abnormalItemIcon {
//     margin-right: 12px;
//     width: 44px;
//     height: 44px;
//     img {
//       width: 100%;
//       height: 100%;
//     }
//   }
//   .abnormalItemTop {
//     display: flex;
//     align-items: center;
//   }
//   .abnormalItemName {
//     flex: 1;
//     text-align: left;
//     font-weight: 600;
//     font-size: 16px;
//     color: #222222;
//   }
//   .abnormalItemDesc {
//     padding-top: 10px;
//     line-height: 24px;
//   }
// }
</style>
