<template>
  <div>
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :render-cell="useEmptyCell"
      :min-height="345"
      :row-key="rowKey"
      @update:checked-row-keys="handleCheck"
    />
  </div>
</template>

<script setup lang="ts">
import { h, ref, VNode, toRaw } from 'vue';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './columns';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { ACTION } from '@/views/incident-bulletin/constant';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';

const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});
//   const { pagination, updateTotal } = useNaivePagination(props.tableData);
const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
  // columns.value.push({
  //   title: '操作',
  //   key: 'actions',
  //   align: 'center',
  //   width: 240,
  //   render(row, index) {
  //     return getActionBtn(row, index);
  //   },
  // });
}
setColumns();
const checkedRowKeysRef = ref([]);
const rowKey = (row: any) => {
  return row;
};
function handleCheck(rowKeys: any) {
  console.log('rowKeys', JSON.stringify(rowKeys));
  checkedRowKeysRef.value = rowKeys;
  emits('action', rowKeys);
}
const emits = defineEmits(['action']);

defineExpose({
  params: checkedRowKeysRef,
});
defineOptions({ name: 'tableComp' });
</script>
<style module lang="scss"></style>
