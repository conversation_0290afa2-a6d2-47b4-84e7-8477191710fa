import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    type: 'selection',
    multiple: false,
  },

  {
    title: '序号',
    key: 'index',
    width: 65,
    align: 'center',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '人员姓名',
    key: 'userName',
    align: 'center',
  },

  {
    title: '部门',
    key: 'orgName',
    align: 'center',
  },
  {
    title: '岗位',
    key: 'postName',
    align: 'center',
  },
  {
    title: '员工类别',
    key: 'userTypeName',
    align: 'center',
  },
];
