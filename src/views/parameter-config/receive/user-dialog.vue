<template>
  <ComDialog @handle-close="handleClose" v-model:show="isShowDialog" :width="1200" :height="900">
    <div :class="$style.selectDiv">
      <div :class="$style.selectTitle">已选人员：</div>
      <div :class="$style.selectuser">
        <div v-if="checkedUser" @click="cancelCheck" :class="$style.selectedPerson">
          <div>{{ checkedUser }}</div>
          <span style="margin-left: 3px; color: #333">x</span>
        </div>
      </div>
    </div>

    <div :class="$style.searchItem">
      <div :class="$style.keyFilter">关键字搜索：</div>
      <n-input
        clearable
        v-model:value="userNameVal"
        type="text"
        placeholder="请输入员工姓名"
        style="width: 300px"
        :on-input="searchUserInfo"
      />
    </div>
    <div :class="$style.comlayercontent">
      <div :class="$style.comleft">
        <treeCom :data="treeData" :searchShow="false" key-field="id" label-field="text" @action="changeFn"></treeCom>
      </div>
      <div :class="$style.comright">
        <!-- <n-input-group></n-input-group> -->
        <!-- <TableList :table-data="userList" ref="userTableRef" @action="handChildClick"></TableList> -->
        <div style="background-color: #fff">
          <n-data-table
            class="h-full"
            remote
            striped
            :columns="columns"
            :data="userList"
            :bordered="false"
            :flex-height="true"
            :render-cell="useEmptyCell"
            :min-height="480"
            :row-key="rowKey"
            v-model:checked-row-keys="checkedRowKeysRef"
            :pagination="pagination"
            @update:checked-row-keys="handleCheck"
          />
        </div>
      </div>
    </div>
    <template v-slot:action>
      <n-space justify="end">
        <n-button @click="handleClose" type="tertiary">关闭</n-button>
        <n-button @click="handleSubmitted" type="primary">确定</n-button>
      </n-space>
    </template>
  </ComDialog>
</template>

<script lang="ts" setup>
import ComDialog from '@/components/dialog/ComDialogC.vue';
import { type FormInst, type SelectOption } from 'naive-ui';
import TreeCom from '@/components/tree/index.vue';
// import TableList from './userTable/Table.vue';
import { ref, computed } from 'vue';
import { postTreeList, getOrgUser, getOrgUserIncludeSub } from '@/views/common/fetchData';
import { addRecipient, updateRecipient } from '../fetchData';
import { IObj } from '@/types';
import type { TreeOption } from 'naive-ui';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useStore } from '@/store';
import type { receiveData } from '../type';
import { DataTableColumns, NButton } from 'naive-ui';
import { cols } from './userTable/columns';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { $toast } from '@/common/shareContext/useToastCtx.ts';
const curSelUnitId = ref('');
const checkedUserInfo = ref();
const [loading, run] = useAutoLoading(false);
const store = useStore();
const isShowDialog = ref(false);
const userNameVal = ref(null);
const formRef1 = ref<FormInst | null>(null);
// const formValue = ref<ReceiveUser>({} as ReceiveUser);
type listT = {
  dataList: receiveData[];
  hazardType: string;
  type?: string;
};
const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
}
setColumns();
const props = defineProps<listT>();
const userTableRef = ref(null);
const checkedUser = ref();

const emits = defineEmits(['setReceiveUser']);
const treeData = ref<any[]>([]);
const userList = ref<any[]>([]);
const treeParam = ref({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  //orgCode: '10000',
  orgCode: store.userInfo.orgCode, //机构id=10000,顶级是-1
  // //机构来源，1，内部 2 外部
  // orgRes: 1,
  // //去除的机构部门id,(下级,本身)
  // removeOrgCode: '',
});
const userData = computed(() => {
  return props.dataList;
});

const { pagination, updateTotal } = useNaivePagination(getUser);
const searchParams = ref({
  orgCode: store.userInfo.orgCode,
  orgName: store.userInfo.orgName,
  userName: '',
});
// const userParam = ref({
//   pageSize: pagination.pageSize,
//   pageNo: pagination.page,
//   orgCode: store.userInfo.orgCode,
//   userName: '',
// });
const params = ref<receiveData>({
  deptId: store.userInfo.deptId,
  deptName: store.userInfo.deptName,
  hazardType: '',
  id: '',
  postId: '',
  postName: '',
  userId: '',
  userName: '',
});
// function searchUser() {
//   console.log(pagination.page, pagination.pageSize);
//   userParam.value.userName = userNameVal.value;
//   getUser();
// }
// function searchChange(value: string) {
//   // console.log('搜索输入框触发了', value);
//   userParam.value.userName = value;
//   getUser();
// }

function searchUserInfo(value: string) {
  console.log('输入了', value);
  searchParams.value.userName = value;
  pagination.page = 1;
  getUser();
}
const checkedRowKeysRef = ref([]);
const rowKey = (row: any) => {
  return row;
};
function cancelCheck() {
  checkedRowKeysRef.value = [];
  checkedUser.value = '';
}
function handleCheck(keys: any, rowKeys: any) {
  checkedRowKeysRef.value = rowKeys;
  // checkedUserInfo.value = rowKeys[0];
  checkedUser.value = rowKeys[0].userName;
  if (props.type == 'ADD') {
    params.value.postName = rowKeys[0].postName;
    params.value.userId = rowKeys[0].id;
    params.value.userName = rowKeys[0].userName;
  }
  if (props.type == 'EDIT') {
    userData.value.postName = rowKeys[0].postName;
    userData.value.userId = rowKeys[0].id;
    userData.value.userName = rowKeys[0].userName;
  }
  // emits('action', rowKeys);
}
function treeList() {
  run(postTreeList(treeParam.value)).then((res) => {
    treeData.value = <[]>res.data || [];
    console.log('默认id', JSON.stringify(treeData.value[0].id));
    // changeFn(treeData.value[0]);
  });
}

function changeFn(v: TreeOption) {
  console.log('回调了', JSON.stringify(v));
  searchParams.value.orgCode = v.id;
  searchParams.value.orgName = v.text;
  pagination.page = 1;
  // params.value.deptId = v.id;
  // params.value.deptName = v.text;
  // console.log('用户列表查询参数', JSON.stringify(userParam.value));
  getUser();
}
function getUser() {
  let params = {
    pageSize: pagination.pageSize,
    pageNo: pagination.page,
    ...searchParams.value,
  };
  // userParam.value.orgCode = store.userInfo.orgCode;
  // checkedUserInfo.value = props.dataList;
  // console.log('打开', JSON.stringify(props.dataList));
  getOrgUserIncludeSub(params).then((res) => {
    userList.value = [];
    userList.value = <[]>res.data.rows || [];
    checkedRowKeysRef.value = [];
    userList.value.forEach((item: any) => {
      if (item.userName == checkedUser.value) {
        checkedRowKeysRef.value.push(item);
      }
    });
    updateTotal(res.data.total || 0);
  });
}
treeList();
function handleSubmitted() {
  if (props.type == 'EDIT') {
    console.log('userData.value', JSON.stringify(userData.value));
    console.log('checkedUserInfo.value', JSON.stringify(checkedRowKeysRef.value));
    if (checkedUser.value == '') {
      $toast.error('请选择人员');
      return;
    }
    console.log('更新params', JSON.stringify(userData.value));
    updateRecipient(userData.value).then((res) => {
      console.log('提交结果', console.log(res));
      $toast.success('操作成功');
      handleClose();
      emits('setReceiveUser', params.value.deptId);
    });
  }
  if (props.type == 'ADD') {
    if (checkedUser.value == '') {
      $toast.error('请选择人员');
      return;
    }
    params.value.hazardType = props.hazardType;
    console.log('新增params', JSON.stringify(params.value));
    addRecipient(params.value).then((res) => {
      handleClose();
      $toast.success('操作成功');
      emits('setReceiveUser', params.value.deptId);
    });
  }
}
function handleClose() {
  userNameVal.value = null;
  checkedRowKeysRef.value = [];
  checkedUserInfo.value = '';
  pagination.page = 1;
  searchParams.value.orgCode = store.userInfo.orgCode;
  searchParams.value.orgName = store.userInfo.orgName;
  searchParams.value.userName = '';
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}
function setCheckedUser(val: any) {
  checkedUser.value = val;
  console.log('checkedUser.value', JSON.stringify(checkedUser.value));
}
function setOrgTreeParms(val: any) {
  params.value.deptId = val.deptId;
  params.value.deptName = val.deptName;
  // searchParams.value.orgCode = val.deptId;
  // searchParams.value.orgName = val.deptName;
  // curSelUnitId.value = val.deptId;
}

defineOptions({ name: 'submitDialogComp' });
defineExpose({
  getUser,
  setCheckedUser,
  setOrgTreeParms,
});
</script>

<style module lang="scss" scoped>
.com-input-label {
  line-height: 34px;
  margin-right: 10px;
}
.searchItem {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // float: right;
  width: 100%;
  padding-bottom: 20px;
}
.selectDiv {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  // align-items: center;
}
.selectTitle {
  font-size: 16px;
  width: 100px;
}
.selectuser {
  // display: flex;
  // align-items: center;
  padding-left: 8px;
  background-color: #fff;
  width: 100%;
  min-height: 60px;
  line-height: 60px;
  border: 1px solid #c8ced9;
  color: #527cff;
}
/* 左右布局 */
.comlayercontent {
  // height: 100%;
  width: 100%;
  display: flex;
}
.comright {
  padding-top: 15px;
  // padding-left: 15px;
  padding: 5px;
  flex: 1;
  background-color: #fff;
  border: 1px solid #c8ced9;
  margin-left: 10px;
  // display: flex;
  // overflow: hidden;
  // flex-direction: column;
}
.comleft {
  width: 326px;
  border: 1px solid #c8ced9;
  flex-shrink: 0;
  padding: 3px;
  background-color: #fff;
}
.keyFilter {
  width: 100px;
  font-size: 16px;
  font-weight: 500;
}
.selectedPerson {
  cursor: pointer;
  display: flex;
  border: 1px solid #527cff;
  float: left;
  height: 25px;
  line-height: 25px;
  margin: 5px;
  border-radius: 3px;
  padding: 0 7px;
}
</style>
