<template>
  <div class="com-warp-col">
    <com-bread :data="breadData"></com-bread>
    <div class="com-warp-col-main">
      <com-tab @tab-action="tabChange" :tab="tabAct" :tab-list="tabData" :is-line="true"></com-tab>
      <div class="com-warp-col-container">
        <!-- <abnormal v-if="tabAct === 'abnorma'" /> -->
        <receive v-if="tabAct === 'receive'" :tree-list="treeData"></receive>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeMount, onUnmounted } from 'vue';

import ComTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import abnormal from './abnormal/index.vue';
import receive from './receive/index.vue';
import { tabData } from './setData';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useRoute, useRouter } from 'vue-router';
import type { TreeOption } from 'naive-ui';
import { postTreeList } from '../common/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useStore } from '@/store';
const [loading, run] = useAutoLoading(true);
const router = useRouter();
const route = useRoute();
const store = useStore();
const breadData = ref<IBreadData[]>([{ name: '智能巡检' }, { name: '参数配置' }]);
const treeData = ref<TreeOption[]>([]);
// const tabAct = ref('abnormal');
const tabAct = ref('');
tabAct.value = route.query.tab ? (route.query.tab as string) : 'receive';
console.log('route.query', route.query.tab);
function tabChange(v: string) {
  console.log('vvv', v);
  tabAct.value = v;
  router.replace(`parameter-config?tab=${v}`);
}
const treeParam = ref({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  orgCode: store.userInfo.orgCode,
});
function treeList() {
  run(postTreeList(treeParam.value))
    .then((res) => {
      treeData.value = res.data || [];
    })
    .catch(() => {});
}
treeList();
onBeforeMount(() => {});

onMounted(() => {});

defineOptions({ name: 'ParameterConfigIndex' });
</script>

<style module lang="scss"></style>
