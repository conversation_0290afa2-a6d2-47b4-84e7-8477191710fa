<template>
  <div :class="$style.abnormalWrap">
    <com-loading v-if="loading"></com-loading>
    <div :class="$style.abnormalList">
      <div :class="$style.abnormalItem" v-for="(item, index) in cardList" :key="item.code">
        <div :class="$style.abnormalItemTop">
          <div :class="$style.abnormalItemIcon">
            <img v-if="item.code === 'personOff'" src="./assets/icon0.png" />
            <img v-if="item.code === 'fire&smoke'" src="./assets/icon1.png" />
            <img v-if="item.code === 'carlan'" src="./assets/icon2.png" />
            <img v-if="item.code === 'unsafetyHelmet'" src="./assets/icon3.png" />
            <img v-if="item.code === 'personnelSmoking'" src="./assets/icon5.png" />
          </div>
          <div :class="$style.abnormalItemName">{{ item.name }}</div>
          <div :class="$style.abnormalItemSwitch">
            <n-switch size="medium" v-model:value="item.checked" @update:value="handleSwitchChange(item, index)">
              <template #checked> 开 </template>
              <template #unchecked> 关 </template>
            </n-switch>
          </div>
        </div>
        <div :class="$style.abnormalItemDesc" v-html="item.content"></div>
        <div class="flex" style="justify-content: space-between">
          <n-select
            class="!w-[150px]"
            v-model:value="idTypeList[index].hazardTypeId"
            :options="idOptions"
            placeholder="请选择隐患类别"
            label-field="className"
            value-field="id"
            :loading="loading"
            filterable
            remote
            @update:value="(value, option) => handleUpdateType(value, option, item, index)"
          />
          <n-select
            class="!w-[150px]"
            v-model:value="idTypeList[index].hazardGradeId"
            :options="hazardLevelOpt"
            placeholder="请选择隐患等级"
            label-field="gradeName"
            value-field="id"
            :loading="loading"
            filterable
            remote
            @update:value="(value, option) => handleUpdateLevel(value, option, item, index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComLoading from '@/components/loading/comLoading.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { configList, updateConfigList } from '../fetchData';
import { useStore } from '@/store';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { taskHazardGradeApi, hazardClassList } from '@/views/video/task/fetchData';
import { type SelectOption } from 'naive-ui';
const store = useStore();
const [loading, run] = useAutoLoading(false);
const switchLoading = ref<boolean>(false);
const cardList = ref<any>([]);
const Param = ref({
  needChildUnit: '1', ////不要下级单位,1要，0不要
  needself: '1', //是否包含自己,1包含 0不包含
  orgCode: store.userInfo.orgCode, //机构id=10000,顶级是-1
});
const hazardLevelOpt = ref<any[]>([]);
const idOptions = ref<any[]>([]);
const idTypeList = ref<any>([]);
function handleUpdateType(value: string, option: SelectOption, item: any, index: any) {
  idTypeList.value[index].hazardTypeId = value;
  idTypeList.value[index].hazardTypeName = option ? option.className : '';
  let params = {
    id: item.id,
    status: item.checked ? 1 : 0,
    hazardTypeId: idTypeList.value[index].hazardTypeId,
    hazardTypeName: idTypeList.value[index].hazardTypeName,
    hazardGradeId: idTypeList.value[index].hazardGradeId,
    hazardGradeName: idTypeList.value[index].hazardGradeName,
  };
  updateConfig(params);
}
function handleUpdateLevel(value: string, option: SelectOption, item: any, index: any) {
  idTypeList.value[index].hazardGradeId = value;
  idTypeList.value[index].hazardGradeName = option ? option.gradeName : '';
  let params = {
    id: item.id,
    status: item.checked ? 1 : 0,
    hazardTypeId: idTypeList.value[index].hazardTypeId,
    hazardTypeName: idTypeList.value[index].hazardTypeName,
    hazardGradeId: idTypeList.value[index].hazardGradeId,
    hazardGradeName: idTypeList.value[index].hazardGradeName,
  };
  updateConfig(params);
}
function handleSwitchChange(value: any, index: any) {
  let type = value.checked ? 1 : 0;
  let typeName = value.checked ? '开启' : '关闭';
  switchLoading.value = true;
  $dialog.warning({
    titleStyle: {
      fontSize: '15px',
      fontWeight: 'bolder',
    },
    closeOnEsc: false, //是否在摁下 Esc 键的时候关闭对话框
    maskClosable: false, //是否可以通过点击 mask 关闭对话框
    title: '确认信息提醒',
    content: `确定${typeName}吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      switchLoading.value = false;
      let p = {
        id: value.id,
        status: type,
        hazardTypeId: idTypeList.value[index].hazardTypeId,
        hazardTypeName: idTypeList.value[index].hazardTypeName,
        hazardGradeId: idTypeList.value[index].hazardGradeId,
        hazardGradeName: idTypeList.value[index].hazardGradeName,
      };
      updateConfig(p);
    },
    onClose: () => {
      let cardIndex = cardList.value.findIndex((item: any) => item.id === value.id);
      cardList.value[cardIndex].checked = !cardList.value[cardIndex].checked;
      switchLoading.value = false;
    },
    onNegativeClick: () => {
      let cardIndex = cardList.value.findIndex((item: any) => item.id === value.id);
      cardList.value[cardIndex].checked = !cardList.value[cardIndex].checked;
      switchLoading.value = false;
    },
  });
}
function updateConfig(param: any) {
  run(updateConfigList(param)).then((res: any) => {
    console.log('修改配置 = ', res);
  });
}

function treeList() {
  run(configList({})).then((res: any) => {
    // console.log('获取参数配置列表 = ', res);
    res.data.map((item: any) => {
      item.checked = item.status === 1 ? true : false;
      item.content = item.content.replace(
        '关闭后，将不再上报此类异常结果。',
        '<br/><span style="color:#ea0000">关闭后，将不再上报此类异常结果。</span>'
      );
      idTypeList.value.push({
        hazardTypeId: item.hazardTypeId != '' ? item.hazardTypeId : null, //隐患分类Id
        hazardTypeName: item.hazardTypeName, //隐患分类名称
        hazardGradeId: item.hazardGradeId != '' ? item.hazardGradeId : null, //隐患等级id
        hazardGradeName: item.hazardGradeName, //隐患等级名称
      });
    });
    cardList.value = res.data;
  });
}
treeList();
// 获取隐患等级数据
function getHazardList() {
  hazardClassList({ children: [], unitId: store.userInfo.topUnitId }).then((res: any) => {
    idOptions.value = res.data;
  });
}
getHazardList();
// 获取隐患分类数据
function taskHazardGrade() {
  taskHazardGradeApi({
    createTime: '',
    delFlag: 0,
    gradeName: '',
    gradeSort: 0,
    id: '',
    parentId: '',
    parentName: '',
    unitId: store.userInfo.topUnitId,
    zhId: '',
  }).then((res) => {
    hazardLevelOpt.value = res.data ? (res.data as any) : [];
  });
}
taskHazardGrade();

defineOptions({ name: 'AbnormalIndex' });
</script>

<style module lang="scss">
.abnormalWrap {
  position: relative;
  .abnormalList {
    padding: 8px 14px;
    display: flex;
    flex-wrap: wrap;
  }
  .abnormalItem {
    width: 381px;
    min-height: 156px;
    background: linear-gradient(180deg, #ffffff 0%, #e0e5f7 100%);
    box-shadow: 0px 2px 0px 0px rgba(0, 20, 82, 0.2);
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #dfe7ff;
    margin: 0 10px 16px 10px;
    padding: 16px;
    font-weight: 400;
    font-size: 14px;
    color: #494a4d;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .abnormalItemIcon {
    margin-right: 12px;
    width: 44px;
    height: 44px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .abnormalItemTop {
    display: flex;
    align-items: center;
  }
  .abnormalItemName {
    flex: 1;
    text-align: left;
    font-weight: 600;
    font-size: 16px;
    color: #222222;
  }
  .abnormalItemDesc {
    padding-top: 10px;
    line-height: 24px;
  }
}
</style>
<style scoped>
:deep(.n-base-selection-label) {
  background-color: #e6ebfb !important;
}

:deep(.n-base-selection) {
  border: none !important;
  --n-border: none !important;
  --n-border-active: none !important;
  --n-border-focus: none !important;
  --n-border-hover: none !important;
}
:deep(.n-base-selection-placeholder) {
  /* background-color: #dfe7ff !important; */
  color: #494a4d !important;
}
</style>
