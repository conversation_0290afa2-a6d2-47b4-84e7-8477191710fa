<template>
  <div :class="$style.waterEvalContainer">
    <div :class="$style.cityGreenLandCharts" id="cityGreenLand-charts"></div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, nextTick, ref } from 'vue';
import * as echarts from 'echarts';
import 'echarts-gl';

const props = defineProps({
  pieData: {
    type: <any>Object,
    default: () => {
      return {};
    },
  },
});
// 智能视频轮巡：973
// 无人机自主巡检：0
// 机器人自助巡检：1
// 人工智能巡检；50
// console.log('props = ', props.pieData);
const optionData = ref([
  {
    name: '智能视频轮巡',
    value: props.pieData.videoTaskCount || 0,
    itemStyle: {
      color: '#80D7F5',
    },
  },
  {
    name: '无人机自动巡检',
    value: props.pieData.uavTaskCount || 0,
    itemStyle: {
      color: '#49E49B',
    },
  },
  {
    name: '机器人自动巡检',
    value: props.pieData.robotTaskCount || 0,
    itemStyle: {
      color: '#FFBB30',
    },
  },
  {
    name: '人工智能巡检',
    value: props.pieData.hazardTaskCount || 0,
    itemStyle: {
      color: '#3472F8',
    },
  },
]);
let option: any;

onMounted(() => {
  nextTick(() => {
    init();
  });
});

const init = () => {
  // 构建3d饼状图
  let myChart = echarts.init(document.getElementById('cityGreenLand-charts'));
  // 传入数据生成 option
  option = getPie3D(optionData.value, 0.8);
  console.log(option);
  console.log(myChart);

  myChart.setOption(option);
  window.addEventListener('resize', () => {
    myChart.resize();
  });
  bindListen(myChart);
};

const getPie3D = (pieData: any, internalDiameterRatio: any) => {
  //internalDiameterRatio:透明的空心占比
  let series: any = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData: any = [];
  let legendBfb: any = [];
  let k = 1 - internalDiameterRatio;
  pieData.sort((a: any, b: any) => {
    return b.value - a.value;
  });
  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    let seriesItem: any = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      radar: {
        shape: 'polygon',
        splitNumber: 0,
      },
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
      center: ['10%', '50%'],
    };

    if (typeof pieData[i].itemStyle != 'undefined') {
      let itemStyle: any = {};
      typeof pieData[i].itemStyle.color != 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null;
      typeof pieData[i].itemStyle.opacity != 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null;
      seriesItem.itemStyle = itemStyle;
    }
    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  legendData = [];
  legendBfb = [];
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    );
    startValue = endValue;
    let bfb = fomatFloat(series[i].pieData.value / sumValue, 4);
    legendData.push({
      name: series[i].name,
      value: bfb,
    });
    legendBfb.push({
      name: series[i].name,
      value: series[i].pieData.value,
    });
  }

  console.log(series);

  let boxHeight = getHeight3D(series, 26); //通过传参设定3d饼/环的高度，26代表26px
  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  let option = {
    legend: {
      data: legendData,
      orient: 'vertical',
      // type: 'scroll',
      right: 45,
      top: 0,
      itemGap: 22,
      textStyle: {
        color: '#A1E2FF',
      },
      show: true,
      icon: 'circle',
      formatter: function (param: any) {
        let item = legendBfb.filter((item: any) => item.name == param)[0];
        // let n = parseInt(item.value as string);
        // let bfs = fomatFloat(n * 100, 2) + '%';
        return `${item.name} ${item.value}`;
      },
    },
    labelLine: {
      show: false,
      lineStyle: {
        color: '#7BC0CB',
      },
    },
    label: {
      show: false,
      position: 'outside',
      rich: {
        b: {
          color: '#7BC0CB',
          fontSize: 12,
          lineHeight: 20,
        },
        c: {
          fontSize: 16,
        },
      },
      formatter: '{b|{b} \n}{c|{c}}{b|  亩}',
    },
    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          let bfb = (
            (option.series[params.seriesIndex].pieData.endRatio -
              option.series[params.seriesIndex].pieData.startRatio) *
            100
          ).toFixed(1);
          let value = option.series[params.seriesIndex].pieData.value;
          return (
            `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
            `${params.seriesName}<br/>` +
            `${value}<br />` +
            `${bfb}%`
          );
        }
      },
    },
    xAxis3D: {
      name: '',
      min: -1,
      max: 1,
    },
    yAxis3D: {
      name: '',
      min: -1,
      max: 1,
    },
    zAxis3D: {
      name: '',
      min: -1,
      max: 1,
    },
    grid3D: {
      top: -10,
      right: 10,
      show: true,
      boxHeight: boxHeight, //圆环的高度
      viewControl: {
        projection: 'orthographic',
        //3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 30, // X角度
        beta: 50, //Y角度
        distance: 100, //调整视角到主体的距离，类似调整zoom
        rotateSensitivity: 0, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 1, //设置为0无法平移
        autoRotate: false, //自动旋转
        center: [120, 60, 0],
      },
      light: {
        main: {
          shadow: false,
          alpha: 100,
          beta: 50,
        },
      },
      splitLine: {
        //平面上的分隔线。
        show: false, //立体网格线
        // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
        splitArea: {
          show: true,
          // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
          areaStyle: {
            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)', 'rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)'],
          },
        },
      },
      axisLabel: {
        show: false,
      },
      // axisLabel: {
      //     show: true,//是否显示刻度  (刻度上的数字，或者类目)
      //     //
      //     interval: 5,//坐标轴刻度标签的显示间隔，在类目轴中有效。
      //     formatter: function () {
      //         // return;
      //     },

      //     textStyle: {
      //         // color:'#000',//刻度标签样式，见图黑色刻度标签
      //         color: function (value) {
      //             return value >= 6 ? 'green' : 'red';//根据范围显示颜色，主页为值有效
      //         },
      //         //  borderWidth:"",//文字的描边宽度。
      //         //  borderColor:'',//文字的描边颜色。
      //         fontSize: 14,//刻度标签字体大小
      //         fontWeight: '',//粗细
      //     }
      // },
      axisTick: {
        show: false, //是否显示出刻度
        // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
        length: 5, //坐标轴刻度的长度
        lineStyle: {
          //举个例子，样式太丑将就
          color: '#000', //颜色
          opacity: 1,
          width: 5, //厚度（虽然为宽表现为高度），对应length*(宽)
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          opacity: 0,
        },
      },
      // splitLine: {//平面上的分隔线。
      //     show: true,//立体网格线
      //     // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
      //     splitArea: {
      //         show: true,
      //         // interval:100,//坐标轴刻度标签的显示间隔，在类目轴中有效
      //         areaStyle: {
      //             color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)', 'rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      //         }
      //     },
      // },
      axisPointer: {
        //坐标轴指示线。
        show: false, //鼠标在chart上的显示线
        // lineStyle:{
        //     color:'#000',//颜色
        //     opacity:1,
        //     width:5//厚度（虽然为宽表现为高度），对应length*(宽)
        // }
      },
    },
    series: series,
  };
  return option;
};

//获取3d丙图的最高扇区的高度
const getHeight3D = (series: any, height: any) => {
  series.sort((a: any, b: any) => {
    return b.pieData.value - a.pieData.value;
  });
  return (height * 25) / series[0].pieData.value;
};

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
const getParametricEquation = (startRatio: any, endRatio: any, isSelected: any, isHovered: any, k: any, h: any) => {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;
  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;
  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3;
  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;
  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u: any, v: any) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u: any, v: any) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
};

const fomatFloat = (num: any, n: any) => {
  var f = parseFloat(num);
  if (isNaN(f)) {
    return false;
  }
  f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n); // n 幂
  var s = f.toString();
  var rs = s.indexOf('.');
  //判定如果是整数，增加小数点再补0
  if (rs < 0) {
    rs = s.length;
    s += '.';
  }
  while (s.length <= rs + n) {
    s += '0';
  }
  return s;
};

const bindListen = (myChart: any) => {
  // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
  let selectedIndex = '';
  let hoveredIndex = '';
  // 监听点击事件，实现选中效果（单选）
  myChart.on('click', function (params: any) {
    // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
    let isSelected = !option.series[params.seriesIndex].pieStatus.selected;
    let isHovered = option.series[params.seriesIndex].pieStatus.hovered;
    let k = option.series[params.seriesIndex].pieStatus.k;
    let startRatio = option.series[params.seriesIndex].pieData.startRatio;
    let endRatio = option.series[params.seriesIndex].pieData.endRatio;
    // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
    if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
      option.series[selectedIndex].parametricEquation = getParametricEquation(
        option.series[selectedIndex].pieData.startRatio,
        option.series[selectedIndex].pieData.endRatio,
        false,
        false,
        k,
        option.series[selectedIndex].pieData.value
      );
      option.series[selectedIndex].pieStatus.selected = false;
    }
    // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
    option.series[params.seriesIndex].parametricEquation = getParametricEquation(
      startRatio,
      endRatio,
      isSelected,
      isHovered,
      k,
      option.series[params.seriesIndex].pieData.value
    );
    option.series[params.seriesIndex].pieStatus.selected = isSelected;
    // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
    isSelected ? (selectedIndex = params.seriesIndex) : null;
    // 使用更新后的 option，渲染图表
    myChart.setOption(option);
    window.addEventListener('resize', () => {
      myChart.resize();
    });
  });
  // 监听 mouseover，近似实现高亮（放大）效果
  myChart.on('mouseover', function (params: any) {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    // 如果触发 mouseover 的扇形当前已高亮，则不做操作
    if (hoveredIndex === params.seriesIndex) {
      return;
      // 否则进行高亮及必要的取消高亮操作
    } else {
      // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
      if (hoveredIndex !== '') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
        isSelected = option.series[hoveredIndex].pieStatus.selected;
        isHovered = false;
        startRatio = option.series[hoveredIndex].pieData.startRatio;
        endRatio = option.series[hoveredIndex].pieData.endRatio;
        k = option.series[hoveredIndex].pieStatus.k;
        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
        option.series[hoveredIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[hoveredIndex].pieData.value
        );
        option.series[hoveredIndex].pieStatus.hovered = isHovered;
        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
        hoveredIndex = '';
      }
      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
        isSelected = option.series[params.seriesIndex].pieStatus.selected;
        isHovered = true;
        startRatio = option.series[params.seriesIndex].pieData.startRatio;
        endRatio = option.series[params.seriesIndex].pieData.endRatio;
        k = option.series[params.seriesIndex].pieStatus.k;
        // 对当前点击的扇形，执行高亮操作（对 option 更新）
        option.series[params.seriesIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[params.seriesIndex].pieData.value + 5
        );
        option.series[params.seriesIndex].pieStatus.hovered = isHovered;
        // 记录上次高亮的扇形对应的系列号 seriesIndex
        hoveredIndex = params.seriesIndex;
      }
      // 使用更新后的 option，渲染图表
      myChart.setOption(option);

      window.addEventListener('resize', () => {
        myChart.resize();
      });
    }
  });
  // 修正取消高亮失败的 bug
  myChart.on('globalout', function () {
    // 准备重新渲染扇形所需的参数
    let isSelected;
    let isHovered;
    let startRatio;
    let endRatio;
    let k;
    if (hoveredIndex !== '') {
      // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = option.series[hoveredIndex].pieStatus.selected;
      isHovered = false;
      k = option.series[hoveredIndex].pieStatus.k;
      startRatio = option.series[hoveredIndex].pieData.startRatio;
      endRatio = option.series[hoveredIndex].pieData.endRatio;
      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      option.series[hoveredIndex].parametricEquation = getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        option.series[hoveredIndex].pieData.value
      );
      option.series[hoveredIndex].pieStatus.hovered = isHovered;
      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex = '';
    }
    // 使用更新后的 option，渲染图表
    myChart.setOption(option);
    window.addEventListener('resize', () => {
      myChart.resize();
    });
  });
};
</script>

<style lang="scss" module>
.cityGreenLandCharts {
  width: 40rem;
  height: 20rem;
}
</style>
