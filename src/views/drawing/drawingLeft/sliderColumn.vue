<template>
  <div ref="echart" :class="$style.echartDiv"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts';
import { onMounted, toRefs, nextTick, ref, reactive, defineProps } from 'vue';
import { sliderColumns } from '../type';
export default {
  props: {
    sliderData: {
      type: Array<sliderColumns>,
      default: () => {
        return {};
      },
    },
  },
  setup(props) {
    let state = reactive({
      xAxisData: ['隐患类型1', '隐患类型2', '隐患类型3', '隐患类型4', '隐患类型5'],
      yAxisData: [4, 22, 1, 11, 23, 11],
      yAxisData1: [1, 1, 1, 1, 1, 1],
      echart: ref(),
    });
    const echartInit = () => {
      let myChart = echarts.init(state.echart);
      // 指定图表的配置项和数据
      // const data = [
      //   {
      //     name: '生产组',
      //     percent: '88%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      //   {
      //     name: '安全组',
      //     percent: '90%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      //   {
      //     name: '保障组',
      //     percent: '92%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      // ]; //自己传过来
      const data = props.sliderData;
      console.log('data = ', data);
      // const yData = data.map((item) => item.name);
      // const zData = data.map((item) => item.percent);
      // const pData = data.map((item) => percentStringToDecimal(item.percent));
      const yData = data.map((item: any) => item.deptName);
      const zData = data.map((item: any) => Math.round(item.ratio) + '%');
      const pData = data.map((item: any) => percentStringToDecimal(item.ratio + '%'));
      // 设置等长的背景柱状图
      const maxData = new Array(yData.length).fill(100);
      const option = {
        dataZoom: [
          {
            id: 'dataZoomY',
            yAxisIndex: [0],
            show: false, //是否显示滑动条，不影响使用
            type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            endValue: 4, // 一次性展示5个
            width: 6,
            borderColor: 'transparent',
            fillerColor: 'rgba(205,205,205,1)',
            zoomLock: true,
            showDataShadow: false, //是否显示数据阴影 默认auto
            backgroundColor: '#fff',
            showDetail: true, //即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleIcon: 'circle',
            handleStyle: {
              color: 'rgba(205,205,205,1)',
              borderColor: 'rgba(205,205,205,1)',
            },
            handleSize: '80%',
            moveHandleSize: 0,
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false, //刷选功能，设为false可以防止拖动条长度改变 ************（这是一个坑）
          },
          {
            type: 'inside',
            yAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        grid: {
          left: '10',
          right: '60',
          bottom: '10%',
          top: '10%',
          containLabel: false,
        },
        xAxis: [
          {
            show: true,
          },
          {
            show: false,
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            show: false,
            inverse: true,
            data: yData,
            position: 'top',
            axisLabel: {
              show: true,
              lineHeight: 0,
              verticalAlign: 'bottom',
              fontSize: 20,
              color: '#ffffff',
              formatter: '{value}',
            },
            x: 'center',
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          {
            show: true,
            data: zData.reverse(),
            offset: 5,
            position: 'right',
            axisLabel: {
              lineHeight: 0,
              verticalAlign: 'bottom',
              fontSize: 14,
              color: '#fff',
              formatter: '{value}',
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            show: true,
            type: 'bar',
            barGap: '-120%',
            xAxisIndex: 1,
            barWidth: 8,
            barCategoryGap: '10%',
            itemStyle: {
              borderRadius: 10,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#3F82FF ', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#45F5E1', // 0% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            // yAxis: {
            //   type: 'category',
            //   inverse: true, // 倒叙
            //   axisLabel: {
            //     color: '#1E90FF',
            //     formatter: (val: any) => {
            //       return `${val}`;
            //     },
            //   },
            //   axisLine: {
            //     show: false, // 轴线
            //   },
            //   axisTick: {
            //     show: false, // 刻度线
            //   },
            //   data: ['一级', '二级', '三级', '四级', '五级'],
            // },
            label: {
              show: true,
              offset: [5, -13],
              color: '#fff',
              align: 'left',
              position: 'left',
              rich: {
                redLine: {
                  height: 10,
                  backgroundColor: '#d7b746',
                  borderRadius: [5, 5], // 圆角
                  width: 4, // 竖线的宽度
                },
              },
              formatter: '{redLine|}  {b}',
              // formatter: (params: any) => {
              //   return data[params.dataIndex].deptName;
              // },
            },
            labelLine: {
              show: false,
              width: 3,
              color: '#d7b746',
              type: 'solid',
              opacity: 1,
            },
            // x: "50%",
            z: 2,
            data: pData,
            animationDelay: 1000,
            animationDuration: 1000,
          },
          {
            name: '百分比',
            z: 1,
            show: true,
            type: 'bar',
            xAxisIndex: 1,
            barGap: '-120%',
            barWidth: 10,
            barCategoryGap: '400',
            itemStyle: {
              borderRadius: 4,
              color: '#1F3E61',
            },
            label: {
              show: false,
            },
            data: maxData,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
      // 监听浏览器窗口大小变化
      // window.addEventListener('resize', myChart);
    };
    function percentStringToDecimal(percentString: any) {
      // 确保百分号在字符串末尾
      if (percentString.endsWith('%')) {
        // 去除百分号并转换为小数
        return parseFloat(percentString.slice(0, -1));
      }
      // 如果不是百分比格式，可以返回NaN或抛出错误
      return NaN; // 或者 throw new Error('Invalid percentage string');
    }
    //挂载
    onMounted(() => {
      nextTick(() => {
        echartInit();
      });
    });

    return {
      ...toRefs(state),
      echartInit,
    };
  },
};
</script>

<style lang="scss" module>
.echartDiv {
  margin-top: 1.5rem;
  width: 40rem;
  height: 20rem;
  overflow: scroll;
}
</style>
