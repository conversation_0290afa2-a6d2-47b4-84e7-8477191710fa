<template>
  <div :class="$style.left">
    <!-- 智能巡检任务执行情况 -->
    <div :class="$style.leftItem">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">智能巡检任务执行情况</span>
      </div>
      <div :class="$style.leftItemCenter">
        <!-- 进行中任务数 -->
        <div :class="$style.topCenterItem">
          <img :class="$style.topCenterItemImg" src="../assets/conduct.png" />
          <div :class="$style.topCenterItemTitle">
            <p :class="$style.itemTitle">进行中任务数</p>
            <p>
              <span :class="$style.itemNumber38">{{ data.underwayTaskCount }}</span> <span>个</span>
            </p>
          </div>
        </div>
        <!-- 已完成任务数 -->
        <div :class="$style.topCenterItem">
          <img :class="$style.topCenterItemImg" src="../assets/accomplish.png" />
          <div :class="$style.topCenterItemTitle">
            <p :class="$style.itemTitle">已完成任务数</p>
            <p>
              <span :class="$style.itemNumber38">{{ data.finishTaskCount }}</span> <span>个</span>
            </p>
          </div>
        </div>
        <!-- 待开始任务数 -->
        <div :class="$style.topCenterItem">
          <img :class="$style.topCenterItemImg" src="../assets/begin.png" />
          <div :class="$style.topCenterItemTitle">
            <p :class="$style.itemTitle">待开始任务数</p>
            <p>
              <span :class="$style.itemNumber38">{{ data.pendingTaskCount }}</span> <span>个</span>
            </p>
          </div>
        </div>
        <!-- 已逾期任务数 -->
        <div :class="$style.topCenterItem">
          <img :class="$style.topCenterItemImg" src="../assets/overdue.png" />
          <div :class="$style.topCenterItemTitle">
            <p :class="$style.itemTitle">已逾期任务数</p>
            <p>
              <span :class="$style.itemNumber38">{{ data.overdueTaskCount }}</span> <span>个</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- 智能巡检任务来源 -->
    <div :class="$style.leftItem">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">智能巡检任务来源</span>
      </div>
      <div :class="$style.leftItemCenter_m">
        <!-- 任务总数 -->
        <div :class="$style.taskTotalLeft">
          <div :class="$style.taskTotal">
            <p :class="$style.itemTitle">任务总数</p>
            <p :class="$style.itemNumber3" v-html="formatWithCommas(data.taskAllCount)"></p>
          </div>
          <!-- <div style="width: 23.2rem; height: 13.2rem" ref="pieChart"></div> -->
          <pieChart :class="$style.pieChart" :pieData="data" :key="index" />
        </div>
      </div>
    </div>
    <!-- 智能巡检任务完成率排名 -->
    <div :class="$style.leftItem">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">智能巡检任务完成率排名</span>
      </div>
      <div :class="$style.leftItemCenter">
        <tempalte v-if="props.sliData.length">
          <sliderCoumn :class="$style.sliderCoumn" :sliderData="props.sliData" :key="index" />
        </tempalte>
        <div style="margin: 0 auto" v-else>
          <Empty />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import pieChart from './pieChart.vue';
import sliderCoumn from './sliderColumn.vue';
import { sliderColumns } from '../type';

const index = ref(0);
import { formatWithCommas } from '@/utils/obj.ts';
interface PropsLeft {
  data: {
    deviceAllCount: number;
    finishTaskCount: number;
    hazardTaskCount: number;
    hazardTaskRatio: number;
    overdueTaskCount: number;
    pendingTaskCount: number;
    robotTaskCount: number;
    robotTaskRatio: number;
    taskAllCount: number;
    uavTaskCount: number;
    uavTaskRatio: number;
    underwayTaskCount: number;
    videoTaskCount: number;
    videoTaskRatio: number;
  };
  sliData: sliderColumns[];
}
const props = withDefaults(defineProps<PropsLeft>(), {
  data: () => {
    return {
      deviceAllCount: 0,
      finishTaskCount: 0,
      hazardTaskCount: 0,
      hazardTaskRatio: 0,
      overdueTaskCount: 0,
      pendingTaskCount: 0,
      robotTaskCount: 0,
      robotTaskRatio: 0,
      taskAllCount: 0,
      uavTaskCount: 0,
      uavTaskRatio: 0,
      underwayTaskCount: 0,
      videoTaskCount: 0,
      videoTaskRatio: 0,
    };
  },
  sliData: () => [],
});
watch(
  props,
  (nV) => {
    index.value++;
  },
  { immediate: true }
);
defineOptions({ name: 'DrawingLeftIndex' });
</script>

<style module lang="scss">
.left {
  width: 41rem;
  margin: 0;
  padding: 0;
}
.leftItem {
  margin-bottom: 1.67rem;
}
.topHeader {
  width: 41rem;
  height: 4.2rem;
  position: relative;
}
.HeaderImage {
  width: 100%;
  height: 100%;
}
.HeaderTitle {
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 24px;
  color: #ffffff;
  line-height: 3rem;
  text-shadow: 0.1rem 0.3rem 0.2rem rgba(0, 0, 0, 0.4);
  font-style: italic;
  position: absolute;
  top: 0;
  left: 4.9rem;
}
.leftItemCenter,
.leftItemCenter_m {
  width: 41rem;
  height: 21rem;
  background: #0e3057;
  border: 1px solid rgba(14, 48, 87, 0.8);
  display: flex;
  flex-wrap: wrap;
  background: linear-gradient(to top, #0e3057, transparent);
}
.leftItemCenter_m {
  height: 24rem;
}
.topCenterItem {
  width: 17.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
}
.topCenterItemImg {
  width: 3.67rem;
  height: 3.75rem;
  margin-right: 1.33rem;
  margin-left: 2.5rem;
}
.itemTitle {
  height: 2.08rem;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.5rem;
  color: #ffffff;
  line-height: 2.08rem;
  text-align: left;
  font-style: normal;
}
.itemNumber38 {
  height: 3.75rem;
  font-family: DINAlternate, DINAlternate;
  font-weight: bold;
  font-size: 3.17rem;
  color: #00fdff;
  line-height: 3.75rem;
  text-align: left;
  font-style: normal;
}
.itemNumber3 {
  font-family: DINAlternate, DINAlternate;
  font-weight: bold;
  color: #00fdff;
  line-height: 4.5rem;
  text-align: left;
  font-style: normal;
  font-size: 3rem;
}
.taskTotalLeft {
  margin-left: 1.92rem;
  margin-top: 1.83rem;
  p {
    span {
      font-size: 2rem;
    }
  }
}
.topCenterItem p:last-child span:last-child {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.6rem;
  color: #ffffff;
  line-height: 2.2rem;
  text-align: left;
  font-style: normal;
}
</style>
