import type { IObj, IPageRes } from '@/types';
import exp from 'constants';

//tab 数据
export interface ITabData {
  text: string;
  value: string;
}

export interface IMapItem {
  aerialviewImg: string;
  unitId: string;
}
export interface IMapMark {
  deviceAddress: string;
  deviceId: string;
  deviceName: string;
  deviceTypeId: string;
  latitude: number | string;
  longitude: number | string;
  mapX: number | string;
  mapY: number | string;
  mapZ: number | string;
}

export interface DataColums {
  deviceAllCount: number;
  finishTaskCount: number;
  hazardTaskCount: number;
  hazardTaskRatio: number;
  overdueTaskCount: number;
  pendingTaskCount: number;
  robotTaskCount: number;
  robotTaskRatio: number;
  taskAllCount: number;
  uavTaskCount: number;
  uavTaskRatio: number;
  underwayTaskCount: number;
  videoTaskCount: number;
  videoTaskRatio: number;
}

export interface sliderColumns {
  deptName: string;
  ratio: number;
}
