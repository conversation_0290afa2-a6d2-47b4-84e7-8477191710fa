<template>
  <div :class="$style.pie" ref="percent"></div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, defineProps, watch, computed } from 'vue';
import { cloneDeep } from 'lodash-es';
import * as echarts from 'echarts';
const percent = ref(null);
const myChart = ref(null);
// 基于准备好的dom，初始化echarts实例

interface IProps {
  digital: {
    type: number;
    default: 0;
  };
  color: {
    type: string;
    default: '#215df6';
  };
}

const props = defineProps<IProps>();

// 指定图表的配置项和数据
const option = ref({
  series: [
    {
      type: 'pie',
      radius: ['65%', '75%'], // 内半径和外半径
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      data: [
        { value: props.digital, name: '', itemStyle: { color: props.color, borderRadius: 5 } },
        { value: 100 - props.digital, name: '', itemStyle: { color: '#3d4a5c' } },
      ],
    },
  ],
});

watch(
  () => props.digital,
  (newVal) => {
    option.value = {
      series: [
        {
          type: 'pie',
          radius: ['65%', '75%'], // 内半径和外半径
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          data: [
            { value: newVal, name: '', itemStyle: { color: props.color, borderRadius: 5 } },
            { value: 100 - props.digital, name: '', itemStyle: { color: '#3d4a5c' } },
          ],
        },
      ],
    };

    if (myChart.value) {
      myChart.value.setOption(option.value);
    }
  }
);

onMounted(() => {
  myChart.value = echarts.init(percent.value);
  // 使用刚指定的配置项和数据显示图表。
  myChart.value.setOption(option.value);
});
</script>

<style lang="scss" module>
.pie {
  width: 100%;
  height: 100%;
}
</style>
