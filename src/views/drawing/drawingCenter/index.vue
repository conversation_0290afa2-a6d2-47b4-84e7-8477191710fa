<template>
  <div :class="$style.center">
    <!-- 已接入智能设备总数、已完成巡检任务总数、已上报巡检异常数量 -->
    <div :class="$style.centerHeader">
      <!-- 已接入智能设备总数 -->
      <div :class="$style.centerHeaderItem">
        <img :class="$style.headerItemImage" src="../assets/smart.png" />
        <div :class="$style.headerItemCenter">
          <img :class="$style.headerItemIcon" src="../assets/yjrIcon.png" />
          <div :class="$style.headerItemTitle">
            <p>{{ dataObj.deviceAllCount }}</p>
            <p>已接入智能设备总数</p>
          </div>
        </div>
      </div>
      <!-- 已完成巡检任务总数 -->
      <div :class="$style.centerHeaderItem">
        <img :class="$style.headerItemImage" src="../assets/ywcBg.png" />
        <div :class="$style.headerItemCenter">
          <img :class="$style.headerItemIcon" src="../assets/ywcicon.png" />
          <div :class="$style.headerItemTitle">
            <p>{{ dataObj.finishTaskCount }}</p>
            <p>已完成巡检任务总数</p>
          </div>
        </div>
      </div>
      <!-- 已上报巡检异常数量 -->
      <div :class="$style.centerHeaderItem">
        <img :class="$style.headerItemImage" src="../assets/report.png" />
        <div :class="$style.headerItemCenter">
          <img :class="$style.headerItemIcon" src="../assets/ybgIcon.png" />
          <div :class="$style.headerItemTitle">
            <p>{{ sliData?.total == '' ? '0' : sliData?.total }}</p>
            <p>已上报巡检异常数量</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 今日任务完成情况 -->
    <div :class="$style.centerContain">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topCenterImage.png" />
        <span :class="$style.HeaderTitle">今日任务完成情况</span>
      </div>
      <div :class="$style.percontain">
        <!-- 今日进行中任务 -->
        <div :class="$style.percontainItem">
          <div :class="$style.percontainItemTop">
            <!-- <img :class="$style.perItemImage" src="../assets/perjxz.png" />
            <p :class="$style.perNumber">{{ data.underwayTaskRatio }}%</p> -->
            <div :class="$style.pieCircle">
              <img :class="$style.perItemImage" src="../assets/chart-circle-blue.svg" />
              <pieCircle :digital="data.underwayTaskRatio" :color="'#215df6'" />
              <p :class="$style.perNumber">{{ data.underwayTaskRatio }}%</p>
            </div>
          </div>
          <!-- <pieper /> -->
          <p :class="$style.perTips">今日进行中任务</p>
        </div>
        <!-- 今日已完成任务 -->
        <div :class="$style.percontainItem">
          <div :class="$style.percontainItemTop">
            <!-- <img :class="$style.perItemImage" src="../assets/perywc.png" />
            <p :class="$style.perNumber">{{ data.finishTaskRatio }}%</p> -->
            <div :class="$style.pieCircle">
              <img :class="$style.perItemImage" src="../assets/chart-circle-green.svg" />
              <pieCircle :digital="data.finishTaskRatio" :color="'#5ac461'" />
              <p :class="$style.perNumber">{{ data.finishTaskRatio }}%</p>
            </div>
          </div>
          <p :class="$style.perTips">今日已完成任务</p>
        </div>
        <!-- 今日待开始任务 -->
        <div :class="$style.percontainItem">
          <div :class="$style.percontainItemTop">
            <!-- <img :class="$style.perItemImage" src="../assets/perdks.png" />
            <p :class="$style.perNumber">{{ data.pendingTaskRatio }}%</p> -->
            <div :class="$style.pieCircle">
              <img :class="$style.perItemImage" src="../assets/chart-circle-yellow.svg" />
              <pieCircle :digital="data.pendingTaskRatio" :color="'#cea437'" />
              <p :class="$style.perNumber">{{ data.pendingTaskRatio }}%</p>
            </div>
          </div>
          <p :class="$style.perTips">今日待开始任务</p>
        </div>
        <!-- 今日已逾期任务 -->
        <div :class="$style.percontainItem">
          <div :class="$style.percontainItemTop">
            <!-- <img :class="$style.perItemImage" src="../assets/peryq.png" />
            <p :class="$style.perNumber">{{ data.overdueTaskRatio }}%</p> -->
            <div :class="$style.pieCircle">
              <img :class="$style.perItemImage" src="../assets/chart-circle-red.svg" />
              <pieCircle :digital="data.overdueTaskRatio" :color="'#a83d41'" />
              <p :class="$style.perNumber">{{ data.overdueTaskRatio }}%</p>
            </div>
          </div>
          <p :class="$style.perTips">今日已逾期任务</p>
        </div>
      </div>
    </div>
    <!-- 今日任务各单位执行情况 -->
    <div :class="$style.unitContain">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topCenterImage.png" />
        <span :class="$style.HeaderTitle">今日任务各单位执行情况</span>
      </div>
      <div :class="$style.unitContainTable">
        <div :class="$style.table">
          <!-- 表头 -->
          <div :class="$style.thead">
            <p :class="$style.tr" v-for="header in headers" :key="header">
              {{ header }}
            </p>
          </div>
          <!-- 内容 -->
          <div :class="$style.contain">
            <template v-if="data.list && data.list.length">
              <p v-for="(row, rowIndex) in data.list" :key="row.id" :class="$style.containP">
                <span :class="$style.tr">{{ rowIndex + 1 }}</span>
                <span
                  v-for="(key, keyIndex) in rowKeys"
                  :key="key"
                  :class="{
                    [$style.tr]: true,
                    [$style.progress]: keyIndex == 1,
                    [$style.completed]: keyIndex == 2,
                    [$style.wstart]: keyIndex == 3,
                    [$style.overdue]: keyIndex == 4,
                  }"
                  >{{ row[key] }}</span
                >
              </p>
            </template>
            <Empty v-else />
          </div>
        </div>
      </div>
    </div>
    <!-- 今日上报隐患 -->
    <div :class="$style.slideshow">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topCenterImage.png" />
        <span :class="$style.HeaderTitle">今日上报隐患</span>
      </div>
      <template v-if="sliData && sliData.hazardList && sliData.hazardList.length > 0">
        <!-- 超过三个显示轮播 -->
        <div :class="$style.slideshowContain" v-if="sliData.hazardList.length <= 3">
          <div :class="$style.slideshowItem" v-for="item in sliData.hazardList" :key="item.id">
            <div :class="$style.slideshowItemImg">
              <n-image
                :src="
                  item.hazardRandomCheckEventFiles.length ? imgBase + item.hazardRandomCheckEventFiles[0].fileUrl : ``
                "
                :previewed-img-props="{ style: { border: '8px solid white' } }"
              />
              <p>{{ item.eventTime }}</p>
            </div>
            <p :class="$style.slideshowLocation">{{ item.hazardPosition }}</p>
          </div>
        </div>
        <!-- 轮播图 -->
        <div :class="$style.slideshowContain" v-else>
          <div :class="$style.slideShowInner">
            <n-carousel autoplay slides-per-view="3" :show-dots="false">
              <div :class="$style.slideshowList" v-for="item in sliData.hazardList" :key="item.id">
                <div :class="$style.slideshowItemImg">
                  <n-image
                    :src="
                      item.hazardRandomCheckEventFiles.length
                        ? imgBase + item.hazardRandomCheckEventFiles[0].fileUrl
                        : ``
                    "
                    :previewed-img-props="{ style: { border: '8px solid white' } }"
                  />
                  <p>{{ item.eventTime }}</p>
                </div>
                <p :class="$style.slideshowLocation">{{ item.hazardPosition }}</p>
              </div>
            </n-carousel>
          </div>
        </div>
      </template>
      <div v-else :class="$style.slideshowContain">
        <div style="margin: auto">
          <Empty />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import piePer from './piePer.vue';
import pieCircle from './pieCircle.vue';
import { computed, ref } from 'vue';
import Empty from '@/components/empty/index.vue';
const props = defineProps({
  data: {
    type: <any>Array,
    default: () => {
      return [];
    },
  },
  //智能巡检任务执行情况、智能巡检任务来源、已接入智能设备总数、已完成巡检任务总数数据
  dataObj: {
    type: <any>Object,
    default: () => {
      return {};
    },
  },
  //今日上报隐患 sliData.hazardList, 巡检异常数据
  sliData: {
    type: <any>Object,
    default: () => {
      return {};
    },
  },
});
// console.log('sliData.hazardList = ', props.sliData.hazardList);
const imgBase = window.$SYS_CFG.fileService;
const headers = ref(['序号', '下级单位', '进行中', '已完成', '待开始', '已逾期', '总数']);
const rowKeys = props.data.list && props.data.list.length ? Object.keys(props.data.list[0]) : [];

defineOptions({ name: 'DrawingCenterIndex' });
</script>

<style module lang="scss">
::-webkit-scrollbar {
  width: 0 !important;
  height: 0;
}
.center {
  width: calc(100% - 50px);
}
.centerHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.centerHeaderItem {
  width: 100%;
  height: 9rem;
  line-height: 9rem;
  position: relative;
}
.headerItemImage {
  width: 100%;
  height: 100%;
}
.headerItemCenter {
  position: absolute;
  top: 0;
  left: 1.5rem;
  display: flex;
  align-items: center;
  bottom: 0;
  margin: auto;
}
.headerItemIcon {
  width: 3.4rem;
  height: 3.4rem;
  margin-right: 1.3rem;
}
.headerItemTitle p:nth-child(1) {
  height: 3rem;
  font-family: 'dinPro', serif;
  font-weight: bold;
  font-size: 2.54rem;
  color: #ffffff;
  line-height: 3rem;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
}
.headerItemTitle p:last-child {
  height: 2.08rem;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.48rem;
  color: #ffffff;
  line-height: 2.08rem;
  text-align: left;
  font-style: normal;
}
.centerContain {
  width: 100%;
  margin-bottom: 20px;
}
.topHeader {
  width: 100%;
  height: 4.2rem;
  position: relative;
}
.HeaderImage {
  width: 100%;
  height: 100%;
}
.HeaderTitle {
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 24px;
  color: #ffffff;
  line-height: 3rem;
  text-shadow: 0.1rem 0.3rem 0.2rem rgba(0, 0, 0, 0.4);
  font-style: italic;
  position: absolute;
  top: 0;
  left: 4.9rem;
}
.percontain {
  background: #0e3057;
  border: 1px solid rgba(14, 48, 87, 0.8);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-around;
  background: linear-gradient(to top, #0e3057, transparent);
}
.percontainItemTop {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  // object-fit: fill;
}
.pieCircle {
  position: relative;
  width: 15rem;
  height: 15rem;
}
.perItemImage {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 15rem;
  height: 15rem;
  margin-left: -7.5rem;
  margin-top: -7.5rem;
  z-index: 10;
}
.perNumber {
  position: absolute;
  left: 0;
  top: 0;
  width: 15rem;
  height: 15rem;
  text-align: center;
  line-height: 15rem;
  font-family: DINAlternate, DINAlternate;
  // font-weight: bold;
  font-size: 26px;
  color: #ffffff;
  font-style: normal;
  position: absolute;
}
.perTips {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  // line-height: 2.2rem;
  text-align: center;
  font-style: normal;
  margin: -10px 0 5px 0;
}
.unitContain {
  width: 100%;
  margin-bottom: 1.67rem;
}
.unitContainTable {
  width: 100%;
  height: 20rem;
  background: #0e3057;
  border: 1px solid rgba(14, 48, 87, 0.8);
  overflow-y: auto;
  position: relative;
  background: linear-gradient(to top, #0e3057, transparent);
}
.table {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}
.thead {
  font-family: AlibabaPuHuiTiR;
  font-size: 1.3rem;
  color: #ffffff;
  line-height: 1.6rem;
  font-style: normal;
  height: 3.9rem;
  display: flex;
  align-items: center;
  background: linear-gradient(180deg, #0b63ae 0%, #022e5b 100%);
  // position: fixed;
  position: absolute;
  width: 100%;
}
.tr {
  display: block;
  // width: 16%;
  flex: 1;
  text-align: center;
}
.progress {
  color: #3372ff;
}
.completed {
  color: #00c852;
}
.wstart {
  color: #d5a300;
}
.overdue {
  color: #f42d2d;
}

.contain {
  height: 23rem;
  padding-top: 3.9rem;
  padding-bottom: 2rem;
}
.contain:first-child {
  margin-bottom: 2rem;
}
.containP:nth-child(odd) {
  background-color: #10284b;
}
.containP:nth-child(even) {
  background-color: #091630;
}
.containP {
  height: 4rem;
  font-family: AlibabaPuHuiTiR;
  font-size: 1.2rem;
  color: #ffffff;
  line-height: 1.6rem;
  font-style: normal;
  display: flex;
  align-items: center;
}
.slideshow {
  width: 100%;
  margin-bottom: 1.67rem;
}
.slideShowInner {
  width: 76rem;
}
.slideshowContain {
  display: flex;
  justify-content: center;
  height: 18rem;
  background: #0e3057;
  padding: 2.1rem 2.2rem 0rem 2.2rem;
  background: linear-gradient(to top, #0e3057, transparent);
}
.slideshowItem {
  flex: 1;
  height: 100%;
}
.slideshowList {
  width: 100%;
}
.slideshowItemImg {
  width: 100%;
  height: 12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.slideshowItemImg img {
  width: 22rem;
  height: 11rem;
}
.slideshowItemImg p {
  position: absolute;
  bottom: 0.4rem;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.2rem;
  color: #ffffff;
  text-align: left;
  font-style: normal;
  background: rgba(0, 0, 0, 0.6);
  width: 22rem;
  text-align: center;
  height: 2.9rem;
  line-height: 2.9rem;
}
.slideshowLocation {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.2rem;
  color: #ffffff;
  text-align: left;
  font-style: normal;
  text-align: center;
  margin-top: 1.2rem;
}
// ::v-deep(.n-carousel.n-carousel--bottom .n-carousel__dots) {
//   display: none;
// }
</style>
