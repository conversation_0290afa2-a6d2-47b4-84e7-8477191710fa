import type { IMapItem, IMapMark } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//
export function unitmap(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.unitmap, query);
  return $http.get<IMapItem>(url, { data: { _cfg: { showTip: true } } });
}
export function unitmapPoint(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.unitmapPoint, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}
//获取智能巡检任务完成率排名
export function completionRanking(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.completionRanking, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}
//获取智能巡检任务执行情况、智能巡检任务来源、已接入智能设备总数、已完成巡检任务总数
export function taskAll(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.taskAll, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}
//获取今日任务完成情况、今日任务各单位执行情况数据
export function todayTask(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.todayTask, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}
//获取智能巡检异常数据
export function hazard(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.hazard, query);
  return $http.get<IMapMark[]>(url, { data: { _cfg: { showTip: true } } });
}

//获取天气
export function getWeather(data: any) {
  const param = api.getComParams(api.type.intelligent, api.name.serve.getWeather, data);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}
