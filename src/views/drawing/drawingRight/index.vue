<template>
  <div :class="$style.right">
    <!-- 智能巡检异常上报情况 -->
    <div :class="$style.rightTop">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">智能巡检异常上报情况</span>
      </div>
      <div :class="$style.rightItemCenter">
        <div :class="$style.taskTotalLeft" v-if="sliData.total > 0">
          <div :class="$style.taskTotal">
            <p :class="$style.itemTitle">累计上报异常总数</p>
            <p :class="$style.itemNumber3">{{ sliData.waitDispose }}</p>
          </div>
          <div :class="$style.pieCharts"><pieColumn :data="sliData" :key="index" /></div>
        </div>
        <div style="margin: 0 auto" v-else>
          <Empty />
        </div>
      </div>
    </div>
    <!-- 智能巡检异常类型分布 -->
    <div :class="$style.rightTop">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">智能巡检异常类型分布</span>
      </div>
      <div :class="$style.rightItemCenter">
        <template v-if="sliData.listX && sliData.listX.length > 0">
          <histogramColumn :pdata="sliData" :key="index" />
        </template>
        <div style="margin: 0 auto" v-else>
          <Empty />
        </div>
      </div>
    </div>
    <!-- 异常处置完成率排名 -->
    <div :class="$style.rightTop">
      <div :class="$style.topHeader">
        <img :class="$style.HeaderImage" src="../assets/topImage.png" />
        <span :class="$style.HeaderTitle">异常处置完成率排名</span>
      </div>
      <div :class="$style.rightItemCenter">
        <template v-if="sliData.childDeptList.length > 0">
          <sliderColumn :sliderData="sliData.childDeptList" :key="index" />
        </template>
        <div style="margin: 0 auto" v-else>
          <Empty />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue';
import pieColumn from './pieColumn.vue';
import histogramColumn from './histogramColumn.vue';
import sliderColumn from './sliderColumn.vue';
import { sliderColumns } from '../type';
interface PropsLeft {
  sliData: any;
}
const props = defineProps<PropsLeft>();
const index = ref(0);
watch(
  props,
  (nV) => {
    index.value++;
  },
  { immediate: true }
);
defineOptions({ name: 'DrawingRightIndex' });
</script>

<style module lang="scss">
.right {
  width: 100%;
}
.rightTop {
  margin-bottom: 1.67rem;
}
.topHeader {
  width: 41rem;
  height: 4.2rem;
  position: relative;
}
.HeaderTitle {
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 24px;
  color: #ffffff;
  line-height: 3rem;
  text-shadow: 0.1rem 0.3rem 0.2rem rgba(0, 0, 0, 0.4);
  font-style: italic;
  position: absolute;
  top: 0;
  left: 4.9rem;
}
.taskTotalLeft {
  position: relative;
  margin-left: 1.92rem;
  margin-top: 1.83rem;
}
.pieCharts {
  position: absolute;
  left: 0;
  top: 2rem;
}
.rightItemCenter {
  width: 41rem;
  height: 22rem;
  background: #0e3057;
  border: 1px solid rgba(14, 48, 87, 0.8);
  display: flex;
  flex-wrap: wrap;
  background: linear-gradient(to top, #0e3057, transparent);
}
.itemTitle {
  height: 2.08rem;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 1.5rem;
  color: #ffffff;
  line-height: 2.08rem;
  text-align: left;
  font-style: normal;
}
.itemNumber3 {
  font-family: DINAlternate, DINAlternate;
  font-weight: bold;
  color: #00fdff;
  line-height: 4.5rem;
  text-align: left;
  font-style: normal;
  font-size: 3rem;
}
</style>
