<template>
  <div ref="echart" class="echartDiv"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts';
import { onMounted, toRefs, nextTick, ref, reactive } from 'vue';
export default {
  props: {
    pdata: {
      type: <any>Object,
      default: () => {
        return {};
      },
    },
  },
  setup(props) {
    let state = reactive({
      // xAxisData: ['隐患类型1', '隐患类型2', '隐患类型3', '隐患类型4', '隐患类型5'],
      xAxisData: props.pdata.listX,
      // yAxisData: [13, 10, 8, 15, 4],
      yAxisData: props.pdata.listY,
      yAxisData1: props.pdata.listX.map(() => {
        return 1;
      }),
      echart: ref(),
    });
    const echartInit = () => {
      var myChart = echarts.init(state.echart);
      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (parms: any) {
            var str = parms[0].axisValue + '</br>' + parms[0].value;
            return str;
          },
        },
        textStyle: {
          color: '#ffffff',
        },
        color: ['#7BA9FA', '#4690FA'],
        grid: {
          containLabel: true,
          left: '7%',
          top: '10%',
          bottom: '8%',
          right: '5%',
        },
        xAxis: {
          type: 'category',
          data: state.xAxisData,
          axisLine: {
            lineStyle: {
              color: '#333',
            },
          },
          align: 'center',
          axisLabel: {
            fontSize: 12,
            margin: 20, //刻度标签与轴线之间的距离。
            rotate: 15, // 设置文字倾斜角度，这里是45度
            alignMaxLabel: 'center',
            textStyle: {
              color: '#ffffff',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#B5B5B5',
            },
          },
          splitLine: {
            lineStyle: {
              // 使用深浅的间隔色
              color: 'rgba(217,231,255,0.2)',
              type: 'solid',
              opacity: 0.5,
            },
          },
          axisLabel: {},
        },
        series: [
          {
            data: state.yAxisData,
            stack: 'zs',
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 15,
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  {
                    offset: 0,
                    color: '#46B5E0',
                  },
                  {
                    offset: 1,
                    color: '#3B82E2',
                  },
                ],
              },
            },
          },

          //下面的立体,控制颜色是color第一个
          {
            data: state.yAxisData1,
            type: 'pictorialBar',
            barMaxWidth: '10',
            symbol: 'diamond',
            symbolOffset: [0, '55%'],
            symbolSize: [15, 15],
            zlevel: 2,
          },
          //上面的立体,控制颜色是color第二个
          {
            data: state.yAxisData,
            type: 'pictorialBar',
            barMaxWidth: '10',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [15, 15],
            zlevel: 2,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    };

    //挂载
    onMounted(() => {
      nextTick(() => {
        echartInit();
      });
    });

    return {
      ...toRefs(state),
      echartInit,
    };
  },
};
</script>

<style lang="scss" scoped>
.echartDiv {
  width: 40rem;
  height: 25rem;
}
</style>
