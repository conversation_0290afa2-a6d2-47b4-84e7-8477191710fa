<template>
  <div ref="echart" :class="$style.echartDiv"></div>
</template>

<script lang="ts">
import * as echarts from 'echarts';
import { onMounted, toRefs, nextTick, ref, reactive } from 'vue';
import { sliderColumns } from '../type';
export default {
  props: {
    sliderData: {
      type: Array<sliderColumns>,
      default: () => {
        return {};
      },
    },
  },
  setup(props) {
    let state = reactive({
      xAxisData: ['隐患类型1', '隐患类型2', '隐患类型3', '隐患类型4', '隐患类型5'],
      yAxisData: [4, 22, 1, 11, 23, 11],
      yAxisData1: [1, 1, 1, 1, 1, 1],
      echart: ref(),
    });
    const echartInit = () => {
      let myChart = echarts.init(state.echart);
      // 指定图表的配置项和数据
      // const data = [
      //   {
      //     name: '生产组',
      //     percent: '88%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      //   {
      //     name: '安全组',
      //     percent: '90%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      //   {
      //     name: '6号站',
      //     percent: '92%',
      //     total: '158111',
      //     finished: '82311',
      //   },
      // ]; //自己传过来
      const data = props.sliderData;
      // const yData = data.map((item) => item.name);
      // const zData = data.map((item) => item.percent);
      // const pData = data.map((item) => percentStringToDecimal(item.percent));
      const yData = data.map((item: any) => item.deptName);
      const zData = data.map((item: any) => parseInt(item.ratio) + '%');
      const pData = data.map((item: any) => percentStringToDecimal(item.ratio + '%'));
      // 设置等长的背景柱状图
      const maxData = new Array(yData.length).fill(100);
      const option = {
        grid: {
          left: '10',
          right: '60',
          bottom: '10%',
          top: '10%',
          containLabel: false,
        },
        xAxis: [
          {
            show: true,
          },
          {
            show: false,
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            show: false,
            inverse: true,
            data: yData,
            position: 'top',
            axisLabel: {
              show: true,
              lineHeight: 0,
              verticalAlign: 'bottom',
              fontSize: 20,
              color: '#ffffff',
              formatter: '{value}',
            },
            x: 'center',
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          {
            show: true,
            data: zData,
            offset: 5,
            position: 'right',
            axisLabel: {
              lineHeight: 0,
              verticalAlign: 'bottom',
              fontSize: 14,
              color: '#fff',
              formatter: '{value}',
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '进度',
            show: true,
            type: 'bar',
            barGap: '-120%',
            xAxisIndex: 1,
            barWidth: 10,
            barCategoryGap: '10%',
            itemStyle: {
              borderRadius: 10,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#3F82FF ', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#45F5E1', // 0% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            label: {
              show: true,
              // formatter: "{c}%",
              offset: [5, -20],
              color: '#fff',
              align: 'left',
              position: 'left',
              borderWidth: 4,
              // borderType: [0, 89, 100],
              // padding: [0, 0, 0, 7],
              // borderColor: '#d7b746',
              rich: {
                redLine: {
                  height: 10,
                  backgroundColor: '#d7b746',
                  borderRadius: [5, 5], // 圆角
                  width: 4, // 竖线的宽度
                },
              },
              formatter: '{redLine|}  {b}',
            },
            labelLine: {
              show: false,
            },
            // x: "50%",
            z: 2,
            data: pData.reverse(),
            animationDelay: 1000,
            animationDuration: 1000,
          },
          {
            name: '百分比',
            z: 1,
            show: true,
            type: 'bar',
            xAxisIndex: 1,
            barGap: '-120%',
            barWidth: 10,
            barCategoryGap: '400',
            itemStyle: {
              borderRadius: 4,
              color: '#1F3E61',
            },
            label: {
              show: false,
            },
            data: maxData,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
      // 监听浏览器窗口大小变化
      // window.addEventListener('resize', myChart);
      window.addEventListener('resize', () => {
        myChart.resize();
      });
    };
    function percentStringToDecimal(percentString: any) {
      // 确保百分号在字符串末尾
      if (percentString.endsWith('%')) {
        // 去除百分号并转换为小数
        return parseFloat(percentString.slice(0, -1));
      }
      // 如果不是百分比格式，可以返回NaN或抛出错误
      return NaN; // 或者 throw new Error('Invalid percentage string');
    }
    //挂载
    onMounted(() => {
      nextTick(() => {
        echartInit();
      });
    });

    return {
      ...toRefs(state),
      echartInit,
    };
  },
};
</script>

<style lang="scss" module>
.echartDiv {
  margin-top: 1.5rem;
  width: 40rem;
  height: 20rem;
}
</style>
