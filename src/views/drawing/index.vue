<template>
  <!-- 智能巡检一张图 -->
  <div :class="$style.drawing" ref="dataScreenRef">
    <div :class="$style.drawingHeader">
      <div :class="$style.area_l">
        <weather />
      </div>
      <div :class="$style.area_r">
        <SelectTree :data="treeData" :isShowTreeList="false" @action="treeChange" />
        <!--        <img :class="$style._area" src="./assets/_area.png" @click="jumpMap" />-->
      </div>
      <img :class="$style.HeaderImage" src="./assets/titleImg.png" />
    </div>
    <template v-if="!loading">
      <div :class="$style.drawingItem">
        <!-- 智能巡检一张图左侧 -->
        <div :class="$style.drawingLeft">
          <drawingLeft :data="leftData" :sliData="sliderData"></drawingLeft>
        </div>
        <!-- 智能巡检一张图中间 -->
        <div :class="$style.drawingCenter">
          <drawingCenter :data="unitData" :dataObj="leftData" :sliData="sliderRightData"></drawingCenter>
        </div>
        <!-- 智能巡检一张图右侧 -->
        <div :class="$style.drawingRight" v-if="sliderRightData">
          <drawingRight :data="leftData" :sliData="sliderRightData"></drawingRight>
        </div>
      </div>
    </template>
    <div :class="$style.drawingBottom">
      <img :class="$style.bottomImage" src="./assets/bottom.png" />
    </div>
    <div :class="$style.drawingPrev">
      <img :class="$style.sideImage" src="./assets/bjprev.png" />
    </div>
    <div :class="$style.drawingNext">
      <img :class="$style.sideImage" src="./assets/bjnext.png" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted, onUnmounted, ref } from 'vue';
import drawingLeft from './drawingLeft/index.vue';
import drawingCenter from './drawingCenter/index.vue';
import drawingRight from './drawingRight/index.vue';
import weather from './weather/index.vue';
import type { TreeOption } from 'naive-ui';
import SelectTree from './selectTree/index.vue';
import { useStore } from '@/store';
import { postTreeList } from '@/views/common/fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { completionRanking, taskAll, todayTask, hazard } from './fetchData';
import { sliderColumns } from './type';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis } from '@/views/common/utils';
const [loading, run] = useAutoLoading(true);
const store = useStore();
const treeParam = ref<any>({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  orgCode: store.userInfo.orgCode,
});
const actTree = ref<any>({
  deptIds: store.userInfo.deptId,
  id: store.userInfo.orgCode || store.userInfo.deptId,
  text: store.userInfo.deptName,
  treeName: store.userInfo.deptName,
  unitId: store.userInfo.unitId,
  unitIds: store.userInfo.unitId,
});
const dataScreenRef = ref<HTMLElement | null>(null);
const treeData = ref<any>([]);
const unitData = ref<any>([]);
const leftData = ref<any>();
const sliderData = ref<sliderColumns[]>([]);
const sliderRightData = ref<any>(null);
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  treeParam.value.orgCode = v.id;
  actTree.value = v;
  sendGis(BRI_EVENT_TYPE.DRAWING_TREE_CHANGE, {
    isMap: true,
    drawingTree: {
      deptIds: v.deptIds,
      id: v.id,
      text: v.text,
      treeName: v.treeName,
      unitId: v.unitId,
      unitIds: v.unitIds,
    },
  });
  getMapData();
}
function treeList() {
  run(postTreeList(treeParam.value)).then((res: any) => {
    treeData.value = res.data || [];
    loading.value = false;
  });
}

function getMapData() {
  completionRanking(treeParam.value)
    .then((res: any) => {
      console.log('获取智能巡检任务完成率排名 = ', res);
      sliderData.value = res.data;
    })
    .catch(() => {});
  taskAll(treeParam.value)
    .then((res: any) => {
      leftData.value = res.data;
      console.log('获取智能巡检任务执行情况、智能巡检任务来源、已接入智能设备总数、已完成巡检任务总数数据 = ', res);
    })
    .catch(() => {});
  todayTask(treeParam.value)
    .then((res: any) => {
      unitData.value = res.data;
      console.log('获取今日任务完成情况、今日任务各单位执行情况数据 = ', res);
    })
    .catch(() => {});
  hazard(treeParam.value)
    .then((res: any) => {
      sliderRightData.value = res.data;
      console.log('获取智能巡检异常数据 = ', res);
    })
    .catch(() => {});
}
getMapData();
// 设置 rem 函数
function setRem() {
  // 基准大小
  const baseSize = 10;
  // 当前页面宽度相对于 1920 宽的缩放比例，可根据自己需要修改。
  const scale = document.documentElement.clientWidth / 1920;
  // 设置页面根节点字体大小
  document.documentElement.style.fontSize = baseSize * Math.min(scale, 2) + 'px';
}
onBeforeMount(() => {
  //打开GIS
  //window.open(window.$SYS_CFG.gisLink, 'ehsInspect-gisWindow');
  // jumpMap();
});
function jumpMap() {
  //window.open(import.meta.env.VITE_APP_GISMAP);
  window.open(window.$SYS_CFG.gisLink, 'ehsInspect-gisWindow');
  sendGis(BRI_EVENT_TYPE.DRAWING, {
    isMap: true,
    drawingTree: {
      deptIds: actTree.value.deptIds,
      id: actTree.value.id,
      text: actTree.value.text,
      treeName: actTree.value.treeName,
      unitId: actTree.value.unitId,
      unitIds: actTree.value.unitIds,
    },
  });
}
onMounted(() => {
  // 初始化
  setRem();
  // 改变窗口大小时重新设置 rem
  window.onresize = function () {
    setRem();
  };
  setTimeout(() => {
    treeList();
  }, 500);
});
onUnmounted(() => {});

// 初始化
function initVisualChart() {}
defineOptions({ name: 'DrawingIndex' });
</script>

<style module lang="scss">
.drawing {
  position: relative;
  width: 100%;
  //height: 91rem;
  height: 100%;
  background: #040d1d;
  display: flex;
  flex-direction: column;
  padding: 0px !important;
}
.drawingItem {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex: 1;
  // margin-top: 8.75rem;
  // position: absolute;
}
.drawingHeader {
  // position: absolute;
  // z-index: 999;
  width: 100%;
  height: 8.75rem;
  background-size: 100% 100%;
  background-image: url('./assets/topHeader.png');
  flex-shrink: 0;
}
.area_l {
  padding-left: 18px;
  position: absolute;
  width: 584px;
  height: 52px;
  background: url('./weather/assets/area-l.png') center center no-repeat;
  background-size: cover;
  left: 9px;
  top: 19px;
}
.HeaderImage {
  position: absolute;
  width: 29.33rem;
  height: 3.75rem;
  top: 1.42rem;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.drawingBottom {
  // position: absolute;
  // z-index: 999;
  // bottom: 0;
  height: 3.75rem;
}
.bottomImage {
  width: 100%;
  height: 3.75rem;
}
.drawingPrev {
  position: absolute;
  left: 0;
  top: 5rem;
  z-index: 999;
}
.drawingNext {
  position: absolute;
  right: 0;
  top: 5rem;
  z-index: 999;
}
.sideImage {
  width: 3.17rem;
  // height: 80.25rem;
}
.drawingLeft {
  margin-left: 2.2rem;
  width: 41rem;
}
.drawingRight {
  width: 41rem;
  margin-right: 2.2rem;
}
.drawingCenter {
  display: flex;
  justify-content: center;
  flex: 1;
}
// .drawingLeft,
// .drawingCenter,
// .drawingRight {
//   // margin-right: 50px;
//   // width: 100%;
// }
.area_r {
  position: absolute;
  right: 0.8rem;
  top: 1rem;
  display: flex;
  align-items: center;
}
._area {
  width: 4rem;
  height: 4rem;
  cursor: pointer;
}
</style>
