<template>
  <div :class="$style.treeWrap">
    <div :class="[$style.treeInput]">
      <n-tree-select
        placeholder="请输入组织名称"
        :key-field="props.keyField"
        :label-field="props.labelField"
        v-model:value="pattern"
        :options="treeSelectSearchOpt"
        @update:value="handleUpdateValue"
        :render-switcher-icon="renderLabelIcon"
        filterable
        :theme-overrides="selectThemeOverrides"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption, TreeSelectOption } from 'naive-ui';
import { GlobalThemeOverrides } from 'naive-ui';
import { computed, h, ref, watch } from 'vue';
import { AnFilledCaretRight } from '@kalimahapps/vue-icons';
import { useStore } from '@/store';
import { BridgeService } from '@/service/bridge/BridgeService.ts';
import { TreeSelectProps } from 'naive-ui';
type SelectThemeOverrides = NonNullable<TreeSelectProps['themeOverrides']>;

const selectThemeOverrides: SelectThemeOverrides = {
  menuColor: 'rgba(13, 39, 66, 0.96)',
  peers: {
    Tree: {
      nodeTextColor: '#fff',
      nodeColorHover: '#0e3057',
    },
  },
};
const store = useStore();
const emits = defineEmits(['action']);
const ns = ref('znxj-tree');
type listT = {
  data: TreeOption[];
  selKey?: string[]; //默认选中项
  openKey?: string[]; //默认打开项
  labelField?: string;
  keyField?: string;
};

const props = withDefaults(defineProps<listT>(), {
  labelField: 'text',
  keyField: 'id',
});
const pattern = ref<any>(store.userInfo.orgCode);

const selectedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

watch(
  () => props.data,
  (v) => {
    expandedKeys.value = [];
    selectedKeys.value = [];
    if (v.length) {
      // pattern.value = v[0].id as string;
      let curKey = v[0].id as string;
      expandedKeys.value = [curKey];
      setTimeout(() => {
        selectedKeys.value = [curKey];
      });
      return;
    }
  },
  {
    immediate: true,
  }
);
const treeSelectSearchOpt = computed(() => {
  if (props.data.length) {
    return [...props.data];
  }
  return [];
});
function renderLabelIcon(info: { option: TreeOption; checked: boolean; selected: boolean }) {
  if (info.option.hasChildren) {
    return h(AnFilledCaretRight);
  }
  return h('span');
}

function handleUpdateValue(value: string, option: TreeOption) {
  console.log('树结构搜索----', option);
  updateTree(option);
  const bridgeToGis = new BridgeService('inspectChange', true);
  bridgeToGis.sendMessage('inspectTreeUpdate', { params: option.id });
}
function updateTree(option: TreeOption) {
  const curKey = option.deptIds as string;
  const keys = curKey.split(',');
  const keyId = option.id as string;
  selectedKeys.value = [keyId];
  expandedKeys.value = keys;
  console.log('树结构改变----', option, selectedKeys.value, expandedKeys.value);
  emits('action', option);
}

const bridge = new BridgeService('gisChange', true);
bridge.onMessage('gisTreeUpdate', (data) => {
  console.log('gis发送过来的数据', JSON.stringify(data));
  pattern.value = data.params;
  emits('action', { id: data.params });
});

defineOptions({ name: 'SelectTree' });
</script>

<style module lang="scss">
.treeWrap {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.treeInput {
  margin: 16px 12px 12px;
  .icon {
    color: #c0c4cc;
  }
}
.comTree {
  flex: 1;
}
</style>
<style scoped>
:deep(.n-base-selection-label) {
  background-color: #0e3057 !important;
}
:deep(.n-base-selection .n-base-selection__border, .n-base-selection .n-base-selection__state-border) {
  border: 1px solid #066abf !important;
}
:deep(.n-base-selection .n-base-selection-label .n-base-selection-input) {
  color: #fff !important;
}
:deep(.n-base-selection .n-base-selection-label .n-base-selection-label__render-label) {
  color: #fff !important;
}
:deep(.n-tree-select-menu) {
  color: #fff !important;
  background-color: #0e3057 !important;
}
</style>
