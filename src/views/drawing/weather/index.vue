<template>
  <div class="flex">
    <div class="w-[155px]">
      <div :class="$style['sfm']">{{ currentTime }}</div>
      <div :class="$style['day']">
        {{ currentDay }}<span class="ml-[12px]">{{ currentWeek }}</span>
      </div>
    </div>
    <div class="ml-[52px] flex-1">
      <div class="w-[26px]">
        <img :class="$style['icon-weather']" :src="getWeatherIcon('1')" alt="weather" />
      </div>
      <div class="ml-[28px]">
        <div class="flex mt-[3px]" style="color: #fff">
          <div :class="$style['temperature']" class="w-[77px]">{{ weatherData.weatherTemperature }}℃</div>
          <div>|</div>
          <div class="w-[115px] text-center">{{ weatherData.windDirect }}{{ weatherData.windSpeed }}级</div>
          <!-- <div>|</div>
          <div class="w-[96px] text-center">空气质量 优</div> -->
        </div>
        <div :class="$style['weather-bottom']" class="flex">
          <div class="w-[77px] text-center">{{ weatherData.weatherInfo }}</div>
          <div>|</div>
          <div class="w-[115px] text-center">气压{{ weatherData.weatherAirpressure }}hpa</div>
          <!-- <div>|</div>
          <div class="w-[96px] text-center">紫外线 较弱</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, ref } from 'vue';
import dayjs from 'dayjs';
import icon from '@/views/drawing/weather/icon.ts';
import { getWeather } from '../fetchData';

const currentTime = ref();
const currentDay = ref();
const currentWeek = ref();
const interval = ref();

function updateTime() {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString();
  currentDay.value = dayjs(now).format('YYYY-MM-DD');
  currentWeek.value = toFormat(dayjs(now).day());
}

function toFormat(str: number) {
  switch (str) {
    case 1:
      return '星期一';
    case 2:
      return '星期二';
    case 3:
      return '星期三';
    case 4:
      return '星期四';
    case 5:
      return '星期五';
    case 6:
      return '星期六';
    default:
      return '星期日';
  }
}

function init() {
  interval.value = setInterval(updateTime, 1000);
}
interface WeatherType {
  weatherImg: string; //天气类型
  weatherInfo: string; //天气名称
  windDirect: string; //风力名称
  windSpeed: string; //几级风力
  weatherAirpressure: string; //气压
}
const weatherData = ref<WeatherType>({
  weatherImg: '',
  weatherInfo: '',
  windDirect: '',
  windSpeed: '',
  weatherAirpressure: '',
});
function getWeathers(data: any) {
  let p = { distinctCode: '610600' };
  getWeather(p).then((res) => {
    console.log('获取天气 res = ', res);
    weatherData.value = res.data;
  });
}
getWeathers();

// 天气图标
function getWeatherIcon(val: string) {
  if (val) {
    return icon[`weather_${val}` as keyof typeof icon];
  }
}

init();

onBeforeUnmount(() => clearInterval(interval.value));

defineOptions({ name: 'WeatherComp' }); // 天气组件
</script>

<style module>
.sfm {
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
}

.day {
  margin-top: -6px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
}

.icon-weather {
  width: 25px;
  height: 26px;
  position: absolute;
  top: 40%;
  transform: translateY(-50%); /* 向左向上移动自身宽高的一半 */
}

.weather-bottom {
  margin-top: 1px;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
}

.temperature {
  text-align: center;
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
}
</style>
