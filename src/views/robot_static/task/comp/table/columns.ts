import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'left',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '任务名称',
    key: 'patrolTaskName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务类型',
    key: 'taskTypeLabel',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '执行设备',
    key: 'deviceInfoIdName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '执行批次',
    key: 'appno',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '地图名称',
    key: 'mapName',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '巡检点数',
    key: 'patrolPointTotal',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '开始时间',
    key: 'startTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '结束时间',
    key: 'endTime',
    align: 'left',
    ellipsis: {
      tooltip: true,
    },
  },
  // {
  //   title: '执行时间',
  //   key: 'excuteTime',
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
  // {
  //   title: '发现异常数量',
  //   key: 'recordPoint',
  //   align: 'left',
  //   ellipsis: {
  //     tooltip: true,
  //   },
  // },
];
