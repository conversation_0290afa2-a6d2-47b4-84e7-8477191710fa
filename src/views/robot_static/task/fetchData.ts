import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import type { IPageData } from '../type';
import type { TaskListType, jumpToqtType } from './type';

//获取巡检任务
export function postTaskListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.task, query);
  return $http.get<IPageData[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//跳转
export function jumpToqtApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.jumptoqt, query);
  return $http.get<jumpToqtType[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
export function taskRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.robot.detailTask, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
