<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" style="width: 327px">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri">
      <!-- <com-filter class="com-table-filter" @action="actionFn" /> -->
      <div :class="$style['layer-cont']">
        <!-- <equipment-list @gotoDetail="gotoDetail" :deptId="deptId" :treeName="treeName"></equipment-list> -->
        <com-card-b
          style="padding-left: 0"
          @gotoDetail="gotoDetail"
          :treeName="treeName"
          :equipmentList="equipmentList"
        ></com-card-b>
      </div>
      <!-- <equipment-detail ref="equipmentDetail" :deviceId="deviceId" /> -->
      <ComCardC ref="ComCardCRef" :deviceDetailsList="deviceDetail" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { computed, provide, Ref, ref, watch, onMounted } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import ComCardB from '@/components/card/ComCardBStatic.vue';
import ComCardC from '@/components/card/ComCardCStatic.vue';
// import equipmentDetail from './comp/equipmentDetail.vue';
import { postDeviceListApi, postDeviceDetailApi, postDevicePowerlApi } from './fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from './constant';
import type { IActionData } from './type';
import type { DeviceListType, DeviceDetailType, DevicePowerType } from './type';
import { useStore } from '@/store';
const store = useStore();

// const equipmentDetailRef = ref<InstanceType<typeof equipmentDetail>>();
const [loading, run] = useAutoLoading(false);
let sendDevicelist = ref<DeviceListType[]>([]);
let deptId = ref<string>('');
let treeName = ref<string>('');
let deviceId = ref<string>('');
// const DevicePowerlPrams = ref({
//   //设备ID必填
//   deviceId: '110728970',
// });

const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
  treeLoading: {
    type: Boolean,
    default: true,
  },
});
const treeAct = ref();
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    let list = v as TreeOption[];
    treeAct.value = list[0];
    treeData.value = list;
  },
  {
    immediate: true,
  }
);
function treeChange(v: TreeOption) {
  console.log('接收到tree v', v);
  // deptId.value = v.id;
  treeName.value = v.treeName as string;
  treeAct.value = v;
  // postDeviceList(v.id);
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id } });
}
// let equipmentList = ref<DeviceListType[]>([]);

let equipmentList = ref<any>([
  {
    id: '2',
    name: 'SH01',
    deviceName: 'SH01',
    eqType: '七腾防爆充电房',
    deviceId: '328490324…',
    eqName2: '6号集气站巡检...',
    category: 'SREX-SGLS-02',
  },
]);
// let deviceDetail = ref<DeviceDetailType[]>([]);
// const equipmentList = ref<any>();
const deviceDetail = ref<any>();
let powerDetail = ref<DevicePowerType[]>([]);

function postDeviceList(id: any) {
  // let prams = {
  //   deviceId: id,
  //   pageNo: 1,
  //   pageSize: 10,
  // };
  // let prams = {
  //   deptId: id,
  //   deviceName:''
  // }
  console.log('设备列表查询参数', JSON.stringify(id));
  // run(postDeviceListApi(id)).then((res) => {
  //   // deviceDetail.value = res.data;
  //   console.log('设备列表查询', JSON.stringify(res.data));
  //   equipmentList.value = res.data;
  //   // sendDevicelist.value = res.data;
  // });
}
// postDeviceList(store.userInfo.deptId)
// function deviceDetail() {
//   run(postDeviceDetail(deviceID.value)).then((res) => {
//     console.log(res);
//   });
// }
// deviceDetail();
// function DevicePowerl() {
//   run(postDevicePowerl(DevicePowerlPrams.value)).then((res) => {
//     console.log(res);
//   });
// }
// DevicePowerl();
function gotoDetail(value: string) {
  console.log('list 传过来的 value 是', value);
  ComCardCRef.value?.init(value);
  // run(postDeviceDetailApi(value)).then((res) => {
  //   console.log(res);
  //   ComCardCRef.value?.init();
  //   deviceDetail.value;
  // });
  // let prams = {
  //   deviceComposeValueId: '',
  //   deviceId: value,
  // };
  // run(postDevicePowerlApi(prams)).then((res) => {
  //   console.log(res);
  //   powerDetail.value;
  // });
}

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const actionLabel = computed(() => ACTION_LABEL[currentAction.value.action]);

const isShowAside = ref(false);
const tableCompRef = ref();
const ComCardCRef = ref();
// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索', JSON.stringify(currentAction.value.data));
      handleSearch(currentAction.value.data);
      postDeviceList(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择', JSON.stringify(currentAction.value.data));
      handleSearch(currentAction.value.data);
      postDeviceList(currentAction.value.data);
      break;
    case ACTION.EDIT:
      console.log('详情');
      isShowAside.value = true;
      break;
    case ACTION.VIDEO:
      console.log('查看视频');
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

onMounted(() => {
  treeName.value = store.userInfo.orgName;
});

defineOptions({ name: 'VideoEquiIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
.layer-cont {
  padding: 16px 24px 0 24px;
}
</style>
