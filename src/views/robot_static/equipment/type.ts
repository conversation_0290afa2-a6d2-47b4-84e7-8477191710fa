import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IPageData {
  areaCode: string;
}
export type IPageDataRes = IPageRes<IPageData>;

export interface IDetail {
  areaCode: string;
}

export interface DeviceListType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}

export interface DeviceDetailType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}

export interface DevicePowerType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}
