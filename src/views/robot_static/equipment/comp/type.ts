export interface DeviceType {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}

export interface deviceDetail {
  areaParentIds: string;
  areaParentId: string;
  deviceId: string; // 设备编号
  deviceName: string; // 设备名称
  areaId: string;
  province: string;
  areaName: string;
  topparent: string;
  robotStatus: string; // 'robotOffLine' 表示关闭
  category: string; // 设备型号
  remark1: string;
  projectName: string;
  projectId: string;
  id: string;
  name: string;
  condition: string;
}
