<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import type { IPageData, IProgressStatus, IButType, IActionData } from '../../type';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, toRaw, VNode, inject, Ref } from 'vue';
import { taskListApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
// import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageData[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
// const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    // createdBy: '', //创建人
    // createTime: '', //创建时间
    // delFlag: 1, //逻辑删除标志1-是，0-否
    // deptId: '', //所属单位
    // deptName: '', //所属单位名称
    // id: '', //主键
    // planId: '', //计划ID
    // planName: '', //计划名称
    // recordPoint: '', //巡检点总数量/已巡数量/未巡检点
    // taskEndTime: '', //任务实际结束时间
    // askPlanEndTime: '', //任务计划结束时间
    // taskPlanStartTime: '', //任务计划开始时间
    // taskStartTime: '', //任务实际开始时间
    // taskStatus: '', //任务状态 0：待开始，1：未执行，2：进行中，3，已完成
    // updatedBy: '', //修改人
    // updateTime: '', //修改时间
    // zhId: '', //租户id
    ...filterData,
  };

  // search(taskListApi(params)).then((res: any) => {
  //   tableData.value = res.data.rows || [];
  //   updateTotal(res.data.total || 0);
  // });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  //const tagType: ITgeType[] = ['default', 'primary', 'info', 'success', 'warning', 'error'];
  //任务状态 0：待开始，1：未执行，2：进行中，3，已完成
  const stateT = ['待开始', '未执行', '进行中', '已完成'];
  const butTColor = ['#F39600', '#FA5151', '#527CFF', '#00B578'];
  const proStatus: IProgressStatus[] = ['default', 'success', 'error', 'warning', 'info'];
  //巡检进度
  columns.value.splice(columns.value.length - 1, 0, {
    title: '巡检进度',
    key: 'state',
    align: 'left',
    width: 150,
    render(row) {
      const curPro = row.pro as number;
      return h(NProgress, {
        //indicatorPlacement: 'inside',
        color: curPro < 100 ? '#527CFF' : '#00B578',
        status: curPro < 100 ? 'default' : 'success',
        size: 'small',
        type: 'line',
        percentage: row.pro as number,
        class: 'com-prog',
      });
    },
  });
  //状态栏
  columns.value.push({
    title: '任务状态',
    key: 'taskStatus',
    align: 'left',
    width: 150,
    render(row) {
      const index = row.taskStatus as number;
      return h(
        NButton,
        {
          size: 'small',
          color: butTColor[index],
        },
        stateT[index]
      );
    },
  });

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
  //加线
  //return useActionDivider(acList);
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
