import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface PlanListType {
  appno: string;
  endTime: string;
  id: string;
  mapName: string;
  patrolPointTotal: string;
  patrolTaskId: string;
  patrolTaskName: string;
  resultStates: string;
  robot: string;
  startTime: string;
  status: string;
  sysProjectName: string;
  taskStartTime: string;
  updateDate: string;
}

export interface PostPlanListType {
  deviceInfoId: string;
  pageNo: number;
  pageSize: number;
  status: string;
  sysProjectId: string;
  taskName: string;
  taskStatus: string;
  taskType: string;
  userId: string;
  deptId: string;
}

export interface jumpToqtType {
  code: string;
  data: string;
  dataType: string;
  message: any;
  status: string;
  token: string;
}
export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

export interface IPageData {
  deviceInfoIdName: string;
  id: string;
  intervalTime: string;
  intervalUnit: string;
  pointName: string;
  pointStatus: string;
  pointTotal: string;
  startTiming: string;
  status: string;
  taskName: string;
  taskStatus: string;
  taskType: string;
}

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';
