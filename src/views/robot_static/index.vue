<template>
  <div :class="{ [$style.videoWrap]: true }">
    <com-bread :data="breadData"></com-bread>
    <com-tab @tab-action="tabChange" :tab="tabAct" :tab-list="tabData"></com-tab>
    <div :class="{ [$style.container]: true }">
      <equipment v-if="tabAct === 'equipment'" :tree-list="treeData" :tree-loading="loading"></equipment>
      <plan v-if="tabAct === 'plan'" :tree-list="treeData" :tree-loading="loading"></plan>
      <task v-if="tabAct === 'task'" :tree-list="treeData" :tree-loading="loading"></task>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, onMounted } from 'vue';
import ComTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import equipment from './equipment/index.vue';
import plan from './plan/index.vue';
import task from './task/index.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import type { TreeOption } from 'naive-ui';
import { tabData } from './setData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { postTreeList } from './fetchData';
import { useRoute, useRouter } from 'vue-router';
import type { jumpToqtType } from './type';
const [loading, run] = useAutoLoading(false);
const router = useRouter();
const route = useRoute();
import { useStore } from '@/store';
const store = useStore();
const breadData: IBreadData[] = [{ name: '智能巡检' }, { name: '机器人演示' }];

const tabAct = ref('');
tabAct.value = route.query.tab ? (route.query.tab as string) : 'task';
function tabChange(v: string) {
  tabAct.value = v;
  router.replace(`robot-static?tab=${v}`);
}
const treeData = ref<TreeOption[]>([]);
const treeParam = ref({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  // orgCode: '10000',
  orgCode: store.userInfo.orgCode, //机构id=10000,顶级是-1
  // //机构来源，1，内部 2 外部
  // orgRes: 1,
  // //去除的机构部门id,(下级,本身)
  // removeOrgCode: '',
});
function treeList() {
  run(postTreeList(treeParam.value))
    .then((res) => {
      treeData.value = res.data || [];
    })
    .catch(() => {});
}
treeList();

const AbnormalParam = ref({
  abilityType: '',
  createEndDate: '',
  createStartDate: '',
  dealEndDate: '',
  dealStartDate: '',
  deviceId: '110728970',
  pageNo: 1,
  pageSize: 10,
  projectId: '',
  status: '',
  type: '',
  userId: '',
  deptId: '0dedd98538d448df826e5066830b276a',
});
// function abnormalList() {
//   run(postAbnormalList(AbnormalParam.value)).then((res) => {
//     treeData.value = res.data || [];
//     treeData.value[0].children[0].isLeaf = false;
//   });
// }
// abnormalList();

onMounted(() => {
  console.log(route.query, 'query');
});
defineOptions({ name: 'VideoIndex' });
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;
  .container {
    flex: 1;
    background: #eef7ff;
  }
}
</style>
