<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="props.treeList" ref="treeRef"></com-tree>
    </div>
    <div class="layer-ri" v-if="treeAct">
      <div class="layer-strenth" v-if="isShowIcon" @click="layerShow"><img :src="strengthImage" /></div>
      <com-card :list="cardList" :act="equiStatus" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont qeui-cont-flex">
          <equi-list ref="tableCompRef" @action="actionFn" :cur-tree="treeAct" />
        </div>
      </div>
    </div>
    <detail @action="actionFn" ref="detailRef" />
    <com-video ref="videoRef" @close="closeIcon"></com-video>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import EquiList from './comp/list/EquiList.vue';
import Detail from './comp/detail/Detail.vue';
import { ACTION, CARDLIST } from './constant';
import type { IActionData } from './type';
import type { IDeviceDetail } from '../type';
import ComCard from '@/components/card/ComCardA.vue';
import type { ICardAItem } from '@/components/card/type';
import { equiListRecordApi } from './fetchData';
import { equiVideoApi } from '../fetchData';
import ComVideo from './comp/video/Video.vue';
import strengthImage from '@/assets/strenth.png';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis, receiveGis } from '@/views/common/utils';
import { storeToRefs } from 'pinia';
import { useStore } from '@/store';
import { useRouter } from 'vue-router';
const router = useRouter();

const props = withDefaults(defineProps<{ treeList: TreeOption[] }>(), {
  treeList: () => [],
});
const layerVisible = ref(false);
const isShowIcon = ref(true);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
function closeIcon() {
  isShowIcon.value = true;
}
const detailRef = ref();
const equiStatus = ref(-1);
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const tableCompRef = ref();
const cardList = ref<ICardAItem[]>(CARDLIST);

const store = useStore();
const { treeAct } = storeToRefs(store);
watch(
  () => treeAct.value,
  (vN, vO) => {
    console.log('有树值了', vN, vO);
    if (vN) {
      sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT);
      actionFn({ action: ACTION.TREECHANGE, data: {} });
    }
  },
  {
    immediate: true,
  }
);
const treeRef = ref();
receiveGis((res) => {
  console.warn('Received app_onEhsInsMgr GISmsg 设备:', res);
  //组织树更改
  if (res.type === BRI_EVENT_TYPE.TREE_CHANGE) {
    if (res.treeActData.id === treeAct.value?.id) {
      //同一个树组织
      return;
    }
    //更新对应的点 todo
    treeRef.value.updateTree(res.treeActData);
  }
  // 去一张图
  if (res.type === BRI_EVENT_TYPE.DRAWING && res.data.isMap) {
    router.push('/drawing');
  }
});
sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT_ITEMPLAYER);
//统计
function equiListRecord(id?: string) {
  const params = {
    //ownerIds: id || '',
    deptId: id || '',
  };
  equiListRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.allNum;
    cardList.value[1].value = res.data.useNum;
    cardList.value[2].value = res.data.stopNum;
  });
}

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    status: equiStatus.value,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    levelCode: treeAct.value?.levelCode || '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      equiListRecord(treeAct.value.id); //treeAct.value.unitIds
      sendGis(BRI_EVENT_TYPE.TREE_CHANGE);
      break;
    case ACTION.EDIT:
      console.log('详情');
      const deviceId = currentAction.value.data.detail.deviceId;
      detailRef.value.open(deviceId);
      sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT_DETAIL, { deviceId: deviceId });
      break;
    case ACTION.VIDEO:
      console.log('查看视频');
      isShowIcon.value = false;
      getEquiVideo(currentAction.value.data.detail);
      sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT_ITEMPLAYER, {
        deviceId: currentAction.value.data.detail.deviceId,
      });
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片筛选');
      equiStatus.value = currentAction.value.data.cardId;
      currentAction.value.data.status = currentAction.value.data.cardId;
      handleSearch(currentAction.value.data);
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
const playerUrl = ref('');
const videoRef = ref();
//查看视频详情
function getEquiVideo(data: IDeviceDetail) {
  equiVideoApi({
    deviceId: data.deviceId,
    manufacturerCode: data.manufacturerCode,
    deviceNum: data.deviceNum,
    accesstype: 1,
  }).then((res) => {
    playerUrl.value = res.data as string;
    videoRef.value.open({ url: playerUrl.value, name: `${data.deviceName}${data.deviceAddress}` });
  });
}

defineOptions({ name: 'VideoEquiIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.video-equi {
  width: 800px !important;
  height: 590px;
  padding: 0px;
  .n-dialog__content {
    margin: 0 !important;
    height: 100%;
  }
}
.qeui-cont-flex {
  display: flex;
  flex-direction: column;
}
</style>
