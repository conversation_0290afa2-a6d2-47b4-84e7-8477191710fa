import type { IPageDataRes, IEquiNum } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//设备列表
export function equiListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}
//设备统计
export function equiListRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiListRecord, query);
  return $http.get<IEquiNum>(url, { data: { _cfg: { showTip: true } } });
}

//设备详情中的天泽智能盒
export function findIbmInfoByVideoDeviceNumApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.findIbmInfoByVideoDeviceNum, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

//查看视频
// export function equiVideoApi(data: {
//   deviceId: string | number;
//   manufacturerCode: string;
//   deviceNum: string;
//   accesstype?: number | string; //接入网络类型(@:内网;1:公网;2:DCN网)
// }) {
//   const url = api.getUrl(api.type.intelligent, api.video.equiListVideourl);
//   return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...data } });
// }
export function equiVideoApi(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiListVideourl, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
