<template>
  <div :class="$style.equiList">
    <com-loading v-if="loading"></com-loading>
    <n-grid :x-gap="0" :y-gap="0" cols="4" v-if="listData.length">
      <n-grid-item v-for="list in listData" :key="list.deviceId">
        <div :class="$style.equiListItem">
          <div :class="$style.equiItemName" @click="toAction(list)">
            <p>{{ list.deviceName }}{{ list.deviceAddress }}</p>
            <p>{{ list.unitName }}</p>
          </div>
          <div :class="{ [$style.equiItemSta]: true, [$style.leve]: list.onlineState !== '0' }">
            {{ list.onlineState !== '0' ? '离线' : '在线' }}
          </div>
          <div :class="$style.equiVideo" @click="toAction(list, 1)">
            <img src="../../../assets/video-icon.png" class="img" />
          </div>
          <div :class="$style.equiVideoImg" v-if="list.floorAreaImg">
            <img src="../../../assets/equi.jpg" />
          </div>
        </div>
      </n-grid-item>
    </n-grid>
    <Empty v-else />
  </div>
  <com-pag @change="getTableData" :item-count="total" />
</template>

<script lang="ts" setup>
import type { IDeviceDetail } from '../../../type';
import { ref } from 'vue';
import { equiListApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { IObj } from '@/types';
import { ACTION } from '../../constant';
import ComPag from '@/components/pag/ComPag.vue';
import ComLoading from '@/components/loading/comLoading.vue';
import { paramsFn } from '@/views/common/utils.ts';
import Empty from '@/components/empty/index.vue';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const listData = ref<IDeviceDetail[]>([]);
const props = defineProps({
  curTree: {
    type: Object,
    default: () => {},
  },
});

interface PagT {
  page: number;
  pageSize: number;
}
const total = ref(0);
const pagination = ref({
  page: 1,
  pageSize: 10,
});
let filterData: IObj<any> = {}; // 搜索条件

function getTableData(v?: PagT) {
  loading.value = true;
  if (v) {
    pagination.value.page = v.page;
    pagination.value.pageSize = v.pageSize;
  }
  const params = {
    pageNo: pagination.value.page,
    pageSize: pagination.value.pageSize,
    ...filterData,
  };
  search(equiListApi(params))
    .then((res) => {
      listData.value = res.data.rows || [];
      total.value = res.data.total;
    })
    .catch(() => {});
}

function getTableDataWrap(data: IObj<any>) {
  let curData = { ...data };
  //curData.ownerIds = '540102DZDA202206010001';
  curData.ownerIds = props.curTree.unitIds;
  // curData.unitName = encodeURIComponent(curData.unitName);
  // curData.deviceAddress = encodeURIComponent(curData.deviceAddress);
  filterData =
    Object.assign(
      {},
      paramsFn(curData, [
        'pageNo',
        'pageSize',
        'status',
        'deptId',
        'ownerIds',
        'deviceAddress',
        'unitName',
        'levelCode',
      ])
    ) || {};
  pagination.value.page = 1;
  getTableData();
}

function toAction(data: IDeviceDetail, type?: number) {
  emits('action', {
    action: type ? ACTION.VIDEO : ACTION.EDIT,
    data: { detail: data },
  });
}

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiList' });
</script>

<style module lang="scss">
.equiList {
  // display: flex;
  // flex-wrap: wrap;
  padding: 16px 24px 0px 8px;
  flex: 1px;
  overflow-y: auto;
  .equiListItem {
    // width: 302px;
    height: 226px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    margin-left: 16px;
    margin-bottom: 16px;
    position: relative;
  }
  .equiItemName {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    padding: 5px 16px;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
  }
  .equiItemSta {
    height: 20px;
    background: #00b578;
    border-radius: 10px;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    position: absolute;
    right: 0;
    top: 0;
    min-width: 34px;
    text-align: center;
    &.leve {
      background: #adadad;
    }
  }
  .equiVideo {
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -20px 0 0 -16px;
    width: 42px;
    height: 42px;
    cursor: pointer;
    .img {
      width: 100%;
      height: 100%;
    }
  }
  .equiVideoImg {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
      border-radius: 10px;
    }
  }
}
</style>
