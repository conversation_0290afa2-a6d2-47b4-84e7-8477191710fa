<template>
  <!-- 状态结果详情 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <com-loading v-if="loading"></com-loading>
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="../../../assets/edit-icon.png" alt="" />
          <span class="header-title">设备详情</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <div class="com-detail-drawer-card">
          <div class="card-title">设备状态</div>
          <div class="card-item">
            <div class="lab">设备状态：</div>
            <div class="val">{{ equiDetail?.status == 0 ? '在线' : '离线' }}</div>
          </div>
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">统计信息</div>
          <div class="card-item">
            <div class="lab">累计巡检任务数：</div>
            <div class="val">{{ equiDetail?.allTaskNum || 0 }}</div>
          </div>
          <div class="card-item">
            <div class="lab">累计发现隐患数：</div>
            <div class="val">{{ equiDetail?.allDisposeNum || 0 }}</div>
          </div>
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">设备信息：</div>
          <div class="card-item">
            <div class="lab">设备编号：</div>
            <div class="val">{{ equiDetail?.deviceNum || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">设备位置：</div>
            <div class="val">{{ equiDetail?.deviceAddress || '--' }}</div>
            <div class="ope" @click="toGisLocation()">查看位置</div>
          </div>
          <div class="card-item">
            <div class="lab">品牌型号：</div>
            <div class="val">{{ produceInfo?.brand || '--' }} {{ produceInfo?.model || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">安装日期：</div>
            <div class="val">{{ installInfo?.install_date || '--' }}</div>
          </div>
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">天泽防灾智盒：</div>
          <div class="card-item">
            <div class="lab">设备编号：</div>
            <div class="val">{{ boxInfo?.macAddr || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">设备位置：</div>
            <div class="val">{{ boxInfo?.ibmName || '--' }}</div>
          </div>
          <!-- <div class="card-item">
            <div class="lab">更新人员：</div>
            <div class="val">{{ '--' }}</div>
          </div> -->
          <div class="card-item">
            <div class="lab">安装日期：</div>
            <div class="val">{{ boxInfo?.createTime || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">更新时间：</div>
            <div class="val">{{ boxInfo?.ibmUpdateTime || '--' }}</div>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { ref, watch, toRaw } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { findIbmInfoByVideoDeviceNumApi } from '../../fetchData';
import { equiListDetailApi } from '../../../fetchData';
import type { IDeviceDetail } from '../../../type';
import ComLoading from '@/components/loading/comLoading.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis } from '@/views/common/utils';

const [loading, run] = useAutoLoading(true);
const emits = defineEmits(['action']);
const active = ref(false);
const actId = ref<string | number>();
const equiDetail = ref<IDeviceDetail>();
const produceInfo = ref();
const installInfo = ref();
const boxInfo = ref();

function open(data: string | number) {
  console.log(data, '打开详情弹框');
  actId.value = data;
  active.value = true;
  //todo
  getDetails(actId.value);
}
function close() {
  active.value = false;
  sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT);
}
function toGisLocation() {
  console.log('查看位置', JSON.stringify(equiDetail.value));
  // const bridgeToGis = new BridgeService('equiDetailList', true);
  // bridgeToGis.sendMessage('equiDetailListUpdate',  equiDetail.value );
  // gis通信-设备定位
  // BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, { type: BRI_EVENT_TYPE.LOCATION });
  sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT_LOCATION, {
    params: toRaw(equiDetail.value),
  });
}

function getDetails(id: string | number) {
  equiListDetailApi(id).then((res) => {
    equiDetail.value = res.data;
    produceInfo.value = JSON.parse(res.data.produceInfo);
    installInfo.value = JSON.parse(res.data.installInfo);
    let loopDeviceNum = JSON.parse(res.data.useInfo);
    findIbmInfoByVideoDeviceNum(loopDeviceNum.loop_device_num);
  });
}
function findIbmInfoByVideoDeviceNum(num: string) {
  run(findIbmInfoByVideoDeviceNumApi({ videoDeviceNum: num })).then((res) => {
    console.log(res, '天泽盒子');
    boxInfo.value = res.data;
  });
}

defineExpose({
  open,
  close,
});

// watch(
//   () => active.value,
//   (v) => {
//     // gis通信-视频详情打开关闭
//     BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
//       type: BRI_EVENT_TYPE.EQUIPMENT_DETAIL,
//       data: { type: v ? 'open' : 'close' },
//     });
//   }
// );

defineOptions({ name: 'VideoEquiDetail' });
</script>

<style lang="scss"></style>
