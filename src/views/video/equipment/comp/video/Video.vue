<template>
  <n-modal
    v-model:show="active"
    class="com-modal"
    preset="dialog"
    :close-on-esc="false"
    :mask-closable="false"
    :show-icon="false"
    :closable="false"
  >
    <template #header>
      <div class="com-modal-header">
        <img class="header-icon" src="../../../assets/edit-icon.png" alt="" />
        <span class="header-title">{{ playerData.name }}</span>
        <div class="btn-close" @click="close">
          <IconClose class="icon" />
        </div>
      </div>
    </template>
    <div class="com-modal-cont">
      <div class="com-modal-video">
        <art-player v-if="playerData.url" :url="playerData.url" id="canvasVideo"></art-player>
        <Empty title="" v-else />
        <!-- <video :src="playerUrl" :autoplay="true" :controls="true" style="width: 696px; height: 430px"></video> -->
      </div>
    </div>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import ArtPlayer from '@/components/player/artPlayer.vue';
import Empty from '@/components/empty/index.vue';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis } from '@/views/common/utils';
const emits = defineEmits(['close']);
const active = ref();

const playerData = ref({
  url: '',
  name: '',
});

function open(data: any) {
  active.value = true;
  playerData.value = data;
}
function close() {
  active.value = false;
  emits('close');
  sendGis(BRI_EVENT_TYPE.VIDEO_EQUIPMENT);
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoPlayer' });
</script>

<style lang="scss"></style>
