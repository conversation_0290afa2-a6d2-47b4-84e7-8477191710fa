<template>
  <n-form :show-feedback="false" label-placement="left">
    <n-flex :size="[20, 10]">
      <!-- <n-form-item label="所属单位:">
        <n-input placeholder="请输入所属单位" v-model:value="filterForm.unitName" clearable class="!w-[260px]" />
      </n-form-item> -->
      <n-form-item label="设备地址:">
        <n-input placeholder="请输入设备地址" v-model:value="filterForm.deviceAddress" clearable class="!w-[260px]" />
      </n-form-item>
      <div style="margin-left: auto">
        <n-button type="primary" @click="doHandle(ACTION.EXPORT)">
          {{ ACTION_LABEL.EXPORT }}
        </n-button>
      </div>
    </n-flex>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);

function initForm() {
  return {
    unitName: '', //所属单位
    deviceAddress: '',
  };
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'VideoEquiFilter' });
</script>

<style module lang="scss"></style>
