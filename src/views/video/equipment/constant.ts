import type { ICardAItem } from '@/components/card/type';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  EDIT = 'EDIT',
  VIDEO = 'VIDEO',
  TREECHANGE = 'TREECHANGE',
  EXPORT = 'EXPORT',
  CARDCHANGE = 'CARDCHANGE',
  REFRESH = 'REFRESH',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.EXPORT]: '导出',
  [ACTION.SEARCH]: '搜索',
  [ACTION.EDIT]: '查看详情',
  [ACTION.VIDEO]: '查看视频',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片筛选',
  [ACTION.REFRESH]: '刷新页面',
};

export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: -1,
  },
  {
    label: '在线（个）',
    value: 0,
    id: 0,
  },
  {
    label: '离线（个）',
    value: 0,
    id: 1,
  },
];
