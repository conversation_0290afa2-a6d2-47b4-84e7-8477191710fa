<template>
  <div class="com-warp-col">
    <com-loading v-if="loading"></com-loading>
    <com-bread :data="breadData"></com-bread>
    <div class="com-warp-col-main" id="drawer-target">
      <com-tab @tab-action="tabChange" :tab="tabAct" :tab-list="tabData"></com-tab>
      <div style="background: none" class="com-warp-col-container">
        <equipment v-if="tabAct === 'equipment'" :tree-list="treeData"></equipment>
        <plan v-if="tabAct === 'plan'" :tree-list="treeData"></plan>
        <task v-if="tabAct === 'task'" :tree-list="treeData" @changeBread="changeBreadFn"></task>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { anchorDark, type TreeOption } from 'naive-ui';
import { ref, onMounted, onBeforeMount, onUnmounted } from 'vue';
import ComLoading from '@/components/loading/comLoading.vue';
import ComTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import equipment from './equipment/index.vue';
import plan from './plan/index.vue';
import task from './task/index.vue';
import { tabData } from './setData';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis } from '@/views/common/utils';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(true);
import { postTreeList } from '../common/fetchData';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from '@/store';
const store = useStore();
//store.setTreeAct(null);

const router = useRouter();
const route = useRoute();
const breadData = ref<IBreadData[]>([{ name: '智能巡检' }, { name: '智能视频轮巡' }]);

const tabAct = ref('');
tabAct.value = route.query.tab ? (route.query.tab as string) : 'task';

function tabChange(v: string) {
  tabAct.value = v;
  router.replace(`video?tab=${v}`);
  const curTree: any = treeData.value[0] || null;
  //store.setTreeAct(curTree);
}

const treeParam = ref({
  needChildUnit: '1', ////不要下级单位,1要，0不要
  needself: '1', //是否包含自己,1包含 0不包含
  orgCode: store.userInfo.orgCode, //机构id=10000,顶级是-1
  // orgRes: 1, //机构来源，1，内部 2 外部
  // removeOrgCode: '', //去除的机构部门id,(下级,本身)
});

const treeData = ref<TreeOption[]>([]);
function treeList() {
  run(postTreeList(treeParam.value))
    .then((res) => {
      treeData.value = res.data || [];
      const curTree: any = treeData.value[0] || null;
      //store.setTreeAct(curTree);
    })
    .catch(() => {});
}
treeList();
function changeBreadFn(v: { text: string; isAdd: boolean }) {
  v.isAdd
    ? breadData.value.splice(breadData.value.length, 0, { name: v.text })
    : breadData.value.splice(breadData.value.length - 1, 1);
}
// onBeforeMount(() => {
//   // gis切换视频撒点
//   //window.open(window.$SYS_CFG.gisLink, '_blank');
//   //const _host = window.location.origin;
//   window.open(window.$SYS_CFG.gisLink, 'ehsInspect-gisWindow');
// });

const actTree = ref<any>({
  deptIds: store.userInfo.deptId,
  id: store.userInfo.orgCode || store.userInfo.deptId,
  text: store.userInfo.deptName,
  treeName: store.userInfo.deptName,
  unitId: store.userInfo.unitId,
  unitIds: store.userInfo.unitId,
});
onBeforeMount(() => {
  //打开GIS
  //window.open(window.$SYS_CFG.gisLink, 'ehsInspect-gisWindow');
  // jumpMap();
});
function jumpMap() {
  //window.open(import.meta.env.VITE_APP_GISMAP);
  window.open(window.$SYS_CFG.gisLink, 'ehsInspect-gisWindow');
  // 等待gis加载完成后再发送消息
  setTimeout(() => {
    console.log('VIDEO_TASK 延迟发送');
    sendGis(BRI_EVENT_TYPE.VIDEO_TASK, {
      isMap: true,
      drawingTree: {
        deptIds: actTree.value.deptIds,
        id: actTree.value.id,
        text: actTree.value.text,
        treeName: actTree.value.treeName,
        unitId: actTree.value.unitId,
        unitIds: actTree.value.unitIds,
      },
    });
  }, 3500);
}

onMounted(() => {
  console.log(route.query, 'query');
});

defineOptions({ name: 'VideoIndex' });
</script>

<style module lang="scss"></style>
