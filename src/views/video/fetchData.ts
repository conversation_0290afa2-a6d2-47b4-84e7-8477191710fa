import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { IDeviceItem, IDeviceDetail } from './type';

//巡检视频列表 选择视频类别地方
export function deviceListApi(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.videoDeviceList, query);
  return $http.get<IDeviceItem[]>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

//获取设备视频
export function equiVideoApi(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiListVideourl, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
//设备详情
export function equiListDetailApi(id: string | number, query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.equiListDetail, query, id);
  return $http.get<IDeviceDetail>(url, { data: { _cfg: { showTip: true } } });
}
