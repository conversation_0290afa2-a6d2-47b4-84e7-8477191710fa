import type { ICardAItem } from '@/components/card/type';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EXPORT = 'EXPORT',
  TREECHANGE = 'TREECHANGE',
  CARDCHANGE = 'CARDCHANGE',
  REFRESH = 'REFRESH',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAILS]: '详情',
  [ACTION.EXPORT]: '导出',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片切换筛选',
  [ACTION.REFRESH]: '刷新页面',
};
////全部：-1   0待开始，1：未执行/逾期，2：进行中，3，已完成
export const TASK_STATE = {
  LAB: ['待开始', '已逾期', '进行中', '已完成'],
  COLOR: ['#F39600', '#FA5151', '#527CFF', '#00B578'],
};

//统计卡片
export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: -1,
  },
  {
    label: '进行中（个）',
    value: 0,
    id: 2,
  },
  {
    label: '待开始（个）',
    value: 0,
    id: 0,
  },
  {
    label: '已完成（个）',
    value: 0,
    id: 3,
  },
  {
    label: '已逾期（个）',
    value: 0,
    id: 1,
  },
];

export const iFrequencyType = ['', '小时', '日', '周', '月', '季度', '年'];
