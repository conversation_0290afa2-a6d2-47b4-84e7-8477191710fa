import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IDetail {
  createTime: string;
  createdBy: string;
  delFlag: number;
  abnormalNum: 0;
  deptIds: string;
  planFrequency: 0;
  planFrequencyUnit: 0;
  progressNum: 0;
  deptId: string;
  deptName: string;
  id: string | number;
  planId: string | number;
  planName: string;
  recordPoint: string;
  taskEndTime: string;
  taskPlanEndTime: string;
  taskPlanStartTime: string;
  taskStartTime: string;
  taskStatus: string | number;
  updateTime: string;
  updatedBy: string;
  zhId: string;
  relVideos: any[];
  resultRecord: any[];
  relTaskVideos: any[];
}
export type IPageDataRes = IPageRes<IDetail>;

export type ITgeType = 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export type IProgressStatus = 'default' | 'success' | 'error' | 'warning' | 'info';
