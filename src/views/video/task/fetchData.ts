import type { IDetail, IPageDataRes } from './type';
import type { ITaskInspReport } from './details/type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//任务列表
export function taskListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.taskList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}
//任务详情
export function taskDetailApi(id: string | number, query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.taskDetails, query, id);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
//任务列表统计
export function taskRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.taskRecord, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
//任务巡检状态更改
export function taskUpStatusApi(data: { id: string | number; planStatus: string | number }) {
  const param = api.getComParams(api.type.intelligent, api.video.taskUpstatus, data);
  return $http.put(param.url, { data: { _cfg: { showTip: true, showOkTip: false }, ...param.data } });
}

//任务巡检异常上报
export function taskReportApi(data: ITaskInspReport) {
  const param = api.getComParams(api.type.intelligent, api.video.taskReport, data);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}
//任务巡检正常上报
export function taskReportOkApi(videoId: string, data: any) {
  const param = api.getComParams(api.type.intelligent, api.video.taskReportOk, videoId);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data, ...data } });
}
//任务巡检上报结果查询
export function taskRuseltDeviceApi(id: string) {
  const url = api.getUrl(api.type.intelligent, api.video.taskRuseltDevice, null, id);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

//上报处理过程
export function taskHazardClassApi(data: { disposeId: string | number }) {
  const url = api.getUrl(api.type.intelligent, api.video.taskHazardClass, data);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

//上报隐患字段
export function taskHazardGradeApi(data: any) {
  const param = api.getComParams(api.type.intelligent, api.video.taskHazardGrade, data);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}

//获取隐患分类
export function hazardClassList(data: any) {
  const param = api.getComParams(api.type.intelligent, api.name.serve.hazardClassList, data);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}

//获取隐患处理记录
export function hazardRecord(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.serve.hazardRecord, query);
  return $http.get(url, { data: { _cfg: { showTip: true } } });
}
