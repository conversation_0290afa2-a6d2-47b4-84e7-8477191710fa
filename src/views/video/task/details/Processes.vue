<template>
  <!-- 处理过程查看 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <com-loading v-if="loading"></com-loading>
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">处理过程</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <template v-if="detailData.length > 0">
          <div v-for="(item, index) in detailData" :key="item.id">
            <div class="card-item">
              <div class="_xuhao">{{ index + 1 }}</div>
              <div class="_jieshou">{{ item.nodeName }}</div>
            </div>
            <div style="margin-left: 48px" class="card-item" v-for="(value, index) in item.nodeInfo" :key="index">
              <div class="lab">{{ value.description }}：</div>
              <template v-if="value.webType === 'string'">
                <div class="val">{{ value.dataValue || '--' }}</div>
              </template>
              <template v-else>
                <div class="_all_n_image" v-for="images in value.dataValue" :key="images">
                  <n-image width="150" :src="`${baseImage}${images}`" />
                </div>
              </template>
            </div>
          </div>
        </template>
        <div style="margin-top: 100px" v-else>
          <Empty title="暂无数据" />
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { taskHazardClassApi, hazardRecord } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(true);

const active = ref(false);
const disposeId = ref<string | number>('');
const detailData = ref<any>([]);

const baseImage = window.$SYS_CFG.apiBaseURL;
function open(id: string | number) {
  // console.log(id, '打开详情的处理过程弹框');
  disposeId.value = id;
  active.value = true;
  //todo 内容获取
  // getDetails();
  getHazardRecord();
}
function close() {
  active.value = false;
  detailData.value = [];
}

function getDetails() {
  run(taskHazardClassApi({ disposeId: disposeId.value })).then((res) => {
    // console.log('taskHazardClassApi = 详情', res);
    detailData.value = res.data;
  });
}
function getHazardRecord() {
  // disposeId.value = '1840037708397092866';
  hazardRecord({ disposeId: disposeId.value }).then((res: any) => {
    console.log('hazardRecord res = --- 处理过程', res);
    detailData.value = res.data.rows;
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskStatuDetail' });
</script>

<style lang="scss">
.task-status-detail {
  .n-drawer-header__main {
    width: 100%;
  }
  .status-detail-header {
    display: flex;
    width: 100%;
    .header-icon {
      width: 18px;
      height: auto;
      margin-right: 8px;
    }
    .header-title {
      font-weight: 600;
      font-size: 16px;
      color: #18191a;
    }
    .btn-close {
      margin-left: auto;
      cursor: pointer;
    }
  }
  .status-detail-cont {
    padding: 20px 24px;
    .status-img {
      width: 100%;
      height: 266px;
      border-radius: 4px;
      background: #f0f0f0;
      overflow: hidden;
      .img {
        width: auto;
        height: 100%;
        margin: 0 auto;
      }
      .no-img {
        width: auto;
        height: 80px;
        margin: 20% auto 0;
      }
    }
    .status-card {
      min-height: 242px;
      border-radius: 4px;
      border: 1px solid #ebeef5;
      padding: 20px;
      margin-top: 16px;
      line-height: 20px;
      .card-title {
        font-weight: 600;
        font-size: 16px;
        color: #222222;
      }
      .card-item {
        margin-top: 16px;
        display: flex;
      }
      .ope {
        margin-left: auto;
        color: #527cff;
        cursor: pointer;
      }
    }
  }
}
._jieshou {
  font-size: 18px;
  position: relative;
  margin-left: 15px;
}
._xuhao {
  color: #fff;
  text-align: center;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background-color: #3e62eb;
  border-radius: 50%;
  font-size: 18px;
}
// ._picture {
//   margin-right: 5px;
// }
// .val {
//   margin-left: 5px;
// }
._desc {
  width: 110p;
}
._all_n_image {
  display: flex;
  flex-wrap: nowrap;
}
._all_n_image image {
  margin-right: 5px;
}
</style>
