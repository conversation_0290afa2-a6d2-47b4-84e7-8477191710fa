import { TASKACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: TASKACTION;
  data: IObj<any>;
}

export interface ITaskVideoDatil {
  disposeId: string;
  disposeStatus: number;
  id: string;
  taskId: string;
  updateTime: string;
  updatedBy: string;
  videoAddress: string;
  videoDesc: string;
  videoId: string;
  videoName: string;
  videoNum: string;
  videoResult: number;
  videoSort: number;
  videoTime: string;
  videoUrl: string;
  manufacturerCode: string;
}

//巡检上报提交类型
//上传图片类型
export interface IUploadFiles {
  fileName: string;
  fileType: string;
  fileUrl: string;
}
export interface IreformUser {
  reformUserId: string;
  reformUserName: string;
}
export interface ITaskInspReport {
  hazardSource: number;
  hazardSourceName: string;
  //startTime: string; //上报开始时间
  //endTime: string; //上报结束时间
  eventTime: string | null; //上报时间
  hazardPosition: string; //隐患位置
  hazardDesc: string; //隐患描述
  hazardLevel: string; //等级
  hazardLevelName: string; //等级名称
  randomCheckId: string;
  files: IUploadFiles[]; //图片上传
  reformUserJson: IreformUser[]; //整个人员
  unitId: string;
  deviceId: string;
  remark: string; //备注
  hazardType: string | null; //隐患分类
  hazardTypeName: string; //隐患分类名称
}
