<template>
  <!-- 上报 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <com-loading v-if="loading"></com-loading>
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">新建隐患</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :size="size"
          :style="{
            maxWidth: '470px',
          }"
        >
          <n-form-item label="隐患来源">
            <n-input default-value="智能视频巡检" :disabled="true" />
          </n-form-item>
          <n-form-item label="上报设备">
            <n-input :default-value="deviceName" :disabled="true" />
          </n-form-item>
          <n-form-item label="上报时间" path="eventTime">
            <!-- <n-date-picker
              v-model:formatted-value="formModel.eventTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              clearable
              style="width: 100%"
            /> 报格式错误-->
            <n-date-picker
              :value="rangeDate"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              clearable
              style="width: 100%"
              @update:value="dateRangeChange"
            />
          </n-form-item>
          <n-form-item label="隐患位置" path="hazardPosition">
            <n-input v-model:value="formModel.hazardPosition" placeholder="请输入隐患位置" maxlength="50" />
          </n-form-item>
          <n-form-item label="隐患描述" path="hazardDesc">
            <n-input
              v-model:value="formModel.hazardDesc"
              placeholder="请输入隐患描述"
              maxlength="500"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
          </n-form-item>
          <n-form-item label="隐患等级" path="hazardLevel">
            <n-radio-group v-model:value="formModel.hazardLevel" name="radiogroup1" @change="levelChange">
              <n-space>
                <n-radio v-for="leve in hazardLevelOpt" :key="leve.id" :value="leve.id"> {{ leve.gradeName }} </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="隐患类别" path="hazardType">
            <n-select
              class="!w-[260px]"
              v-model:value="formModel.hazardType"
              :options="idOptions"
              placeholder="请选择隐患类别"
              label-field="className"
              value-field="id"
              :loading="loading"
              clearable
              filterable
              remote
              @update:value="searchHazard"
            />
          </n-form-item>
          <n-form-item label="现场照片" path="files">
            <n-space>
              <div class="video-default" v-if="videoScreenshotData.fileUrl">
                <!-- 默认截图文件显示  :default-file-list="defaultFileList" -->
                <a>
                  <img :src="fileHtp + videoScreenshotData.fileUrl" />
                  <div class="video-default-mask">
                    <div class="video-default-mask-btn" @click="handlePreview(0, true)"><AkEye /></div>
                    <div class="video-default-mask-btn" @click="hanlePrevDel"><LaTrashAltSolid /></div>
                  </div>
                </a>
              </div>
              <img-upload
                @update="handleUpdate"
                :data="imgData"
                :size="10"
                mode="browse"
                tips="请上传png或jpg格式图片"
                @prev="handlePreview"
                @updateBefore="updateBeforeFn"
              ></img-upload>
            </n-space>
          </n-form-item>
          <n-form-item label="备注">
            <n-input
              v-model:value="formModel.remark"
              placeholder="请输入备注"
              maxlength="500"
              type="textarea"
              :autosize="{
                minRows: 3,
                maxRows: 5,
              }"
            />
          </n-form-item>
        </n-form>
      </div>
      <template #footer>
        <div class="com-detail-drawer-footer">
          <n-button type="primary" @click="close" color="#DCDFE6"> 取消 </n-button>
          <n-button type="primary" @click="handleValidateButtonClick" :loading="submitLoading">确定 </n-button>
        </div>
      </template>
      <n-modal v-model:show="showModal" preset="card" style="width: 600px">
        <img :src="previewImageUrl" style="width: 100%" />
      </n-modal>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { TASKACTION, REPORTFORM, RULES } from './constant';
import { taskReportApi, taskHazardGradeApi, hazardClassList } from '../fetchData';
import { ImgUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import type { ITaskVideoDatil, IUploadFiles } from './type';
import { dayjs } from '@/utils/dayjs';
import type { FormInst, FormItemRule, UploadFileInfo } from 'naive-ui';
import { AkEye, LaTrashAltSolid } from '@kalimahapps/vue-icons';
import { api } from '@/api';
import ComLoading from '@/components/loading/comLoading.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);
const submitLoading = ref(false);
const emits = defineEmits(['action']);
import { useStore } from '@/store';
const store = useStore();

const fileHtp = window.$SYS_CFG.fileService;

const props = defineProps({
  unitId: {
    type: String,
    default: '',
  },
});
const imgData = ref<any>([]);
function handleUpdate(res: IUploadRes[]) {
  console.log('上传回调', res);
  if (!res) return;
  imgData.value = [];
  formModel.value.files = [];
  res.map((item) => {
    imgData.value.push({
      fjMc: item,
      fjCflj: item,
      fjTywysbm: item,
    });
    formModel.value.files.push({
      fileName: '',
      fileType: '',
      fileUrl: `${item}`,
    });
  });
  loading.value = false;
}
//预览
const showModal = ref(false);
const previewImageUrl = ref('');
function handlePreview(index: number, isD: boolean) {
  console.log(index, isD, imgData.value, '文件预览');
  let curIndex = videoScreenshotData.value.fileUrl ? index + 1 : index;
  let curUrl = isD ? videoScreenshotData.value.fileUrl : formModel.value.files[curIndex].fileUrl;
  previewImageUrl.value = window.$SYS_CFG.fileService + curUrl;

  showModal.value = true;
}
function hanlePrevDel() {
  videoScreenshotData.value = { ...videoDataD };
  formModel.value.files.shift();
  console.log(formModel.value, '删除图片---');
}
function searchHazard(value: string) {
  console.log('value = ', value);
  let ret = idOptions.value.filter((item: any) => item.id === value);
  formModel.value.hazardTypeName = ret.length ? ret[0].className : '';
}
//提交
const formRef = ref<FormInst | null>(null);
const size = ref('medium');
const formModel = ref({ ...REPORTFORM });
const rules = RULES;
function handleValidateButtonClick(e: MouseEvent) {
  submitLoading.value = true;
  e.preventDefault();
  //增加unitId
  formModel.value.unitId = props.unitId;
  formRef.value?.validate((errors) => {
    if (!errors) {
      console.log('验证成功');
      taskReportApi(formModel.value)
        .then((res) => {
          emits('action', { action: TASKACTION.REPORTSUCSSE, data: {} });
          submitLoading.value = false;
          close();
        })
        .catch((err) => {
          console.log(err, '提交错误');
          submitLoading.value = false;
        });
    } else {
      console.log('验证失败', errors);
      submitLoading.value = false;
    }
  });
}
//被打开
const active = ref(false);
const videoInsp = ref<ITaskVideoDatil>();
const videoScreenshotFile = ref();
const rangeDate = ref<any>(null);
function open(data: ITaskVideoDatil, videoImgFile: any) {
  console.log(data, videoImgFile, '打开上报异常弹框');
  videoInsp.value = data;
  formModel.value.randomCheckId = videoInsp.value?.id;
  formModel.value.deviceId = videoInsp.value?.videoId;
  videoScreenshotFile.value = videoImgFile;
  //复制默认时间
  rangeDate.value = Date.now();
  formModel.value.eventTime = dayjs(Date.now()).format('YYYY-MM-DD hh:mm:ss');
  taskHazardGrade();
  if (videoScreenshotFile.value) {
    loading.value = true;
    toUpload();
  }
  active.value = true;
}
//关闭
function close() {
  active.value = false;
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
  formModel.value = { ...REPORTFORM };
  videoScreenshotData.value = { ...videoDataD };
  imgData.value = [];
  formModel.value.files = [];
  console.log(formModel.value, videoScreenshotData.value, '关闭');
}
const deviceName = computed(() => {
  return videoInsp.value?.videoName || '';
});
//日期转换
function dateRangeChange(value: number | null, formattedValue: string | null) {
  formModel.value.eventTime = formattedValue;
  rangeDate.value = value;
}
//日期面板打开关闭
function dateRangeShow(show: boolean) {
  console.log(show, rangeDate.value, '日期面板打开关闭-----');
}
//默认截图
const videoDataD = {
  fileName: '',
  fileType: '',
  fileUrl: '',
};
const videoScreenshotData = ref<IUploadFiles>({ ...videoDataD });
function toUpload() {
  const actionURL = api.getUrl(api.type.intelligent, api.name.file.uploadFile);
  //file
  const formData = new FormData();
  formData.append('file', videoScreenshotFile.value);
  const xhr = new XMLHttpRequest();
  xhr.onload = () => {
    const imageUrl = JSON.parse(xhr.responseText).data; // 接口回调参数
    console.log('默认截图上传成功', imageUrl);
    videoScreenshotData.value = {
      fileName: '',
      fileType: '',
      fileUrl: imageUrl,
    };
    //formModel.value.files.unshift(videoScreenshotData.value);
    if (videoScreenshotData.value.fileUrl) {
      formModel.value.files.unshift(videoScreenshotData.value);
    }
    loading.value = false;
  };
  xhr.open('POST', actionURL, true); // '/api/upload'是上传接口
  xhr.send(formData);
}
//
function levelChange() {
  let curLab = hazardLevelOpt.value.filter((item) => item.id == formModel.value.hazardLevel);
  formModel.value.hazardLevelName = curLab.length ? curLab[0].gradeName : '';
}
function updateBeforeFn() {
  loading.value = true;
}

//hu
const hazardLevelOpt = ref<any[]>([]);
function taskHazardGrade() {
  run(
    taskHazardGradeApi({
      createTime: '',
      delFlag: 0,
      gradeName: '',
      gradeSort: 0,
      id: '',
      parentId: '',
      parentName: '',
      unitId: store.userInfo.topUnitId,
      zhId: '',
    })
  ).then((res) => {
    hazardLevelOpt.value = res.data ? (res.data as any) : [];
  });
}
const idOptions = ref<any>([]);
function getHazardList() {
  hazardClassList({ children: [], unitId: store.userInfo.topUnitId }).then((res: any) => {
    idOptions.value = res.data;
  });
}
getHazardList();

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskInspReport' });
</script>

<style lang="scss">
.com-detail-drawer {
  .video-default {
    width: 98px;
    height: 98px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    position: relative;
    img {
      height: 100%;
      width: 100%;
    }
    a:hover {
      .video-default-mask {
        visibility: visible;
      }
    }
    .video-default-mask {
      visibility: hidden;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      .video-default-mask-btn {
        font-size: 18px;
        font-weight: 300px;
        margin: 0 5px;
        cursor: pointer;
      }
    }
  }
}
</style>
