<template>
  <!-- 状态结果详情 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <com-loading v-if="loading"></com-loading>
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">
            <!-- {{ equiDetail?.deviceName }} -->
            巡查结果详情
          </span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <div class="status-img">
          <img
            v-if="ruseltDetail?.videoUrl"
            :src="getVideoUrl(ruseltDetail)"
            class="img"
            @click="handlePreview(ruseltDetail)"
          />
          <img v-else src="@/assets/noData.png" class="no-img" />
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">设备信息</div>
          <div class="card-item">
            <div class="lab">上报设备：</div>
            <div>{{ equiDetail?.deviceName || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">上报时间：</div>
            <div>{{ ruseltDetail?.createTime || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">设备编号：</div>
            <div>{{ equiDetail?.deviceNum || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">设备位置：</div>
            <div>{{ equiDetail?.deviceAddress || '--' }}</div>
            <div class="ope" @click="handleGis">
              <!-- 显示gis图 -->
              查看位置
            </div>
          </div>
          <div class="card-item">
            <div class="lab">品牌型号：</div>
            <div>{{ produceInfo?.brand || '--' }}</div>
          </div>
          <div class="card-item">
            <div class="lab">安装日期：</div>
            <div>{{ equiDetail?.createTime }}</div>
          </div>
          <div class="card-item">
            <div class="lab">天泽防灾智盒ID：</div>
            <!-- 缺少字段 -->
            <div>--</div>
          </div>
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">巡检结果</div>
          <template v-if="ruseltDetail?.videoSource == '2'">
            <div class="card-item" v-for="item in ruseltDetail.disposeList" :key="item.id">
              <div class="lab">{{ item.eventTypeName }}：</div>
              <div class="_success" v-if="item.videoResult === 0">{{ videoResult2(item.videoResult) }}</div>
              <div class="_wait" v-else-if="item.videoResult === 1">{{ videoResult2(item.videoResult) }}</div>
              <div class="_error" v-else-if="item.videoResult === 2">{{ videoResult2(item.videoResult) }}</div>
              <div v-else>--</div>
              <div class="ope" @click="toPro2(item.disposeId)" v-if="item.videoResult == 2">处理过程</div>
            </div>
          </template>
          <template v-else>
            <div class="card-item">
              <div class="lab">巡检结果：</div>
              <div class="_success" v-if="ruseltDetail?.videoResult === 0">{{ videoResult }}</div>
              <div class="_wait" v-else-if="ruseltDetail?.videoResult === 1">{{ videoResult }}</div>
              <div class="_error" v-else-if="ruseltDetail?.videoResult === 2">{{ videoResult }}</div>
              <div v-else>--</div>
              <div class="ope" @click="toPro" v-if="ruseltDetail?.videoResult === 2">处理过程</div>
            </div>
            <!-- 异常数据才显示隐患信息 非异常则不展示 -->
            <template v-if="ruseltDetail?.videoResult === 2">
              <div class="card-item">
                <div class="lab">隐患描述：</div>
                <div>{{ ruseltDetail?.hazardDesc || '--' }}</div>
              </div>
              <div class="card-item">
                <div class="lab">隐患分类：</div>
                <div>{{ ruseltDetail?.hazardClass || '--' }}</div>
              </div>
              <div class="card-item">
                <div class="lab">隐患等级：</div>
                <div>{{ ruseltDetail?.hazardGrade || '--' }}</div>
              </div>
              <div class="card-item">
                <div class="lab">隐患位置：</div>
                <div>{{ ruseltDetail?.hazardPosition || '--' }}</div>
              </div>
              <div class="card-item">
                <div class="lab _remark">备注：</div>
                <div class="val _remark_content">{{ ruseltDetail?.remark || '--' }}</div>
              </div>
            </template>
            <div class="card-item">
              <div class="lab">上报人员：</div>
              <div>{{ ruseltDetail?.updatedByName || '--' }}</div>
            </div>
            <div class="card-item">
              <div class="lab">上报时间：</div>
              <div>{{ ruseltDetail?.videoTime || '--' }}</div>
            </div>
          </template>
        </div>
      </div>
      <n-modal v-model:show="showModal" preset="card" style="width: 600px">
        <img :src="previewImageUrl" style="width: 100%" />
      </n-modal>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, PropType } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { TASKACTION } from './constant';
import ComLoading from '@/components/loading/comLoading.vue';
import { taskRuseltDeviceApi } from '../fetchData';
import { equiListDetailApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(true);
import { sendGis } from '@/views/common/utils';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { useStore } from '@/store';

const emits = defineEmits(['action']);
const active = ref(false);
const taskVideoAct = ref();
const equiDetail = ref<any>();
const ruseltDetail = ref<any>();
const produceInfo = ref();

function open(data: any) {
  console.log(data, '打开巡检结果详情弹框');
  taskVideoAct.value = { ...data };
  active.value = true;
  equiDetail.value = null;
  ruseltDetail.value = null;
  //todo
  getDetails();
  //增加打开视频详情就直接在GIS中定位的需求
  handleGis();
}
function close() {
  active.value = false;
  sendGis(BRI_EVENT_TYPE.VIDEO_TASK_DETAIL, { taskId: taskVideoAct.value.taskId });
}
const videoResult = computed(() => {
  const lab = ['正常', '待巡检', '异常'];
  let curLab = '--';
  if (ruseltDetail.value) {
    curLab = lab[ruseltDetail.value.videoResult];
  }
  return curLab;
});
function videoResult2(val) {
  const lab = ['正常', '待巡检', '异常'];
  let curLab = '--';
  if (ruseltDetail.value.disposeList.length > 0) {
    curLab = lab[val];
  }
  return (curLab = lab[val]);
}
function toPro() {
  emits('action', { action: TASKACTION.PROCESSES, data: { disposeId: ruseltDetail.value.disposeId } });
}
function toPro2(val) {
  emits('action', { action: TASKACTION.PROCESSES, data: { disposeId: val } });
}
function getVideoUrl(value: any) {
  let curV = value.videoUrl.split(',');
  let url = value.videoSource === 1 ? window.$SYS_CFG.fileService + curV[0] : window.$SYS_CFG.apiBaseURL + curV[0];
  return url;
}
function getDetails() {
  equiListDetailApi(taskVideoAct.value.videoId)
    .then((res) => {
      equiDetail.value = res.data;
      produceInfo.value = JSON.parse(res.data.produceInfo as string);
    })
    .finally(() => {
      run(taskRuseltDeviceApi(taskVideoAct.value.id)).then((res: any) => {
        // 延长石油不展示 personOff
        if (res.data.disposeList && res.data.disposeList.length > 0) {
          const store = useStore();
          if (store.userInfo.zhId === 'ycsyqt') {
            res.data.disposeList = res.data.disposeList.filter((item: any) => item.eventType !== 'personOff');
          }
        }
        ruseltDetail.value = res.data;
      });
    });
}
//预览
const showModal = ref(false);
const previewImageUrl = ref('');
function handlePreview(value: any) {
  if (!value.videoUrl) {
    return;
  }
  previewImageUrl.value = getVideoUrl(value);
  showModal.value = true;
}

//查看位置
function handleGis() {
  sendGis(BRI_EVENT_TYPE.VIDEO_TASK_DETAIL_STATUS, {
    videoId: taskVideoAct.value.id,
    taskId: taskVideoAct.value.taskId,
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskStatuDetail' });
</script>

<style lang="scss">
.com-detail-drawer {
  .com-detail-drawer-cont {
    .status-img {
      width: 100%;
      height: 266px;
      border-radius: 4px;
      background: #f0f0f0;
      overflow: hidden;
      margin-bottom: 16px;
      .img {
        width: auto;
        height: 100%;
        margin: 0 auto;
        cursor: pointer;
      }
      .no-img {
        width: auto;
        height: 80px;
        margin: 20% auto 0;
      }
    }
  }
  ._success {
    color: #2ba471;
    position: relative;
    margin-left: 5px;
  }
  ._success::before {
    content: ' ';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #2ba471;
    position: absolute;
    top: 7px;
    left: -8px;
  }
  ._error {
    color: #ea0000;
    position: relative;
    margin-left: 5px;
  }
  ._error::before {
    content: ' ';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #ea0000;
    position: absolute;
    top: 7px;
    left: -8px;
  }
  ._wait {
    color: #f39600;
    position: relative;
    margin-left: 5px;
  }
  ._wait::before {
    content: ' ';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #f39600;
    position: absolute;
    top: 7px;
    left: -8px;
  }
}
._remark {
  width: 70px;
  text-align: right;
}

._remark_content {
  width: 350px;
}
</style>
