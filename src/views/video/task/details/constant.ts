import type { IDetail } from '../type';
import type { ITaskInspReport } from './type';
export const enum PROVIDE_KEY {
  detailData = 'detailData',
}
export const enum TASKACTION {
  NONE = 'NONE',
  LAYER = 'LAYER',
  DETAILS = 'DETAILS',
  REPORT = 'REPORT',
  REPORTSUCSSE = 'REPORTSUCSSE',
  INSPECTIONHANDCLOSE = 'INSPECTIONHANDCLOSE',
  PROCESSES = 'PROCESSES',
  STARTINSPECTION = 'STARTINSPECTION',
  STARTITEMINSPECTION = 'STARTITEMINSPECTION',
}

export const ACTION_LABEL: { [key in TASKACTION]: string } = {
  [TASKACTION.NONE]: '',
  [TASKACTION.LAYER]: '布局更改',
  [TASKACTION.DETAILS]: '任务的状态详情',
  [TASKACTION.REPORT]: '巡检视频上报',
  [TASKACTION.PROCESSES]: '结果状态详情下的处理过程弹框',
  [TASKACTION.STARTINSPECTION]: '开始巡检',
  [TASKACTION.STARTITEMINSPECTION]: '单项开始巡检',
  [TASKACTION.REPORTSUCSSE]: '巡检上报成功',
  [TASKACTION.INSPECTIONHANDCLOSE]: '巡检上报视频手动关闭',
};
//videoResult  巡检视频结果 0：正常，1：待巡检，2：异常
//disposeStatus   巡检异常处置状态 1：解决，2：未解决
export const TASK_XUNJIAN_STATE = {
  LAB: ['无异常', '待巡检', '有异常'],
  COLOR: ['#00B578', '#F39600', '#FA5151'],
};

export const DETAILS: IDetail = {
  createTime: '',
  createdBy: '',
  delFlag: 0,
  deptId: '',
  deptName: '',
  id: '',
  planId: '',
  planName: '',
  recordPoint: '',
  taskEndTime: '',
  taskPlanEndTime: '',
  taskPlanStartTime: '',
  taskStartTime: '',
  taskStatus: 0,
  updateTime: '',
  updatedBy: '',
  zhId: '',
  abnormalNum: 0,
  deptIds: '',
  planFrequency: 0,
  planFrequencyUnit: 0,
  progressNum: 0,
  relVideos: [],
  resultRecord: [],
  relTaskVideos: [],
};

//视频巡检隐患上报 默认字段

export const REPORTFORM: ITaskInspReport = {
  //隐患来源(1:物联网监测 2:人工上报 3:智能视频终端 4:监督检查隐患) 5:巡查检查 6:督导检查,7:专项检查,8:隐患自查,9:随机检查,
  //10:隐患随手拍,11:设备点检,12:点位巡查,13:作业巡查 ,14、智能视频轮巡15、机器人自动巡检16、无人机自动巡检17、人工智能巡检18、人工上报（视频）
  //19、人工上报（无人机）20、人工上报（机器人）
  hazardSource: 18,
  hazardSourceName: '人工上报（视频）',
  //startTime: '', //上报开始时间
  //endTime: '', //上报结束时间
  eventTime: '',
  hazardPosition: '', //隐患位置
  hazardDesc: '', //隐患描述
  hazardLevel: '', //等级
  hazardLevelName: '', //等级名称
  randomCheckId: '', //id
  files: [], //现场图片
  reformUserJson: [
    {
      reformUserId: '',
      reformUserName: '',
    },
  ], //上报人员
  unitId: '',
  deviceId: '',
  remark: '',
  hazardType: null, //隐患分类
  hazardTypeName: '', //隐患分类名称
};

//
export const RULES = {
  eventTime: {
    required: true,
    trigger: ['change'],
    message: '请选择上报时间',
  },
  hazardPosition: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入隐患位置',
  },
  hazardDesc: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入隐患描述',
  },
  hazardLevel: {
    required: true,
    trigger: ['change'],
    message: '请选择隐患等级',
  },
  files: {
    type: 'array',
    required: true,
    trigger: ['change', 'blur'],
    message: '请上传现场图片',
  },
  hazardType: {
    required: true,
    trigger: ['change'],
    message: '请选择隐患类别',
  },
};
