<template>
  <div class="task-detail-top">
    <div class="top">
      <div class="_video_title">视频巡检</div>
      <div class="action">
        <n-button v-if="detailData.taskStatus === 0" type="primary" size="small" @click="startAction(2)"
          >开始巡检</n-button
        >
        <n-button v-if="detailData.taskStatus === 2" type="primary" size="small" @click="startAction(3)"
          >结束巡检</n-button
        >
        <n-button
          v-if="detailData.taskStatus === 3"
          :disabled="detailData.taskStatus !== 3"
          type="primary"
          size="small"
          :color="TASK_STATE.COLOR[detailData.taskStatus]"
          @click="taskReview"
          >任务回溯</n-button
        >
      </div>
    </div>
    <div class="desc">
      <div class="desc-item">
        巡检点数：<span class="num">{{ recordNum.allNum }}</span
        >个
      </div>
      <div class="desc-item">
        已巡点总数：<span class="num green">{{ recordNum.useNum }}</span
        >个
      </div>
      <div class="desc-item">
        未巡点总数：<span class="num red">{{ recordNum.waitOpenNum }}</span
        >个
      </div>
      <div class="desc-item">
        结果正常点位数：<span class="num green">{{ recordNum.finishedNum }}</span
        >个
      </div>
      <div class="desc-item">
        结果异常点位数：<span class="num red">{{ recordNum.overdueNum }}</span
        >个
      </div>
      <!-- <div class="list-action">
        <div class="block" v-for="b in 3" :key="b" :class="{ act: colsNum === b }" @click="closChange(b)">
          <div class="block-item" v-for="i in cols[b - 1]" :key="i"></div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { TASKACTION, DETAILS } from '../constant';
import { TASK_STATE } from '../../constant';
import { useActionComfirm } from '@/common/hooks/comfirm.ts';

import { sendGis } from '@/views/common/utils';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';

const emits = defineEmits(['action']);
const props = defineProps({
  data: {
    type: Object,
  },
});
const detailData = computed(() => {
  return props.data ? props.data : { ...DETAILS };
});
const recordNum = computed(() => {
  return props.data?.resultRecord || { allNum: 0, finishedNum: 0, waitOpenNum: 0 };
});

const cols = ref([1, 4, 9]);
const colsNum = ref(3);
function closChange(c: number) {
  colsNum.value = c;
  emits('action', { action: TASKACTION.LAYER, data: { colsNum: c } });
}
//开始巡检/结束巡检
function startAction(s: number | string) {
  if (s === 3) {
    let len = props.data?.resultRecord.waitOpenNum || 0;
    useActionComfirm({
      msg: len === 0 ? `请问是否结束本次巡检？` : `还有${len}个巡检点没有上报巡检结果，请问是否真的结束本次巡检？`,
      cannelText: `取消结束巡检`,
    }).then(() => {
      emits('action', {
        action: TASKACTION.STARTINSPECTION,
        data: { taskStatus: s },
      });
    });
    return;
  }
  emits('action', {
    action: TASKACTION.STARTINSPECTION,
    data: { taskStatus: s },
  });
}

// 任务回溯
function taskReview() {
  // 通知gis
  // BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
  //   type: BRI_EVENT_TYPE.TASK_REVIEW,
  // });
  sendGis(BRI_EVENT_TYPE.VIDEO_TASK_REVIEW, { taskId: detailData.value.id });
}

defineOptions({ name: 'VideoTaskDetailListAction' });
</script>

<style lang="scss">
.task-detail-top {
  .top {
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    ._video_title {
      font-weight: bolder;
    }
  }
  .desc {
    padding: 24px 24px 12px;
    display: flex;
    .desc-item {
      margin-right: 32px;
    }
    .num {
      color: #212121;
      &.green {
        color: #2ba471;
      }
      &.red {
        color: #d54941;
      }
    }
  }
  .list-action {
    margin-left: auto;
    display: flex;
    .block {
      width: 25px;
      height: 25px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #c7cdd9;
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-around;
      // align-content: center;
      cursor: pointer;
      padding: 4px;
      &.act {
        background-color: #527cff;
        .block-item {
          background-color: #fff;
        }
      }
      &:first-child {
        flex: 1;
        .block-item {
          width: 12px;
          height: 12px;
        }
      }
      &:nth-child(2) {
        .block-item {
          width: 6px;
          height: 6px;
        }
      }
      &:nth-child(3) {
        padding: 2px;
        .block-item {
          width: 5px;
          height: 5px;
        }
      }
    }
    .block-item {
      background: #afb5bf;
      border-radius: 1px 1px 1px 1px;
      margin: 0.5px 0.5px;
    }
  }
}
</style>
