<template>
  <div class="task-detail-le">
    <div class="title">基本信息</div>
    <div class="cont">
      <div class="text">
        <div class="lab">所属单位：</div>
        <div>{{ detailData.deptName || '--' }}</div>
      </div>
      <div class="text">
        <div class="lab">计划名称：</div>
        <div>{{ detailData.planName || '--' }}</div>
      </div>
      <div class="text">
        <div class="lab">起止日期：</div>
        <div>{{ detailData.planStartTime || '--' }}至{{ detailData.planEndTime || '--' }}</div>
      </div>
      <div class="text">
        <div class="lab">巡检频次：</div>
        <div>
          {{ frequency }}
        </div>
      </div>
      <div class="text">
        <div class="lab">巡检视频：</div>
        <div>
          <p v-for="rel in deviceAddress" :key="rel.id">{{ rel }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, Ref, ref, watch, inject } from 'vue';
import { DETAILS } from '../constant';
import { iFrequencyType } from '../../constant';

const props = defineProps({
  data: {
    type: Object,
  },
});
const detailData = computed(() => {
  return props.data ? props.data : { ...DETAILS };
});
const deviceAddress = computed(() => {
  let name = [];
  if (props.data?.deviceAddress) {
    name = props.data.deviceAddress.split(',');
  }
  return name;
});
const frequency = computed(() => {
  const fT = detailData.value?.planFrequencyUnit as number;
  const f = detailData.value?.planFrequency;
  return detailData.value?.planType === 1 ? '不重复' : `每${f || '--'}${iFrequencyType[fT] || '--'}一次`;
});

defineOptions({ name: 'VideoTaskDetailLe' });
</script>

<style lang="scss">
.task-detail-le {
  width: 434px;
  height: 100%;
  border-right: 1px solid #c8ced9;
  padding: 20px 24px;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 20px;
  overflow-y: auto;
  .title {
    font-weight: 700;
  }
  .text {
    margin-top: 16px;
    display: flex;
    .lab {
      flex-shrink: 0;
    }
    p {
      margin-bottom: 8px;
    }
  }
}
</style>
