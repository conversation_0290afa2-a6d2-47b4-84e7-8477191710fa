<template>
  <div class="list-cont">
    <n-grid :x-gap="0" :y-gap="0" :cols="props.cols" v-if="relVideos.length">
      <n-grid-item v-for="(rel, r) in relVideos" :key="rel.id">
        <div class="list-item">
          <div class="tips-le" v-if="detailData?.taskStatus !== 0 && rel.videoTime">{{ rel.videoTime }}</div>
          <div class="tips-ri" :style="{ background: statusFn(rel.videoResult).color }">
            {{ statusFn(rel.videoResult).lab }}
          </div>
          <div class="text" @click="toDetail(rel, r)">{{ rel.videoName || '--' }} {{ rel.videoAddress || '--' }}</div>
          <div class="cont" @click="toDetail(rel, r)">
            <img v-if="rel.videoUrl" :src="getVideoUrl(rel)" style="width: auto; height: 100%; margin: 0 auto" />
          </div>
        </div>
      </n-grid-item>
    </n-grid>
    <div v-else></div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { TASKACTION, DETAILS, TASK_XUNJIAN_STATE } from '../constant';
//videoResult  巡检视频结果 0：正常，1：待巡检，2：异常
//disposeStatus   巡检异常处置状态 1：解决，2：未解决

const emits = defineEmits(['action']);
const props = defineProps({
  cols: {
    type: Number,
    default: 1,
  },
  data: {
    type: Object,
  },
});
const detailData = computed(() => {
  return props.data ? { ...props.data } : { ...DETAILS };
});
const relVideos = computed(() => {
  return props.data?.relTaskVideos ? [...props.data?.relTaskVideos] : [];
});

function statusFn(s: number) {
  return {
    color: TASK_XUNJIAN_STATE.COLOR[s],
    lab: TASK_XUNJIAN_STATE.LAB[s],
  };
}
function getVideoUrl(value: any) {
  let curV = value.videoUrl.split(',');
  let url = value.videoSource === 1 ? window.$SYS_CFG.fileService + curV[0] : window.$SYS_CFG.apiBaseURL + curV[0];
  return url;
}

function toDetail(data: any, index: number) {
  if (detailData.value.taskStatus === 0) {
    return;
  }
  if (data.videoResult === 1 && detailData.value.taskStatus !== 3) {
    emits('action', { action: TASKACTION.STARTITEMINSPECTION, data: { insIndex: index } });
    return;
  }
  emits('action', { action: TASKACTION.DETAILS, data: { taskVideoItem: data } });
}

defineOptions({ name: 'VideoTaskDetailListCont' });
</script>

<style lang="scss">
.list-cont {
  padding: 0 16px;
  flex: 1;
  .list-item {
    height: 210px;
    border-radius: 8px 8px 8px 8px;
    margin: 8px;
    max-width: 500px;
    background: #c4c6cc url(../../assets/sp.png) no-repeat center center/ 62px auto;
    overflow: hidden;
    position: relative;
    .tips-le {
      height: 20px;
      background: rgba(0, 0, 0, 0.65);
      border-radius: 8px 0px 0px 0px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      position: absolute;
      left: 0;
      top: 0;
      padding: 0 5px;
    }
    .tips-ri {
      height: 20px;
      background: #00b578;
      border-radius: 0px 8px 0px 8px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      right: 0px;
      top: 0px;
      padding: 0 5px;
      position: absolute;
    }
    .text {
      height: 30px;
      background: rgba(0, 0, 0, 0.5);
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 30px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 0 16px;
      cursor: pointer;
    }
    .cont {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
}
</style>
