<template>
  <n-modal
    v-model:show="active"
    class="com-modal"
    preset="dialog"
    :close-on-esc="false"
    :mask-closable="false"
    :show-icon="false"
    :closable="false"
  >
    <template #header>
      <div class="com-modal-header">
        <img class="header-icon" src="./assets/edit-icon.png" alt="" />
        <span class="header-title">{{ videoAct?.videoAddress || '--' }}</span>
        <div class="btn-close" @click="close(1)">
          <IconClose class="icon" />
        </div>
      </div>
    </template>
    <div class="com-modal-cont">
      <div class="com-modal-video">
        <art-player v-if="playerUrl && active" :url="playerUrl" id="canvasVideo" :key="i"></art-player>
        <Empty title="设备未启动" v-else />
        <!-- <video
          :src="playerUrl"
          :autoplay="true"
          :controls="true"
          style="width: 696px; height: 430px"
          ref="canvasVideoRef"
          id="canvasVideo"
          crossorigin="anonymous"
        ></video> -->
      </div>
      <!-- crossorigin="anonymous" :src="playerUrl"-->
      <!-- <img v-if="canvasVideoImg" :src="canvasVideoImg" style="width: 200px; height: 200px" /> -->
      <div class="com-modal-ope">
        <n-button type="primary" color="#FA5151" class="btn" @click="reportFn(1)" :disabled="!playerUrl">
          上报异常
        </n-button>
        <n-button type="primary" @click="reportFn(2)" :disabled="!playerUrl"> 无异常 </n-button>
      </div>
    </div>
  </n-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { equiVideoApi } from '../../fetchData';
import { ITaskVideoDatil } from './type';
import { TASKACTION } from './constant';
// import html2canvas from 'html2canvas';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import Empty from '@/components/empty/index.vue';
import ArtPlayer from '@/components/player/artPlayer.vue';
import { sendGis } from '@/views/common/utils';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';

const [loading, run] = useAutoLoading(false);
const emits = defineEmits(['action']);
const active = ref(false);
const videoAct = ref<ITaskVideoDatil>(); //要巡检的视频对象

let i = ref<number>(0);
function open(data: ITaskVideoDatil) {
  console.log(data, '打开巡检上报弹框,接收要巡检的对象');
  active.value = true;
  videoAct.value = data;
  getEquiVideo(videoAct.value);
  sendGis(BRI_EVENT_TYPE.VIDEO_TASK_CHECK, { videoId: data.id, taskId: data.taskId });
}
function close(isHand: number) {
  active.value = false;
  if (isHand === 1) {
    emits('action', { action: TASKACTION.INSPECTIONHANDCLOSE, data: {} });
  } else {
    sendGis(BRI_EVENT_TYPE.VIDEO_TASK_DETAIL, { taskId: videoAct.value?.taskId });
  }
}
function reportFn(type: number) {
  //type 上报异常 1 无2
  console.log('视频巡检上报');
  takeScreenshot(type);
}

const playerUrl = ref('');
//查看视频详情
function getEquiVideo(data: ITaskVideoDatil) {
  equiVideoApi({
    deviceId: data.videoId,
    manufacturerCode: data.manufacturerCode,
    deviceNum: data.videoNum,
    accesstype: 1,
  }).then((res) => {
    if (res.data == 'error') {
      playerUrl.value = '';
      return;
    }
    playerUrl.value = res.data as string;
    i.value++;
  });
}
//视频截图 demo ceshi
const canvasVideoRef = ref();
const canvasVideoImg = ref();

//使用html2canvas
// function toImg() {
//   html2canvas(canvasVideoRef.value, {
//     dpi: 300, // 解决生产图片模糊
//     useCORS: true,
//     allowTaint: false,
//     logging: false,
//     scale: 2,
//     //windowHeight: 100,//Window.innerHeight
//   })
//     .then((canvas) => {
//       canvasVideoImg.value = canvas.toDataURL('image/png');
//       console.log(canvasVideoImg.value, '视频截图成功');
//     })
//     .catch(() => {
//       console.log(html2canvas, canvasVideoRef.value, '视频截图失败');
//     });
// }
//原生方法
const videoEl = ref();
function takeScreenshot(type: number) {
  videoEl.value = document.querySelector('.art-video');
  console.log('截图', canvasVideoRef.value, videoEl);
  // 获取 video 元素和 canvas 元素
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  // video?.setAttribute（'crossOrigin','anonymous'）
  if (context && videoEl) {
    // 设置 canvas 尺寸
    const { width, height } = videoEl.value.getBoundingClientRect();
    canvas.width = width;
    canvas.height = height;
    // 在 canvas 上绘制 video 的当前帧
    context.drawImage(videoEl.value, 0, 0, canvas.width, canvas.height);
    //转为base64
    canvasVideoImg.value = canvas.toDataURL('image/png');
    //转为blob
    canvas.toBlob(
      (blob) => {
        canvasVideoImg.value = blob;
        //blob.name = 'yy.jpg';
        console.log(canvasVideoImg.value, blob, '视频截图成功');
        uploadImg(blob, type);
      },
      'image/png',
      0.92
    );
  } else {
    emits('action', {
      action: TASKACTION.REPORT,
      data: { reportType: 1, videoItem: videoAct.value, videoScreenshotFile: null },
    });
  }
}
function uploadImg(file: any, type: number) {
  console.log(file, '截图成功去上传');
  emits('action', {
    action: TASKACTION.REPORT,
    data: { reportType: type, videoItem: videoAct.value, videoScreenshotFile: file },
  });
}

onMounted(() => {});

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskInspectionReport' });
</script>

<style lang="scss"></style>
