<template>
  <n-drawer
    v-model:show="active"
    to="#drawer-target"
    :close-on-esc="false"
    :mask-closable="false"
    :show-mask="false"
    :block-scroll="true"
    class="task-detail"
  >
    <n-drawer-content :body-content-style="{ padding: '0px' }">
      <com-loading v-if="loading"></com-loading>
      <div class="task-detail-wrap">
        <div class="task-detail-head">
          <div class="head-back" @click="close"><AkArrowBackThickFill /></div>
          <div class="head-title">{{ headTit }}</div>
          <div class="head-state" :style="{ backgroundColor: headState.color }">
            {{ headState.text }}
          </div>
        </div>
        <div class="task-detail-cont">
          <con-le :data="taskDetailData"></con-le>
          <div class="task-detail-ri">
            <list-action @action="actionFn" :data="taskDetailData"></list-action>
            <list-cont :cols="colsNum" :data="taskDetailData" @action="actionFn"></list-cont>
            <!-- <comPag @change="getDetailsList" :item-count="total" /> -->
          </div>
        </div>
      </div>
    </n-drawer-content>
    <!-- 状态详情 -->
    <status-detail ref="statusDetailRef" @action="actionFn" />
    <!-- 状态详情下的处理过程 -->
    <processes ref="statusProRef" />
    <!-- 巡检上报视屏弹框 -->
    <inspection ref="inspectRef" @action="actionFn" />
    <!-- 巡检上报 -->
    <inspection-report ref="inspReportRef" @action="actionFn" :unitId="taskDetailData.deptId" />
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { TASK_STATE, ACTION } from '../constant';
import { TASKACTION, DETAILS } from './constant';
import type { IActionData } from './type';
import type { IDetail } from '../type';
import ConLe from './comp/ContLe.vue';
import ListAction from './comp/ListAction.vue';
import ListCont from './comp/ListCont.vue';
// import ComPag from '@/components/pag/ComPag.vue';
import ComLoading from '@/components/loading/comLoading.vue';
import StatusDetail from './StatusDetail.vue';
import Processes from './Processes.vue';
import Inspection from './Inspection.vue';
import InspectionReport from './inspectionReport.vue';
import { AkArrowBackThickFill } from '@kalimahapps/vue-icons';
import { taskDetailApi, taskUpStatusApi, taskReportOkApi } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
// import { BridgeService } from '@/service/bridge/BridgeService';
// const bridge = new BridgeService('inspect', true);
import { api } from '@/api';
// import video from '@/api/video';
import { useRouter } from 'vue-router';
const router = useRouter();

import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis } from '@/views/common/utils';

const emits = defineEmits(['action']);
const [loading, run] = useAutoLoading(true);

const active = ref(false);
const taskAct = ref();
const colsNum = ref(3);
const taskDetailData = ref<IDetail>({ ...DETAILS });
// const total = ref(0);
const statusDetailRef = ref();
const statusProRef = ref();
const inspectRef = ref();
const inspReportRef = ref();
const videoInspIndex = ref<number>(0); //巡检的位置记录

function open(detailId: string, detailStatus: number) {
  console.log(detailId, detailStatus, '任务详情弹框');
  colsNum.value = 3;
  videoInspIndex.value = 0;
  taskAct.value = {
    id: detailId,
    status: detailStatus,
  };
  active.value = true;
  //todo
  taskDetailData.value = { ...DETAILS };
  changeUrl(detailId);
  getDetails(detailId);
}
function close() {
  active.value = false;
  emits('action', { action: ACTION.REFRESH, data: {} });
}
function changeUrl(s: string | number) {
  router.replace(`video?tab=task&taskId=${taskAct.value.id}&taskStatus=${s}`);
}

// bridge.onMessage('closePupop', (data: string) => {
//   active.value = false
// });

const headTit = computed(() => {
  return `${taskDetailData.value.taskPlanStartTime} ${taskDetailData.value.planName}` || '--';
});
const headState = computed(() => {
  return {
    color: TASK_STATE.COLOR[taskDetailData.value.taskStatus as number] || '',
    text: TASK_STATE.LAB[taskDetailData.value.taskStatus as number] || '--',
  };
});

const currentAction = ref<IActionData>({ action: TASKACTION.NONE, data: {} });

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList111-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    ...val.data,
  };
  switch (val.action) {
    case TASKACTION.LAYER:
      console.log('布局更改');
      colsNum.value = val.data.colsNum;
      break;
    case TASKACTION.DETAILS:
      console.log('状态详情');
      statusDetailRef.value.open(val.data.taskVideoItem);
      break;
    case TASKACTION.PROCESSES:
      console.log('状态详情下的处理过程');
      statusProRef.value.open(val.data.disposeId);
      break;
    case TASKACTION.STARTINSPECTION:
      console.log('开始巡检', val.data.taskStatus);
      taskUpStatus(val.data.taskStatus);
      break;
    case TASKACTION.STARTITEMINSPECTION:
      videoInspIndex.value = val.data.insIndex;
      console.log('单项开始巡检', val.data.insIndex, videoInspIndex.value);
      inspectRef.value.open(taskDetailData.value.relTaskVideos[val.data.insIndex]);
      break;
    case TASKACTION.REPORT:
      console.log(videoInspIndex.value, '视频巡检上报入口');
      //val.data.reportType 1 异常 2 无异常
      if (val.data.reportType === 1) {
        inspReportRef.value.open(val.data.videoItem, val.data.videoScreenshotFile);
      } else {
        toUpload(val.data.videoScreenshotFile);
        //走无异常接口
        sendGis(BRI_EVENT_TYPE.VIDEO_TASK_CHECK, {
          videoId: taskDetailData.value.relTaskVideos[videoInspIndex.value].id,
        });
      }
      break;
    case TASKACTION.REPORTSUCSSE:
      console.log(videoInspIndex.value, taskDetailData.value.relTaskVideos, '视频巡检上报成功');
      videoInspIndex.value += 1;
      if (videoInspIndex.value > taskDetailData.value.relTaskVideos.length - 1) {
        inspectRef.value.close(1);
        return;
      }
      console.log(videoInspIndex.value, taskDetailData.value.relTaskVideos[videoInspIndex.value], '-------');
      if (taskDetailData.value.relTaskVideos[videoInspIndex.value].videoResult != 1) {
        actionFn({ action: TASKACTION.REPORTSUCSSE, data: {} });
        return;
      }
      inspectRef.value.close(0);
      setTimeout(() => {
        inspectRef.value.open(taskDetailData.value.relTaskVideos[videoInspIndex.value]);
      }, 500);
      break;
    case TASKACTION.INSPECTIONHANDCLOSE:
      console.log('手动关闭视频上报');
      videoInspIndex.value = 0;
      getDetails(taskAct.value.id); //去更新
      sendGis(BRI_EVENT_TYPE.VIDEO_TASK_DETAIL, {
        taskId: taskAct.value.id,
      });
      break;
  }
}
function toUpload(videoScreenshotFile: any) {
  const actionURL = api.getUrl(api.type.intelligent, api.name.file.uploadFile);
  const formData = new FormData();
  formData.append('file', videoScreenshotFile);
  const xhr = new XMLHttpRequest();
  xhr.onload = () => {
    const imageUrl = JSON.parse(xhr.responseText).data; // 接口回调参数
    console.log('默认截图上传成功', imageUrl);
    taskReportOk(taskDetailData.value.relTaskVideos[videoInspIndex.value].id, imageUrl);
    loading.value = false;
  };
  xhr.open('POST', actionURL, true); // '/api/upload'是上传接口
  xhr.send(formData);
}
function getDetails(id: string | number) {
  run(taskDetailApi(id)).then((res) => {
    taskDetailData.value = res.data;
    changeUrl(taskDetailData.value.taskStatus);
  });
}
//开始巡检、结束 更新状态
function taskUpStatus(s: number | string) {
  //2 开启巡检状态 3结束
  let param = {
    id: taskDetailData.value.id,
    planStatus: s,
  };
  taskUpStatusApi(param).then((res) => {
    taskDetailData.value.taskStatus = s;
    if (s == 2 && taskDetailData.value.relTaskVideos.length) {
      inspectRef.value.open(taskDetailData.value.relTaskVideos[0]);
    }
    sendGis(BRI_EVENT_TYPE.VIDEO_TASK_STATUS, {
      taskStatus: s,
    });
    changeUrl(s);
  });
}
//无异常上报
function taskReportOk(taskVideoId: string, imgUrl: any) {
  run(taskReportOkApi(taskVideoId, { fileUrl: imgUrl })).then((res) => {
    actionFn({ action: TASKACTION.REPORTSUCSSE, data: {} });
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskDetail' });
</script>
<style lang="scss">
.task-detail {
  width: 100% !important;
  background: #eef7ff;
  border: 1px solid #ffffff;
  border-radius: 9px !important;
  .task-detail-wrap {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    min-width: 1358px;
  }
  .task-detail-head {
    height: 56px;
    background: #dce4f4;
    border-radius: 9px 9px 0px 0px;
    padding: 0 23px;
    display: flex;
    align-items: center;
    color: #242526;
    font-size: 16px;
    flex-shrink: 0;
    .head-back {
      cursor: pointer;
    }
    .head-title {
      margin-left: 16px;
      margin-right: 16px;
    }
    .head-state {
      padding: 0 4px;
      height: 20px;
      line-height: 20px;
      background: #527cff;
      border-radius: 3px 3px 3px 3px;
      font-weight: 400;
      font-size: 12px;
      color: #fff;
    }
  }
  .task-detail-cont {
    flex: 1;
    display: flex;
    overflow: auto;
    .task-detail-ri {
      flex: 1;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      min-width: 500px;
    }
  }
}
</style>
