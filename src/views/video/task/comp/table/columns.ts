import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'planName',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'deptName',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划开始日期',
    key: 'taskPlanStartTime',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '实际开始-结束时间',
    key: 'taskStartTime',
    width: 360,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return `${row.taskStartTime as string}~${row.taskEndTime as string}`;
    },
  },
  {
    title: '发现异常数量',
    key: 'abnormalNum',
    width: 120,
    ellipsis: {
      tooltip: true,
    },
  },
];
