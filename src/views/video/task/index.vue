<template>
  <div class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="props.treeList" ref="treeRef"></com-tree>
    </div>
    <div class="layer-ri">
      <div class="layer-strenth" v-if="isShowIcon" @click="layerShow"><img :src="strengthImage" /></div>
      <com-card :list="cardList" :act="taskStatus" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
    </div>
  </div>
  <task-detail ref="taskDetailRef" @action="actionFn"></task-detail>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch, onUnmounted, computed, nextTick } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import TaskDetail from './details/Task.vue';
import { ACTION, CARDLIST } from './constant';
import type { IActionData } from './type';
import ComCard from '@/components/card/ComCardA.vue';
import type { ICardAItem } from '@/components/card/type';
import { taskRecordApi } from './fetchData';
import strengthImage from '@/assets/strenth.png';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis, receiveGis } from '@/views/common/utils';
//offGis
import { storeToRefs } from 'pinia';
import { useStore } from '@/store';

import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
const isShowIcon = ref(true);
const emits = defineEmits(['changeBread']);
const props = withDefaults(defineProps<{ treeList: TreeOption[] }>(), {
  treeList: () => [],
});

const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const taskDetailRef = ref();
const tableCompRef = ref();
//全部：-1   0待开始，1：未执行/逾期，2：进行中，3，已完成
const taskStatus = ref(3);
const cardList = ref<ICardAItem[]>([...CARDLIST]);

const store = useStore();
const { treeAct } = storeToRefs(store);
watch(
  () => treeAct.value,
  (vN, vO) => {
    console.log('有树值了', vN, vO);
    if (vN) {
      sendGis(BRI_EVENT_TYPE.VIDEO_TASK);
      actionFn({ action: ACTION.TREECHANGE, data: {} });
    }
  },
  {
    immediate: true,
  }
);
const treeRef = ref();
receiveGis((res) => {
  console.warn('Received app_onEhsInsMgr GISmsg 任务:', res);
  //组织树更改
  if (res.type === BRI_EVENT_TYPE.TREE_CHANGE) {
    if (res.treeActData.id === treeAct.value?.id) {
      //同一个树组织
      return;
    }
    //更新对应的点 todo
    treeRef.value.updateTree(res.treeActData);
  }
  // 去一张图
  if (res.type === BRI_EVENT_TYPE.DRAWING && res.data.isMap) {
    router.push('/drawing');
  }
});

//统计
function taskListRecord(id?: string) {
  const params = {
    deptId: id || '',
  };
  taskRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.allNum;
    cardList.value[1].value = res.data.useNum;
    cardList.value[2].value = res.data.waitOpenNum;
    cardList.value[3].value = res.data.finishedNum;
    cardList.value[4].value = res.data.overdueNum;
  });
}

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    taskStatus: taskStatus.value,
    deptId: treeAct.value?.id || '',
    deptName: treeAct.value?.text || '',
    levelCode: treeAct.value?.levelCode || '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      if (route.query.taskId) {
        actionFn({
          action: ACTION.DETAILS,
          data: { idD: route.query.taskId, taskStutasD: route.query.taskStatus },
        });
      }
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      taskListRecord(treeAct.value?.id || '');
      // 通知gis 树结构改变了
      sendGis(BRI_EVENT_TYPE.TREE_CHANGE);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      isShowIcon.value = false;
      layerVisible.value = true;
      taskDetailRef.value?.open(val.data.idD, val.data.taskStutasD);
      // 通知gis
      sendGis(BRI_EVENT_TYPE.VIDEO_TASK_DETAIL, {
        taskId: val.data.idD,
      });
      emits('changeBread', { text: '任务详情', isAdd: true });
      break;
    case ACTION.EXPORT:
      console.log('导出');
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片切换筛选');
      taskStatus.value = val.data.cardId;
      currentAction.value.data.taskStatus = val.data.cardId;
      handleSearch(currentAction.value.data);
      break;
    case ACTION.REFRESH:
      isShowIcon.value = true;
      layerVisible.value = false;
      console.log(currentAction.value.data, '刷新当前页面');
      // 通知gis - 返回
      sendGis(BRI_EVENT_TYPE.VIDEO_TASK);
      emits('changeBread', { text: '任务详情', isAdd: false });
      handleSearch(currentAction.value.data);
      taskListRecord(treeAct.value?.id || '');
      router.replace(`video?tab=task`);
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  nextTick(() => {
    if (data) {
      tableCompRef.value?.getTableDataWrap(data);
    } else {
      tableCompRef.value?.getTableData();
    }
  });
}

onUnmounted(() => {
  //BridgeRemoteService.getIns().offMessage(EVENT_TYPE.MESSAGE);
  //offGis();
});

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss"></style>
