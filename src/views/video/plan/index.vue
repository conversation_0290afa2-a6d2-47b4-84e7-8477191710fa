<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="props.treeList" ref="treeRef"></com-tree>
    </div>
    <div class="layer-ri" v-if="treeAct">
      <div class="layer-strenth" @click="layerShow"><img :src="strengthImage" /></div>
      <com-card :list="cardList" :act="cardStatus" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
    </div>
    <detail @action="actionFn" ref="detailRef" />
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch, toRaw, nextTick } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, CARDLIST } from './constant';
import type { IActionData } from './type';
import ComCard from '@/components/card/ComCardA.vue';
import Detail from './comp/detail/Detail.vue';
import type { ICardAItem } from '@/components/card/type';
import { planListRecordApi } from './fetchData';
import { getSelectValue } from '@/utils/tree';
// import { BridgeRemoteService } from '@/service/bridge/BridgeRemoteService.ts';
// import { BRI_EVENT_TYPE, EVENT_TYPE } from '@/service/bridge/type.ts';

import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { sendGis, receiveGis } from '@/views/common/utils';
import { storeToRefs } from 'pinia';
import { useStore } from '@/store';
import { useRoute, useRouter } from 'vue-router';
import strengthImage from '@/assets/strenth.png';
const router = useRouter();

const props = withDefaults(defineProps<{ treeList: TreeOption[] }>(), {
  treeList: () => [],
});
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });
const tableCompRef = ref();
const detailRef = ref();
//状态 0：待启用，1：开启，2：关闭，3: 已过期
const cardStatus = ref(1);
const cardList = ref<ICardAItem[]>([...CARDLIST]);

const store = useStore();
const { treeAct } = storeToRefs(store);
watch(
  () => treeAct.value,
  (vN, vO) => {
    console.log('有树值了', vN, vO);
    if (vN) {
      sendGis(BRI_EVENT_TYPE.VIDEO_PLAN);
      actionFn({ action: ACTION.TREECHANGE, data: {} });
    }
  },
  {
    immediate: true,
  }
);
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
const treeRef = ref();
receiveGis((res) => {
  console.warn('Received app_onEhsInsMgr GISmsg 计划:', res);
  //组织树更改
  if (res.type === BRI_EVENT_TYPE.TREE_CHANGE) {
    if (res.treeActData.id === treeAct.value?.id) {
      //同一个树组织
      return;
    }
    //更新对应的点 todo
    treeRef.value.updateTree(res.treeActData);
  }
  // 去一张图
  if (res.type === BRI_EVENT_TYPE.DRAWING && res.data.isMap) {
    router.push('/drawing');
  }
});

//统计
function listRecord(id?: string) {
  const params = {
    deptId: id || '',
  };
  planListRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.allNum;
    cardList.value[1].value = res.data.useNum;
    cardList.value[2].value = res.data.waitOpenNum;
    cardList.value[3].value = res.data.stopNum;
    cardList.value[4].value = res.data.overdueNum;
  });
}

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    deptName: treeAct.value?.text ? treeAct.value.text : '',
    treeName: treeAct.value?.treeName ? treeAct.value.treeName : '',
    planStatus: cardStatus.value,
    levelCode: treeAct.value?.levelCode || '',
    ...val.data,
  };
  let curData = currentAction.value.data;
  let curTreeData = {
    id: curData.id,
    deptId: curData.deptId,
    deptName: curData.deptName,
    treeName: curData.treeName,
    deptIds: treeAct.value.deptIds || '',
    unitIds: treeAct.value.unitIds || '',
    treeData: toRaw(props.treeList),
    detail: curData.detail,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择', treeAct.value);
      handleSearch(currentAction.value.data);
      listRecord(currentAction.value.data.deptId);
      // 通知gis 树结构改变了
      sendGis(BRI_EVENT_TYPE.TREE_CHANGE);
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片筛选');
      cardStatus.value = currentAction.value.data.cardId;
      currentAction.value.data.planStatus = currentAction.value.data.cardId;
      handleSearch(currentAction.value.data);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      detailRef.value.open({ type: 3, ...curTreeData });
      break;
    case ACTION.EDIT:
      console.log('编辑');
      detailRef.value.open({ type: 2, ...curTreeData });
      break;
    case ACTION.ADD:
      console.log('添加');
      // let curData = currentAction.value.data;
      detailRef.value.open({ type: 1, ...curTreeData });
      // // gis通信-计划新增
      // BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, { type: BRI_EVENT_TYPE.ADD_PLAN });
      break;
    case ACTION.PLANLISTTREECHANGE:
      console.log('gis中改变了列表');
      treeRef.value.updateTree(curData.planListTree);
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  nextTick(() => {
    if (data) {
      tableCompRef.value?.getTableDataWrap(data);
    } else {
      tableCompRef.value?.getTableData();
    }
  });
}
async function handleUpdateValue() {
  let selectData = await getSelectValue({ tree: props.treeList, selectParantCode: treeAct.value.levelCode as string });
  let curIds: string[] = [];
  selectData.forEach((item) => {
    curIds.push(item.id);
  });
  treeAct.value.deptIds = curIds.join(',');
}

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
