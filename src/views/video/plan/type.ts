import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export type IPageDataRes = IPageRes<IDetail>;

export type IRelVideos = {
  deviceId: string;
  deviceName: string;
  deviceSort: 0;
  id: string;
  planId: string;
};
export interface IDetail {
  createTime: string;
  createdBy: string;
  delFlag: 0;
  deptId: string;
  deptIds: string;
  deptName: string;
  id: string;
  planCategory: string;
  planCategoryName: string;
  planEndDate: string;
  planFrequency: number;
  planFrequencyUnit: number;
  planName: string;
  planStartDate: string;
  planStatus: number;
  planType: string;
  relVideos: IRelVideos[];
  updateTime: string;
  updatedBy: string;
  updatedByName: string;
  zhId: string;
}
//统计
export interface IPlanNum {
  allNum: number;
  overdueNum: number;
  stopNum: number;
  useNum: number;
  waitOpenNum: number;
}

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export interface IDevice {
  deviceId: string;
  deviceName: string;
  deviceSort: string;
  manufacturerCode: string;
  deviceNum: string;
}
export interface ISubmit {
  // createdBy: string;
  // delFlag: number; //逻辑删除标志1-是，0-否
  // planCategory: string; //计划类型
  // planCategoryName: string; //计划类型名称
  //planStatus: number; //状态 0：待启用，1：开启，2：关闭，9: 已过期
  deptId: string | number | null;
  deptIds: string | number | null;
  deptName: string | number | null;
  id?: string | number | null;
  planStartDate: number | string | null;
  planEndDate: number | string | null;
  planFrequency: string | number | null; //计划巡检频次
  planFrequencyUnit: number | string | null; //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
  planName: string | null;
  planType: number | string; //计划类型 0：重复 1：不重复
  relVideos: any[];
}
