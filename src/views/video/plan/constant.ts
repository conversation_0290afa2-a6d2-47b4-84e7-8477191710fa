import type { ICardAItem } from '@/components/card/type';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EDIT = 'EDIT',
  MORE = 'MORE',
  ADD = 'ADD',
  DEL = 'DEL',
  USING = 'USING',
  STOPUSING = 'STOPUSING',
  TREECHANGE = 'TREECHANGE',
  CARDCHANGE = 'CARDCHANGE',
  PLANLISTTREECHANGE = 'PLANLISTTREECHANGE',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAILS]: '详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.MORE]: '更多',
  [ACTION.ADD]: '新建计划',
  [ACTION.DEL]: '删除',
  [ACTION.USING]: '启用',
  [ACTION.STOPUSING]: '停用',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片筛选',
  [ACTION.PLANLISTTREECHANGE]: 'gis在计划列表切换树结构',
};
//计划状态 0：待启用，1：开启/使用中， 2：关闭/停用  3：过期
export const PLAN_STATE = {
  LAB: ['待启用', '使用中', '已停用', '已过期'],
  COLOR: ['#F39600', '#527CFF', '#FA5151', '#B2B2B2'],
};

//计划状态状态 0：待启用，1：开启，2：关闭，3: 已过期 状态统计
export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: -1,
  },
  {
    label: '使用中（个）',
    value: 0,
    id: 1,
  },
  {
    label: '待启用（个）',
    value: 0,
    id: 0,
  },
  {
    label: '已停用（个）',
    value: 0,
    id: 2,
  },
  {
    label: '已过期（个）',
    value: 0,
    id: 3,
  },
];

//计划频率选项
export const PLANFREQUENCYUNITOPT = [
  {
    label: '小时',
    value: 1,
  },
  {
    label: '天',
    value: 2,
  },
  {
    label: '周',
    value: 3,
  },
  {
    label: '月',
    value: 4,
  },
  {
    label: '季度',
    value: 5,
  },
  {
    label: '年',
    value: 6,
  },
];

//计划编辑 表单验证
export const RULES = {
  deptId: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择所属单位',
  },
  planName: {
    required: true,
    trigger: ['blur'],
    message: '请输入计划名称',
  },
  planStartDate: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择起止时间',
  },
  planType: {
    type: 'number',
    required: true,
    trigger: 'change',
    message: '请选择是否重复',
  }, //计划类型 0：重复 1：不重复
  planFrequency: {
    type: 'number',
    required: true,
    trigger: 'change',
    message: '请输入巡检频次',
  }, //
  relVideos: {
    type: 'array',
    required: true,
    trigger: 'change',
    message: '请选择巡检视频',
  },
};
//频次
export const iFrequencyType = ['', '小时', '日', '周', '月', '季度', '年'];
