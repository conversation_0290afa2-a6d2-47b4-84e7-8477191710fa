import type { IPageDataRes, IDetail, IPlanNum, ISubmit } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//列表
export function planListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planList, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}
//统计
export function planListRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planRecord, query);
  return $http.get<IPlanNum>(url, { data: { _cfg: { showTip: true } } });
}
//详情
export function planDetailApi(id: string | number, query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.video.planEdit, query, id);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
//保存新建
export function planAddApi(data: ISubmit) {
  const param = api.getComParams(api.type.intelligent, api.video.planEdit, data);
  return $http.post(param.url, { data: { _cfg: { showTip: true, showOkTip: true }, ...param.data } });
}

//编辑
export function planUpdateApi(data: ISubmit) {
  const param = api.getComParams(api.type.intelligent, api.video.planEdit, data);
  return $http.put(param.url, { data: { _cfg: { showTip: true, showOkTip: true }, ...param.data } });
}
//删除
export function planDelApi(id: string) {
  const param = api.getComParams(api.type.intelligent, api.video.planEdit, `delete/${id}`);
  return $http.post(param.url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}
//启用、停用
export function planUpStatusApi(data: { id: string; planStatus: number }) {
  const param = api.getComParams(api.type.intelligent, api.video.planUpstatus, data);
  return $http.put(param.url, { data: { _cfg: { showTip: true, showOkTip: true }, ...param.data } });
}
