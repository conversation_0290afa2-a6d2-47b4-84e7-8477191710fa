import { DataTableColumn } from 'naive-ui';
import { ymrDateFn } from '@/views/common/utils.ts';
import { iFrequencyType } from '../../constant';
export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'planName',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'deptName',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '开始日期-结束日期',
    key: 'planStartDate',
    width: 280,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return `${ymrDateFn(row.planStartDate as string)} ~ ${ymrDateFn(row.planEndDate as string)}`;
    },
  },
  {
    title: '巡检频次',
    key: 'planFrequency',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      const curPro = row.planFrequencyUnit as number;
      return row.planType === 1 ? '不重复' : `每${row.planFrequency as string}${iFrequencyType[curPro] as string}一次`;
    },
  },
];
