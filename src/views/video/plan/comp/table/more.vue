<template>
  <n-dropdown placement="bottom-start" trigger="click" size="small" :options="options" @select="handleSelect">
    <n-button icon-placement="right" size="small" color="rgba(82,124,255,0.1)" class="com-action-button1">
      <template #icon>
        <CdChevronRight />
      </template>
      {{ ACTION_LABEL.MORE }}
    </n-button>
  </n-dropdown>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue';
import { computed } from 'vue';
import { CdChevronRight } from '@kalimahapps/vue-icons';
import { ACTION, ACTION_LABEL, PROVIDE_KEY } from '../../constant';
const emits = defineEmits(['select']);
interface Props {
  state: number; //状态 0：待启用，1：开启， 2：关闭  3：过期
}
const props = defineProps<Props>();
const options = computed(() => {
  //const state = currentAction.value.data.areaCode;
  let opt = [
    {
      label: ACTION_LABEL.STOPUSING,
      key: ACTION.STOPUSING,
      disabled: [0, 2, 3].includes(props.state),
    },
    {
      label: ACTION_LABEL.USING,
      key: ACTION.USING,
      disabled: [1, 3].includes(props.state),
    },
    {
      label: ACTION_LABEL.DEL,
      key: ACTION.DEL,
      disabled: [1].includes(props.state),
    },
  ];
  return opt;
});
function handleSelect(key: string) {
  console.log(key, 'more-sel------');
  emits('select', key);
}

defineComponent({ name: 'PlanTabMore' });
</script>

<style module lang="scss"></style>
