<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="flexHeight"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
    :scroll-x="1345"
  />
</template>

<script lang="ts" setup>
import type { IDetail } from '../../type';
import { ACTION, ACTION_LABEL, PLAN_STATE } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, toRaw, VNode, computed } from 'vue';
import { planListApi, planDelApi, planUpStatusApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import tabMore from './more.vue';
import { useActionComfirm } from '@/common/hooks/comfirm.ts';
// import { $toast } from '@/common/shareContext/useToastCtx';
import { paramsFn, ymrDateFn } from '@/views/common/utils.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
const flexHeight = computed(() => {
  return tableData.value.length === 0 ? false : true;
});

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  search(planListApi(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  loading.value = true;
  filterData =
    Object.assign(
      {},
      paramsFn(data, ['pageNo', 'pageSize', 'planStatus', 'deptId', 'deptName', 'planName', 'planType', 'levelCode'])
    ) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  //状态 0：待启用，1：开启/使用中， 2：关闭/停用  3：过期
  columns.value.push({
    title: '计划状态',
    key: 'state',
    align: 'left',
    width: 120,
    render(row) {
      const index = row.planStatus as number;
      return h(
        NButton,
        {
          size: 'small',
          color: PLAN_STATE.COLOR[index],
        },
        PLAN_STATE.LAB[index]
      );
    },
  });
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 280,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [];
  acList.push(
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () =>
            emits('action', {
              action: ACTION.DETAILS,
              data: { detail: toRaw(row) },
            }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          disabled: [1, 3].includes(row.planStatus),
          class: 'com-action-button1',
          onClick: () =>
            emits('action', {
              action: ACTION.EDIT,
              data: { detail: toRaw(row) },
            }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          disabled: [1].includes(row.planStatus),
          class: 'com-action-button1',
          onClick: () => opeMore(ACTION.DEL, { detail: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DEL }
      ),
    ]
  );
  if (row.planStatus == '0' || row.planStatus == '2') {
    acList.push([
      h(
        NButton,
        {
          color: '#527CFF',
          ghost: true,
          style: { '--n-color': 'rgba(82,124,255,0.1)' },
          size: 'small',
          // class: 'com-action-button1',
          disabled: [3].includes(row.planStatus),
          onClick: () => opeMore(ACTION.USING, { detail: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.USING }
      ),
    ]);
  } else {
    acList.push([
      h(
        NButton,
        {
          color: '#FA5151',
          ghost: true,
          style: { '--n-color': 'rgba(250, 81, 81, 0.1)' },
          // color: 'rgba(82,124,255,0.1)',
          size: 'small',
          // class: 'com-action-button1',
          disabled: [3].includes(row.planStatus),
          onClick: () => opeMore(ACTION.STOPUSING, { detail: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.STOPUSING }
      ),
    ]);
  }

  // [
  //   h(tabMore, {
  //     state: row.planStatus,
  //     onSelect: (key: string) => opeMore(key, { detail: toRaw(row) }),
  //   }),
  // ],
  return acList;
}

function opeMore(action: string, row: any) {
  //ACTION.DEL
  switch (action) {
    case ACTION.DEL:
      console.log('删除');
      useActionComfirm({ msg: '确定删除吗？', cannelText: '取消删除' }).then(() => {
        planDel(row.detail.id);
      });
      break;
    case ACTION.USING:
      console.log('启用');
      useActionComfirm({ msg: '确定启用吗？', cannelText: '取消启用' }).then(() => {
        planUpStatus(row.detail.id, 1);
      });
      break;
    case ACTION.STOPUSING:
      console.log('停用');
      useActionComfirm({ msg: '确定停用吗？', cannelText: '取消停用' }).then(() => {
        planUpStatus(row.detail.id, 2);
      });

      break;
  }
}
//删除
function planDel(id: string) {
  planDelApi(id)
    .then((res) => {
      emits('action', { action: ACTION.TREECHANGE, data: {} });
    })
    .finally(() => {});
}
//启用、停用
function planUpStatus(id: string, planStatus: number) {
  planUpStatusApi({ id, planStatus })
    .then((res) => {
      emits('action', { action: ACTION.TREECHANGE, data: {} });
    })
    .finally(() => {});
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
