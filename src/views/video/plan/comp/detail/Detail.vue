<template>
  <!-- 状态结果详情 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <com-loading v-if="loading"></com-loading>
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="../../../assets/edit-icon.png" alt="" />
          <span class="header-title">{{
            actData.type === 1 ? '新建计划' : actData.type === 2 ? '编辑计划' : '计划详情'
          }}</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :size="size"
          :style="{
            maxWidth: '470px',
          }"
        >
          <n-form-item label="所属单位" path="deptId">
            <n-tree-select
              placeholder="请选择所属单位"
              key-field="id"
              label-field="text"
              v-model:value="formModel.deptId"
              :options="deptOptions"
              :disabled="actData.type === 3"
              @update:value="handleUpdateValue"
              :render-switcher-icon="renderLabelIcon"
            />
          </n-form-item>
          <n-form-item label="计划名称" path="planName">
            <n-input
              v-model:value="formModel.planName"
              placeholder="请输入计划名称"
              :disabled="actData.type === 3"
              maxlength="50"
            />
          </n-form-item>
          <n-form-item label="起止时间" path="planStartDate">
            <n-date-picker
              v-model:value="rangeDate"
              type="daterange"
              clearable
              @update:value="dateRangeChange"
              :disabled="actData.type === 3"
              :is-date-disabled="disablePreviousDate"
            />
          </n-form-item>
          <n-space style="display: flex; justify-content: space-between">
            <n-form-item label="是否重复" path="planType">
              <n-radio-group
                v-model:value="formModel.planType"
                name="planTypeRadiogroup"
                :disabled="actData.type === 3"
              >
                <n-space>
                  <n-radio :value="1"> 不重复 </n-radio>
                  <n-radio :value="0"> 重复</n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item path="planFrequency" v-if="formModel.planType === 0">
              <n-input-group>
                <n-input-group-label>每</n-input-group-label>
                <n-input-number
                  :style="{ width: '80px' }"
                  v-model:value="formModel.planFrequency"
                  :show-button="false"
                  :disabled="actData.type === 3"
                  :min="1"
                  :max="9999"
                  :precision="0"
                />
                <n-select
                  :style="{ width: '80px' }"
                  v-model:value="formModel.planFrequencyUnit"
                  :options="planFrequencyUnitOpt"
                  :disabled="actData.type === 3"
                />
              </n-input-group>
            </n-form-item>
          </n-space>
          <n-form-item label="巡检视频" path="relVideos">
            <n-space vertical>
              <p style="margin-top: 6px">已选择{{ formModel.relVideos.length }}个</p>
              <n-checkbox-group v-model:value="formModel.relVideos" name="listCheckbox" @update:value="relVideosChange">
                <n-space vertical>
                  <n-checkbox
                    v-for="divce in divceOpt"
                    :value="divce.deviceId"
                    :key="divce.deviceId"
                    :disabled="divce.onlineState !== '0' || actData.type === 3"
                  >
                    {{ divce.deviceAddress }}
                  </n-checkbox>
                </n-space>
              </n-checkbox-group>
            </n-space>
          </n-form-item>
          <n-form-item label="最近编辑人员" v-if="actData.type === 3">{{
            detailData?.updatedByName || '--'
          }}</n-form-item>
          <n-form-item label="最近编辑时间" v-if="actData.type === 3">{{ detailData?.updateTime || '--' }}</n-form-item>
        </n-form>
      </div>
      <template #footer v-if="actData.type !== 3">
        <div class="com-detail-drawer-footer">
          <n-button type="tertiary" @click="close"> 取消 </n-button>
          <n-button
            type="primary"
            @click="handleValidateButtonClick"
            :disabled="actData.type === 3"
            :loading="loadingSubmit"
            >确定
          </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { ref, watch, h, toRaw } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { planDetailApi, planAddApi, planUpdateApi } from '../../fetchData';
import { deviceListApi } from '../../../fetchData';
import { IDeviceItem } from '../../../type';
import type { IDetail, ISubmit } from '../../type';
import type { FormInst, CascaderOption } from 'naive-ui';
import ComLoading from '@/components/loading/comLoading.vue';
import { ACTION, PLANFREQUENCYUNITOPT, RULES } from '../../constant';
// import { useYMDRange } from '@/common/hooks/useDate';
import { dayjs } from '@/utils/dayjs';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';

import { AnFilledCaretRight } from '@kalimahapps/vue-icons';

import { sendGis, receiveGis } from '@/views/common/utils';
import { BRI_EVENT_TYPE } from '@/service/bridge/type.ts';
import { useStore } from '@/store';

import { useRoute, useRouter } from 'vue-router';
const router = useRouter();

const [loading, run] = useAutoLoading(true);
const loadingSubmit = ref(false);
const emits = defineEmits(['action']);
function renderLabelIcon(info: any) {
  if (info.option.hasChildren) {
    return h(AnFilledCaretRight);
  }
  return h('span');
}
const active = ref(false);

interface ActData {
  type: number;
  deptId: number | string;
  deptIds: number | string;
  treeName: string;
  unitIds?: string;
  treeData?: any;
  id: string | number;
  detail?: any;
}
const actData = ref<ActData>({
  type: 1, //1 add 2 edit 3 detail
  deptIds: '',
  treeName: '',
  deptId: '',
  id: '',
});
const detailData = ref<IDetail>();
const defaultForm = {
  deptIds: null,
  deptId: null,
  deptName: '',
  planStartDate: null,
  planEndDate: null,
  planFrequency: 1, //计划巡检频次
  planFrequencyUnit: 2, //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
  planName: '',
  planType: 0, //计划类型 0：重复 1：不重复
  relVideos: [],
};
const rules = ref<any>();
function open(data: ActData) {
  console.log(data, '打开计划详情弹框');
  actData.value = data;
  active.value = true;
  deptOptions.value = data.treeData || [];
  rules.value = { ...RULES };
  let curDeptId = actData.value.detail?.deptId || '';
  //let curGisType = BRI_EVENT_TYPE.VIDEO_PLAN_ADD;
  if (actData.value.type === 1) {
    formModel.value.deptId = data.deptId;
    formModel.value.deptIds = data.deptIds;
    formModel.value.deptName = data.treeName;
    curDeptId = actData.value.deptId;
  } else if (actData.value.type === 3) {
    rules.value = null;
    //curGisType = BRI_EVENT_TYPE.VIDEO_PLAN_DETAIL;
  }
  getDeviceList(curDeptId as string);

  //sendGis(curGisType, { data: { selVideo: [] } });
}
function close() {
  active.value = false;
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
  formModel.value = { ...defaultForm };
  rangeDate.value = null;
  //返回计划列表
  sendGis(BRI_EVENT_TYPE.VIDEO_PLAN);
}

const planFrequencyUnitOpt = ref(PLANFREQUENCYUNITOPT);
const divceOpt = ref<IDeviceItem[]>([]);
const rangeDate = ref<any[] | null>();
const deptOptions = ref([]);
const formRef = ref<FormInst | null>(null);
const size = ref('medium');
const formModel = ref<ISubmit>({ ...defaultForm });

function dateRangeChange(value: number[]) {
  if (value && value.length === 2) {
    formModel.value.planStartDate = dayjs(value[0]).format('YYYY-MM-DD 00:00:00');
    formModel.value.planEndDate = dayjs(value[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    formModel.value.planStartDate = '';
    formModel.value.planEndDate = '';
  }
}
function disablePreviousDate(ts: number) {
  const date = new Date();
  date.setDate(date.getDate() - 1);
  return ts < Date.parse(date.toString());
}

function getDeviceList(deptId: string, nD?: boolean, treeData?: any) {
  loading.value = true;
  //'540102DZDA202206010001' ownerIds: owner,
  deviceListApi({ deptId: deptId })
    .then((res) => {
      divceOpt.value = res.data;
    })
    .finally(() => {
      formModel.value.relVideos = [];
      if (actData.value.type !== 1 && !nD) {
        getDetails(actData.value.detail.id);
        return;
      }
      const { treeAct } = useStore();
      curPlanTree.value = {
        id: deptId,
        deptIds: treeAct?.deptIds ?? '',
        text: treeAct?.text ?? '',
        treeName: treeAct?.treeName ?? '',
        unitId: treeAct?.unitId ?? '',
        unitIds: treeAct?.unitIds ?? '',
      };
      if (nD) {
        curPlanTree.value = treeData;
      }
      let type = nD ? BRI_EVENT_TYPE.VIDEO_PLAN_TREE_CHANGE : BRI_EVENT_TYPE.VIDEO_PLAN_ADD;
      loading.value = false;
      try {
        sendGis(type, {
          planTree: toRaw(curPlanTree.value),
          selVideo: '',
        });
      } catch (error) {
        console.log('error = ', error);
      }
    });
}
const curPlanTree = ref();
function handleUpdateValue(value: string, option: CascaderOption) {
  console.log('设备信息----', option);
  formModel.value.deptId = option.id as string;
  formModel.value.deptName = option.text as string;
  formModel.value.deptIds = option.deptIds as string;
  //option.unitIds as string
  curPlanTree.value = {
    id: option.id,
    deptIds: option.deptIds,
    text: option.text,
    treeName: option.treeName ?? '',
    unitId: option.unitId ?? '',
    unitIds: option.unitIds ?? '',
  };
  getDeviceList(option.id as string, true, curPlanTree.value);
}

function handleValidateButtonClick(e: MouseEvent) {
  e.preventDefault();
  loadingSubmit.value = true;
  formRef.value?.validate((errors) => {
    if (!errors) {
      console.log(rangeDate.value, 'rangeDate.value-----');
      let formParam = { ...formModel.value };
      let curDivce: any = [];
      formModel.value.relVideos?.forEach((item, i) => {
        let curItem = divceOpt.value.filter((divc) => divc.deviceId === item);
        if (curItem.length) {
          curDivce.push({
            deviceId: curItem[0].deviceId,
            deviceName: curItem[0].deviceName,
            deviceSort: i,
            deviceNum: curItem[0].deviceNum,
            loopDeviceNum: curItem[0].loopDeviceNum,
            manufacturerCode: curItem[0].manufacturerCode,
          });
        }
      });
      formParam.relVideos = curDivce;
      actData.value.type === 1 ? planAdd(formParam) : planEdit(formParam);
    } else {
      console.log(errors);
      loadingSubmit.value = false;
    }
  });
}

//添加
function planAdd(data: ISubmit) {
  console.log('添加');
  planAddApi(data)
    .then((res) => {
      close();
      emits('action', {
        action: ACTION.TREECHANGE,
        data: {},
      });
    })
    .finally(() => {
      loadingSubmit.value = false;
    });
}
//编辑
function planEdit(data: ISubmit) {
  console.log('编辑');
  planUpdateApi(data)
    .then((res) => {
      close();
      emits('action', {
        action: ACTION.TREECHANGE,
        data: {},
      });
    })
    .finally(() => {
      loadingSubmit.value = false;
    });
}

function toPro(v: number | string) {
  console.log('查看位置');
}

function getDetails(id: string | number) {
  run(planDetailApi(id)).then((res) => {
    if (!res.data) {
      return;
    }
    detailData.value = res.data;
    formModel.value = {
      deptIds: res.data.deptIds,
      deptId: res.data.deptId,
      deptName: res.data.deptName,
      planStartDate: res.data.planStartDate,
      planEndDate: res.data.planEndDate,
      planFrequency: res.data.planFrequency, //计划巡检频次
      planFrequencyUnit: res.data.planFrequencyUnit, //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
      planName: res.data.planName,
      planType: res.data.planType, //计划类型 0：重复 1：不重复
      relVideos: [],
      id: res.data.id,
    };
    rangeDate.value = [new Date(res.data.planStartDate).getTime(), new Date(res.data.planEndDate).getTime()];
    if (res.data.relVideos) {
      res.data.relVideos.forEach((item) => {
        formModel.value.relVideos.push(item.deviceId);
      });
    }
    let curGisType = actData.value.type === 3 ? BRI_EVENT_TYPE.VIDEO_PLAN_DETAIL : BRI_EVENT_TYPE.VIDEO_PLAN_ADD;
    //走详情查询一下relvideos
    const { treeAct } = useStore();
    sendGis(curGisType, {
      planTree: {
        id: res.data.deptId,
        deptIds: treeAct?.deptIds ?? '',
        text: treeAct?.text ?? '',
        treeName: treeAct?.treeName ?? '',
        unitId: treeAct?.unitId ?? '',
        unitIds: treeAct?.unitIds ?? '',
      },
      selVideo: JSON.stringify(formModel.value.relVideos),
    });
  });
}

// watch(
//   () => active.value,
//   (val: boolean) => {
//     if (!val && actData.value.type === 1) {
//       // gis通信-计划新增取消
//       //BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, { type: BRI_EVENT_TYPE.ADD_PLAN_CANCEL });
//     }
//   }
// );
// // gis通信
// // BridgeRemoteService.getIns().onMessage(EVENT_TYPE.MESSAGE, (data) => {
// //   if (data.type === BRI_EVENT_TYPE.ADD_PLAN) {
// //     console.log('ADD_PLAN', data.data.selectedData);
// //     formModel.value.relVideos = data.data.selectedData;
// //   }
// // });
function relVideosChange(value: any) {
  if (isGisMsg.value) {
    isGisMsg.value = false;
    return;
  }
  sendGis(BRI_EVENT_TYPE.VIDEO_PLAN_ADD_LOCATION, {
    planTree: toRaw(curPlanTree.value),
    selVideo: JSON.stringify(value),
  });
}

const isGisMsg = ref(false);
receiveGis((res) => {
  console.warn('Received app_onEhsInsMgr GISmsg 计划详情:', res);
  const { treeAct } = useStore();
  //组织树更改
  if (res.type === BRI_EVENT_TYPE.TREE_CHANGE) {
    if (res.treeActData.id === treeAct.value?.id) {
      //同一个树组织
      return;
    }
    //更新对应的点 todo
    // treeRef.value.updateTree(res.treeActData);
    emits('action', {
      action: ACTION.PLANLISTTREECHANGE,
      data: { planListTree: res.treeActData },
    });
  }
  //组织树更改 计划详情的组织更改
  if (res.type === BRI_EVENT_TYPE.VIDEO_PLAN_TREE_CHANGE) {
    if (res.treeActData.id === formModel.value.deptId) {
      //同一个树组织
      return;
    }
    //更新对应的点 todo
    formModel.value.deptId = res.treeActData.id;
    getDeviceList(formModel.value.deptId as string);
  }
  //定位点更新 视频巡检gisxuanze
  if (res.type === BRI_EVENT_TYPE.VIDEO_PLAN_ADD_LOCATION) {
    isGisMsg.value = true;
    formModel.value.relVideos = res.data.selVideo ? JSON.parse(res.data.selVideo) : [];
  }
  // 去一张图
  if (res.type === BRI_EVENT_TYPE.DRAWING && res.data.isMap) {
    router.push('/drawing');
  }
});

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoPlanDetail' });
</script>

<style lang="scss">
.com-detail-drawer {
  .plan-frequency {
    margin-left: 10px;
  }
}
</style>
