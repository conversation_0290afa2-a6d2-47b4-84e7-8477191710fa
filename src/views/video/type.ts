import type { IObj, IPageRes } from '@/types';

//tab 数据
export interface ITabData {
  text: string;
  value: string;
}

//视频巡检视频选类型的列表
export interface IDeviceItem {
  deviceAddress: string;
  deviceId: string;
  deviceName: string;
  deviceTypeId: string;
  latitude: number;
  longitude: number;
  mapX: number;
  mapY: number;
  mapZ: number;
  deviceNum: string;
  manufacturerCode: string;
  onlineState?: string;
  loopDeviceNum?: string;
}

//设备详情 类型
export interface IDeviceDetail {
  allTaskNum: number;
  allDisposeNum: number;
  aerialMapType: number;
  brandId: string;
  buildingId: string;
  buildingName: string;
  cardInfo: unknown;
  channelNum: string;
  createTime: string;
  deviceAddress: string;
  deviceClassification: number;
  deviceId: string;
  deviceName: string;
  deviceNum: string;
  deviceOnlyId: string;
  deviceTask: string;
  deviceTypeId: string;
  deviceTypeName: string;
  deviceTypePid: string;
  deviceTypePname: string;
  floorAreaImg: string;
  floorId: string;
  floorMapType: number;
  floorName: string;
  houseNumber: string;
  installImg: string;
  installInfo: string;
  isAerialMap: number;
  keyPartId: string;
  laLoop: string;
  laMake: string;
  laPoint: string;
  latitude: number;
  longitude: number;
  manufacturerCode: string;
  mapX: number;
  mapY: number;
  mapZ: number;
  monitoringInfo: string;
  nonHostInfo: string;
  obsoleteDate: string;
  ownerId: string;
  ownerType: number;
  produceInfo: string;
  projectId: string;
  serviceModelCode: number;
  serviceModelName: string;
  standardInfo: string;
  status: number;
  subCenterCode: string;
  subCenterName: string;
  subordinateUnits: string;
  timeInterval: number;
  twoCode: string;
  unitAddress: string;
  unitName: string;
  unitPointX: number;
  unitPointY: number;
  updateTime: string;
  useInfo: unknown;
  videoLatitude: string;
  videoLongitude: string;
  videoMapX: string;
  videoMapY: string;
  videoMapZ: number;
  onlineState: string;
}

//当前树结构 类型
export interface ITreeItem {
  attributes: any;
  checked: boolean;
  children: any;
  deptIds: string;
  hasChildren: boolean;
  hasParent: boolean;
  id: string;
  level: string;
  levelCode: string;
  parentId: string;
  state: string;
  text: string;
  treeName: string;
  type: string;
  typeId: string;
  unitId: string;
  unitIds: string;
  unitStatus: string;
}
