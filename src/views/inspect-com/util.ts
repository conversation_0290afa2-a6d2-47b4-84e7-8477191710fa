import { IObj } from '@/types';

/**
 * 主机回路点位
 * @param {(Object)} newInfo
 * 迁移代码来源：ehs-internet-monitor/src/utils/index.js
 */
export function switchCode(newInfo: IObj<any>) {
  // 如果三个都不存在，则显示二次码
  if (!newInfo.laMake && !newInfo.laLoop && !newInfo.laPoint) {
    return '--';
  } else {
    newInfo.laMake = newInfo.laMake ? newInfo.laMake : '';
    newInfo.laLoop = newInfo.laLoop ? newInfo.laLoop : '';
    newInfo.laPoint = newInfo.laPoint ? newInfo.laPoint : '';
    let pinjie = '';
    if (newInfo.laMake !== '') {
      pinjie = newInfo.laMake;
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop;
    }
    if (newInfo.laMake !== '' && newInfo.laLoop !== '' && newInfo.laPoint !== '') {
      pinjie = newInfo.laMake + '-' + newInfo.laLoop + '-' + newInfo.laPoint;
    }
    return pinjie;
  }
}

export function normalizeAddress(item: any) {
  let address =
    (item.buildingName || item.buildName || '') +
    '' +
    (item.floorName || item.floorName || '') +
    '' +
    (item.deviceAddress || item.faultAddress || '');

  if (item.unitType != 0 && item.unitType) {
    address = (item.houseNumber || '') + (item.deviceAddress || '');
  }

  return address.trim() === '' ? '未采集' : address;
}
