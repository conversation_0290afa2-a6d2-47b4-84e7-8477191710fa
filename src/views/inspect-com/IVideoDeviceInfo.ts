/**
 * VideoDeviceInfo
 */
export interface IVideoDeviceInfo {
  /**
   * 电子档案设备信息
   */
  erecordDeviceInfo?: ErecordDeviceInfoVo;
  /**
   * 天泽智盒平台视频终端
   */
  ibmDeviceInfoVo?: IbmDeviceInfoVo;
  /**
   * 智能视频轮询终端视频设备号
   */
  loopDeviceNum?: string;
  /**
   * 视频播放地址
   */
  videoPlayUrl?: string;
  /**
   * 安消联动关联设备
   */
  videoRelDeviceList?: ErecordDeviceInfoVo[];
  [property: string]: any;
}

/**
 * 电子档案设备信息
 *
 * ErecordDeviceInfoVo
 */
export interface ErecordDeviceInfoVo {
  /**
   * 空开值
   */
  airSwitchAcValue?: string;
  /**
   * 品牌
   */
  brand?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 楼栋id
   */
  buildingId?: string;
  /**
   * 楼栋名称
   */
  buildingName?: string;
  /**
   * 通道号
   */
  channelNum?: string;
  /**
   * 是否核点    0-未核点 1-已核点
   */
  checkPoint?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: string;
  /**
   * 创建人名称
   */
  createUserName?: string;
  /**
   * 设备地址
   */
  deviceAddress?: string;
  /**
   * 设备分类 1 主机设备 2 非主机设备 3 物联网设备 4 用户信息传输装置  5 电子标签 6 视频设备
   */
  deviceClassification?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 消防设备名称
   */
  deviceName?: string;
  /**
   * 用户信息传输装置唯一编码/或者NB的唯一编码/或者LOra的唯一编码
   */
  deviceNum?: string;
  /**
   * 设备全局id
   */
  deviceOnlyId?: string;
  /**
   * 设备类型CODE
   */
  deviceTypeId?: string;
  /**
   * 系统类型名称
   */
  deviceTypeName?: string;
  /**
   * 设备类型父CODE
   */
  deviceTypePid?: string;
  /**
   * 系统父类型名称
   */
  deviceTypePname?: string;
  /**
   * 设备用途
   */
  deviceUsage?: string;
  /**
   * 设备用途名称
   */
  deviceUsageName?: string;
  /**
   * 楼层平面成果图
   */
  floorAreaImg?: string;
  /**
   * 楼层id
   */
  floorId?: string;
  /**
   * 楼层名称
   */
  floorName?: string;
  /**
   * 安装时间
   */
  installDate?: string;
  /**
   * 是否标点 0-是 1-否
   */
  isMarkSpot?: string;
  /**
   * 重点部位id
   */
  keyPartId?: string;
  /**
   * 回路
   */
  laLoop?: string;
  /**
   * 机号
   */
  laMake?: string;
  /**
   * 位置
   */
  laPoint?: string;
  /**
   * 纬度
   */
  latitude?: number;
  /**
   * 经度
   */
  longitude?: number;
  /**
   * 主机回路点位
   */
  makeLoopPoint?: string;
  /**
   * 生产日期
   */
  manufactureDate?: string;
  /**
   * 用户信息传输装置的厂家/NB的厂家/lora的厂家
   */
  manufacturerCode?: string;
  /**
   * 百度坐标X
   */
  mapX?: number;
  /**
   * 百度坐标Y
   */
  mapY?: number;
  /**
   * 三维坐标Z
   */
  mapZ?: number;
  /**
   * 用传编码规则
   */
  methodRule?: string;
  /**
   * 型号
   */
  model?: string;
  /**
   * 型号ID
   */
  modelId?: string;
  /**
   * 非主机设备信息
   */
  nonHostInfo?: string;
  /**
   * 报废日期
   */
  obsoleteDate?: string;
  /**
   * 单位ID
   */
  ownerId?: string;
  /**
   * 关联设备ID
   */
  relatedDeviceId?: string;
  /**
   * 关联设备类型
   */
  relatedType?: string;
  /**
   * 摄像头是否展示在鸟瞰图 0：不展示在鸟瞰图，1：展示在鸟瞰图
   */
  shownOnAerialView?: number;
  /**
   * 0,在用，1停用
   */
  status?: number;
  /**
   * 所属单元
   */
  subordinateUnits?: number;
  /**
   * 时间间隔
   */
  timeInterval?: number;
  /**
   * 二次码
   */
  twoCode?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 修改人id
   */
  updateUserId?: string;
  /**
   * 更新人
   */
  updateUserName?: string;
  /**
   * 有效期
   */
  validityDate?: string;
  /**
   * 摄像头所在鸟瞰图纬度
   */
  videoLatitude?: number;
  /**
   * 摄像头所在鸟瞰图经度
   */
  videoLongitude?: number;
  /**
   * 视频路径
   */
  videoUrl?: string;
  [property: string]: any;
}

/**
 * 天泽智盒平台视频终端
 *
 * IbmDeviceInfoVo
 */
export interface IbmDeviceInfoVo {
  /**
   * 支持的算法列表
   */
  algoList?: IbmAlgoDict[];
  /**
   * 通道号
   */
  channelNo?: string;
  /**
   * 主键，前端不用此字段
   */
  deviceId?: string;
  /**
   * 设备名称
   */
  deviceName?: string;
  /**
   * 设备号：MAC地址:通道号组成
   */
  deviceNo?: string;
  /**
   * 天泽智盒在线状态：0在线，1离线
   */
  ibmOnlineStatus?: string;
  /**
   * MAC地址
   */
  macAddr?: string;
  /**
   * 视频终端在线状态：0在线，1离线
   */
  onlineStatus?: string;
  /**
   * 组织ID
   */
  orgId?: string;
  /**
   * 组织名称
   */
  orgName?: string;
  [property: string]: any;
}

/**
 * 算法字典表
 *
 * IbmAlgoDict
 */
export interface IbmAlgoDict {
  /**
   * 算法代码
   */
  algoCode?: string;
  /**
   * 算法名称
   */
  algoName?: string;
  /**
   * 样式，例如颜色、字体，由前端定义
   */
  dictStyle?: string;
  /**
   * 排序号
   */
  orderNo?: number;
  [property: string]: any;
}
