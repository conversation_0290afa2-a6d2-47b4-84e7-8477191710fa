/**
 * TaskPointDetailVo，TaskPointDetailVo
 */
export interface ITaskPointDetailVo {
  /**
   * 品牌型号
   */
  brand?: string;
  /**
   * 设备地址
   */
  deviceAddress?: string;
  /**
   * 设备ID
   */
  deviceId?: string;
  /**
   * 设备编号
   */
  deviceNum?: string;
  /**
   * 巡检结果
   */
  inspectionResult?: InspectionVideoTaskDispose[];
  /**
   * 巡检时间
   */
  videoTime?: string;
  /**
   * 巡检图片
   */
  videoUrl?: string;
  [property: string]: any;
}

/**
 * InspectionVideoTaskDispose对象
 *
 * InspectionVideoTaskDispose
 */
export interface InspectionVideoTaskDispose {
  /**
   * 创建人
   */
  createdBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 逻辑删除标志1-是，0-否
   */
  delFlag?: number;
  /**
   * 设备上报时间
   */
  deviceTime?: string;
  /**
   * _处置事件类型 1: 火警事件，4: 隐患事件
   */
  disposeEventType?: number;
  /**
   * 处置ID
   */
  disposeId?: string;
  /**
   * 巡检异常处置状态 1：解决，2：未解决
   */
  disposeStatus?: number;
  /**
   * 隐患类型
   */
  eventType?: string;
  /**
   * 隐患类型名称
   */
  eventTypeName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 设备编码
   */
  loopDeviceNum?: string;
  /**
   * 厂商编码
   */
  manufacturerCode?: string;
  /**
   * 收到时间
   */
  receiveTime?: string;
  /**
   * 任务ID
   */
  taskId?: string;
  /**
   * 修改人
   */
  updatedBy?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 巡检视频地址
   */
  videoAddress?: string;
  /**
   * 巡检描述
   */
  videoDesc?: string;
  /**
   * 巡检视频ID
   */
  videoId?: string;
  /**
   * 巡检视频名称
   */
  videoName?: string;
  /**
   * 设备标号
   */
  videoNum?: string;
  /**
   * 巡检视频结果 0：正常，1：待巡检，2：异常
   */
  videoResult?: number;
  /**
   * 巡检视频结果名称 正常，异常
   */
  videoResultName?: string;
  /**
   * 巡检视频排序
   */
  videoSort?: number;
  /**
   * 巡检时间
   */
  videoTime?: string;
  /**
   * 巡检图片
   */
  videoUrl?: string;
  /**
   * 租户id
   */
  zhId?: string;
  [property: string]: any;
}
