c
<template>
  <ComDialogC title="查看点位" :width="1000" :height="600" :close-on-esc="false">
    <FloorGis :type="gisType" :build-id="buildId" :floor-id="floorId" :device-list="deviceList" />
  </ComDialogC>
</template>

<script setup lang="ts">
import { computed, useAttrs, watch } from 'vue';
import ComDialogC from '@/components/dialog/ComDialogC.vue';
import { useRoute } from 'vue-router';
import FloorGis from '@/gis-floor/floorGis.vue';
import { EGisType } from '@/gis-floor/constant';
import { IDeviceRow } from '@/gis-floor/type';

const gisType = EGisType.DEVICELOC;

const route = useRoute();
const $attr = useAttrs();

const props = withDefaults(
  defineProps<{
    deviceList: IDeviceRow[];
  }>(),
  {
    deviceList: () => [],
  }
);

const buildId = computed(() => props.deviceList[0]?.buildingId || '');
const floorId = computed(() => props.deviceList[0]?.floorId || '');

defineOptions({ name: 'DeviceLocation' });
</script>

<style module lang="scss">
.content {
  position: relative;
  backdrop-filter: blur(3px);
}
</style>
