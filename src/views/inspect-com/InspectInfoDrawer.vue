<template>
  <ComDrawerA
    title="详情"
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskNeedClosable="true"
    :show-action="false"
    class="!w-[430px]"
  >
    <div class="p-[24px]">
      <div class="flex justify-center">
        <ComTabF class="m-auto" :tab-list="tabList" :tab="curTab" @change="handleTabChange" />
      </div>

      <component :is="curViewComp" class="mt-[30px]" :data="data" />
    </div>
  </ComDrawerA>
</template>

<script setup lang="ts">
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import ComTabF from '@/components/tab/ComRadioTabF.vue';
import { computed, ref, useAttrs, watch } from 'vue';
import Fire from './InspectInfoFire.vue';
import Hazard from './InspectInfoHazard.vue';
import Record from './InspectInfoRecord.vue';

const $attr = useAttrs();

const props = defineProps({
  data: {
    type: Object as () => Partial<{ disposeId: string; disposeEventType: number; [key: string]: any }>,
    default: () => {},
  },
});

const tabMap: Record<string, { name: string; label: string }> = {
  '1': { name: '1', label: '火警信息' },
  '4': { name: '4', label: '隐患信息' },
  '6': { name: '6', label: '处置记录' },
};

// 根据类型动态组合 tab 项
const tabList = computed(() => {
  const key = String(props.data.disposeEventType);

  return [tabMap[key], tabMap['6']].filter(Boolean);
});

const viewCompMap: Record<string, any> = {
  '1': Fire, // 火警
  '4': Hazard, // 隐患
  '6': Record, // 处置记录
};

const curTab = ref<string>('');
const curViewComp = computed(() => viewCompMap[curTab.value]);

function handleTabChange(val: string) {
  curTab.value = val;
}

function getData() {}

function reset() {}

watch(
  () => $attr.show,
  (val) => {
    if (val) {
      curTab.value = tabList.value[0].name;
      getData();
    } else {
      reset();
    }
  }
);

defineOptions({ name: 'InspectInfoDrawer' });
</script>

<style module lang="scss"></style>
