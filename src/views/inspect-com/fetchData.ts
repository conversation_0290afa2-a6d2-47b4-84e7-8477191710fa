import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http/index.ts';
import { ITaskPointDetailVo } from './ITaskPointDetailVo.ts';
import { IVideoDeviceInfo } from './IVideoDeviceInfo.ts';

export function getDisposeEventFireInfo(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.internetMonitor.getDisposeEventFireInfo, query);

  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getDisposeEventHazardInfo(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.internetMonitor.getDisposeEventHazardInfo, query);

  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getDisposeEventHazardUrgeRecord(query: IObj<any>) {
  const params = api.getComParams(
    api.type.intelligent,
    api.name.internetMonitor.getDisposeEventHazardUrgeRecord,
    query
  );

  return $http.post<any>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

export function getDisposeEventFireRecord(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.internetMonitor.getDisposeEventFireRecord, query);

  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getDisposeEventHazardRecord(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.internetMonitor.getDisposeEventHazardRecord, query);

  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getTaskPointDetail(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.intelligent.taskPointDetailByPositionNo, query);

  return $http.post<ITaskPointDetailVo>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

export function getVideoDeviceInfo(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getVideoDeviceInfo, query);

  return $http.get<IVideoDeviceInfo>(url, { data: { _cfg: { showTip: true } } });
}
