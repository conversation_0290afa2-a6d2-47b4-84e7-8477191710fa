import { IObj } from '@/types';
import { useStore } from '@/store';
import { BridgeRemoteService } from '@/service/bridge/BridgeRemoteService.ts';
import { BRI_EVENT_TYPE, EVENT_TYPE, ISender, ISenderData } from '@/service/bridge/type.ts';

//参数筛选
export function paramsFn(data: IObj<any>, param: string[]) {
  const curParam = [...param];
  const curData = { ...data };
  //Object.keys(data).forEach
  for (const key in data) {
    if (!param.includes(key)) {
      delete curData[key];
    }
  }
  return curData;
}

//截取时间
export function ymrDateFn(d: string | number) {
  const curD = d?.toString() || '';
  return curD ? curD.slice(0, 10) : '--';
}

//通知GIS 通信
export function sendGis(type: BRI_EVENT_TYPE, data?: any) {
  const { treeAct } = useStore();
  BridgeRemoteService.getIns().sendMessage(EVENT_TYPE.MESSAGE, {
    type: type,
    data: data || null,
    treeActData: {
      id: treeAct?.id ?? '',
      deptIds: treeAct?.deptIds ?? '',
      text: treeAct?.text ?? '',
      treeName: treeAct?.treeName ?? '',
      unitId: treeAct?.unitId ?? '',
      unitIds: treeAct?.unitIds ?? '',
    },
  });
}
//关闭
export function offGis() {
  BridgeRemoteService.getIns().offMessage(EVENT_TYPE.MESSAGE);
}

//接收 receive
export function receiveGis(cb: (data: any) => any) {
  BridgeRemoteService.getIns().onMessage(EVENT_TYPE.MESSAGE, (receiveData) => {
    cb(receiveData);
  });
}
