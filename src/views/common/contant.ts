// 状态颜色常量
export const STATUS_COLORS: Record<string, string> = {
  blue: 'rgba(36, 109, 237, 0.65)',
  yellow: 'rgba(211, 132, 21, 0.65)',
  green: 'rgba(21, 117, 59, 0.65)',
  gray: 'rgba(85, 95, 112, 0.65)',
  purple: 'rgba(72, 13, 137, 0.65)',
  red: 'rgba(250, 81, 81, 0.65)',
} as const;

// 状态颜色类型
export type StatusColorType = keyof typeof STATUS_COLORS;

/* 任务相关 -> */

// 任务状态颜色映射
export const TASK_STATUS_COLORS: Record<number, string> = {
  0: STATUS_COLORS.purple, // 待开始
  1: STATUS_COLORS.yellow, // 未执行/逾期
  2: STATUS_COLORS.blue, // 进行中
  3: STATUS_COLORS.green, // 已完成
} as const;

// 任务状态类型
export type TaskStatusType = keyof typeof TASK_STATUS_COLORS;

// 任务状态到颜色类名的映射
export const TASK_STATUS_CLASS_MAP: Record<number, keyof typeof STATUS_COLORS> = {
  0: 'purple',
  1: 'yellow',
  2: 'blue',
  3: 'green',
} as const;

/* 任务相关 <- */

/* 巡检视频结果 -> */

// 巡检视频结果状态到颜色类名的映射
export const VIDEO_RESULT_STATUS_CLASS_MAP: Record<number, keyof typeof STATUS_COLORS> = {
  0: 'green',
  1: 'yellow',
  2: 'red',
} as const;

/* 巡检视频结果 <- */

/* 扩展其它 -> */
