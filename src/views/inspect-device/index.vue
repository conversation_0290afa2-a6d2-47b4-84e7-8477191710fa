<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData" />
    <div class="com-g-col-a1">
      <ComOrgTreeWrap @change="treeChange" />
      <div class="com-g-row-aa1">
        <statisticComp ref="statisticCompRef" @action="actionFn"></statisticComp>
        <Filter ref="filterCompRef" @action="actionFn" />
        <tbaleComp class="com-table-container min-h-0" ref="tableCompRef" @action="actionFn"></tbaleComp>
      </div>
    </div>
    <detailComp ref="detailCompRef"></detailComp>
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { IBreadData } from '@/components/breadcrumb/type';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import { provide, Ref, ref } from 'vue';
import { useStore } from '@/store';
import statisticComp from './comp/statistic.vue';
import Filter from './comp/Filter.vue';
import tbaleComp from './comp/Table/index.vue';
import detailComp from './comp/detail/index.vue';
import { ACTION, PROVIDE_KEY } from './constant';
import { getVideoDeviceInfoAPI } from './fetchData';
import { deviceService } from './deviceService.ts';
// 卡片统计
const statisticCompRef = ref();

// 卡片列表
const tableCompRef = ref();

// 详情
const detailCompRef = ref();
const filterCompRef = ref();

const breadData = ref<IBreadData[]>([{ name: '智能巡检' }, { name: '设备巡检' }]);

function treeChange(val: any) {
  handleSearch({ action: ACTION.SEARCH, data: {} });
  handleSearchByStatistic({ action: ACTION.SEARCH, data: {} });
  filterCompRef.value?.GetoptionsbuildByunitId();
}
function actionFn(val: any) {
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(val.data);
      handleSearchByStatistic(val.data);
      break;
    case ACTION.DETAIL:
      console.log('详情');
      handlDetail(val.data);
      // detailCompRef.value?.open(val.data);
      break;
  }
}

function handlDetail(data: any) {
  getVideoDeviceInfoAPI({ deviceId: data.deviceId }).then((res: any) => {
    if (res.code === 'success') {
      deviceService.baseInfoForm.value = res.data;
      detailCompRef.value?.open(data);
    }
  });
}

function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}
function handleSearchByStatistic(data?: Record<string, any>) {
  if (data) {
    statisticCompRef.value?.equiListRecordWrap(data);
  } else {
    statisticCompRef.value?.equiListRecord();
  }
}

defineOptions({ name: 'InspectDeviceIndex' });
</script>

<style scoped lang="scss"></style>
