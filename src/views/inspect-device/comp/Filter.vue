<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="4" label="楼栋">
          <n-select
            v-model:value="filterForm.buildingId"
            :options="optionsbuild"
            label-field="buildingName"
            value-field="buildingId"
            clearable
            placeholder="请选择楼栋"
            @update:value="GetoptionsfloorBybuild"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="4" label="楼层">
          <n-select
            v-model:value="filterForm.floorId"
            :options="optionsfloor"
            label-field="floorName"
            value-field="floorId"
            clearable
            placeholder="请选择楼层"
          />
        </n-form-item-gi>

        <n-form-item-gi label="在线状态" :span="4">
          <n-select
            :options="PTIONSSTATE"
            clearable
            v-model:value="filterForm.onlineState"
            placeholder="请选择在线状态"
          />
        </n-form-item-gi>

        <n-form-item-gi label="设备位置" :span="4">
          <n-input placeholder="设备位置" clearable v-model:value="filterForm.deviceAddress" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { trimObjNull } from '@/utils/obj';
import { onMounted, ref, watch } from 'vue';
import { ACTION, PTIONSSTATE } from '../constant';
import { getBuildingListByUnitIdAPI, getFloorListByUnitIdAndBuildingAPI } from '../fetchData';
import { useStore } from '@/store';
import { storeToRefs } from 'pinia';
const store = useStore();
const { treeAct } = storeToRefs(store);
const emits = defineEmits(['action']);

const optionsbuild = ref([]);
const optionsfloor = ref([]);

const filterForm = ref(initForm());
function initForm() {
  return {
    buildingId: null, //楼栋
    floorId: null, //楼层
    onlineState: null, //在线状态
    deviceAddress: '', //设备位置
  };
}

// 获取楼栋
function GetoptionsbuildByunitId() {
  filterForm.value.buildingId = null;
  filterForm.value.floorId = null;
  optionsbuild.value = [];
  optionsfloor.value = [];
  getBuildingListByUnitIdAPI({ unitId: treeAct.value?.attributes.erecordUnitId }).then((res: any) => {
    optionsbuild.value = res.data;
    console.log(optionsbuild.value, '-=-=-=-=-=楼栋列表');
  });
}
function GetoptionsfloorBybuild() {
  // console.log(filterForm.value.buildingId, '-=-=-=-GetoptionsfloorBybuild');
  if (filterForm.value.buildingId) {
    filterForm.value.floorId = null;
    optionsfloor.value = [];
    getFloorListByUnitIdAndBuildingAPI({
      unitId: treeAct.value?.attributes.erecordUnitId,
      buildId: filterForm.value.buildingId,
    }).then((res: any) => {
      optionsfloor.value = res.data;
      console.log(optionsfloor.value, '-=-=-=-=-=楼层列表');
    });
  } else {
    optionsfloor.value = [];
    filterForm.value.floorId = null;
  }
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  // console.log(filterForm.value.buildingId, '-=-=-=-filterForm.value.buildingId');
  // if (filterForm.value.buildingId) {
  //   optionsfloor.value = [];
  //   filterForm.value.floorId = null;
  //   GetoptionsfloorBybuild();
  // } else {
  //   optionsfloor.value = [];
  //   filterForm.value.floorId = null;
  // }
  doHandle(ACTION.SEARCH);
});
defineExpose({ GetoptionsbuildByunitId });
defineOptions({ name: 'FilterComp' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
