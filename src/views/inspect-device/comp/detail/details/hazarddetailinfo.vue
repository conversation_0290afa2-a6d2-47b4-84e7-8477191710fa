<template>
  <div class="detail-comp">
    <div class="title" @click="isShow = !isShow">
      <div class="text-[16px]">隐患信息</div>
      <div v-if="isShow"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShow">
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-gi v-for="item in basicInfo" :key="item.key" :span="item.span">
          <span class="label">{{ item.label }}</span>
          <span class="value"></span>
          <!-- <span class="relative" v-if="item.type === 'map'">查看位置</span> -->
        </n-gi>
      </n-grid>
    </div>
  </div>

  <div class="detail-comp">
    <div class="title" @click="isShow1 = !isShow1">
      <div class="text-[16px]">设备信息</div>
      <div v-if="isShow1"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShow1">
      <n-grid :x-gap="12" :y-gap="8" :cols="1">
        <n-gi v-for="item in basicInfo1" :key="item.key" :span="item.span">
          <span class="label">{{ item.label }}</span>
          <span class="value"></span>
        </n-gi>
      </n-grid>
    </div>
  </div>

  <div class="detail-comp">
    <div class="title" @click="isShow2 = !isShow2">
      <div class="text-[16px] cursor-pointer">催促记录</div>

      <div v-if="isShow2"><AkChevronDown /></div>
      <div v-else><AkChevronUp /></div>
    </div>
    <div class="content" v-if="isShow2">
      <!--  :columns="columnsPlan" -->
      <n-data-table class="h-full" remote striped :data="tableDataPlan" :bordered="false" :render-cell="useEmptyCell" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { AkChevronUp } from '@kalimahapps/vue-icons';
import { AkChevronDown } from '@kalimahapps/vue-icons';
import { computed, ref } from 'vue';
import Empty from '@/components/empty/index.vue';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { columnsPlan } from './columns.ts';

const isShow = ref(true);

const isShow1 = ref(true);

const isShow2 = ref(true);

const basicInfo = computed(() => {
  let result: any[] = [
    { label: '隐患来源：', key: 'unitName1', span: 1 },
    { label: '隐患类别：', key: 'unitName2', span: 1 },
    { label: '隐患描述：', key: 'unitName3', span: 1 },
  ];
  return result;
});

const basicInfo1 = computed(() => {
  let result: any[] = [
    { label: '单位名称：', key: 'unitName1', span: 1 },
    { label: '设备编号：', key: 'unitName2', span: 1 },
    { label: '系统类型：', key: 'unitName3', span: 1 },
    { label: '设备类型：', key: 'unitName3', span: 1 },
    { label: '设备位置：', key: 'unitName3', span: 1 },
    { label: '品牌型号：', key: 'unitName3', span: 1 },
    { label: '安装日期：', key: 'unitName3', span: 1 },
  ];
  return result;
});

const tableData = ref([]);
const tableDataPlan = ref([]);
function handleLocation() {
  console.log('位置');
}
function getTableData() {
  console.log('列表数据');
}
defineOptions({ name: 'detailComp' });
</script>

<style scoped lang="scss">
.detail-comp {
  margin-top: 20px;
  width: 100%;
  background: rgba(24, 40, 71, 0.45);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #48669b;
  padding: 20px;
  .title {
    @apply flex items-center justify-between;
  }
  .content {
    @apply mt-[12px];
    .relative {
      cursor: pointer;
      border: 1px solid #dcdfe6;
      padding: 0 12px 0 12px;
      &:hover {
        background-color: #0356a3;
        border: 1px solid #0356a3;
      }
    }
  }
}
</style>
