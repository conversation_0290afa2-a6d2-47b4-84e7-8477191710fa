<template>
  <div class="flex flex-col">1</div>
</template>

<script setup lang="ts">
defineOptions({ name: 'recordComp' });
</script>

<style scoped lang="scss">
@mixin font($font-size) {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: 500;
  font-size: $font-size;
  color: #ffffff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.item {
  width: 100%;
  height: 115px;
  background: rgba(24, 40, 71, 0.45);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #48669b;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;

  .itemL {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .itemL-T {
      @include font(16px);
    }
    .itemL-M {
      @include font(12px);
    }
    .itemL-B {
      @apply w-[60px] h-[20px] text-[#fff] text-[12px];
      text-align: center;
    }
  }
}
</style>
