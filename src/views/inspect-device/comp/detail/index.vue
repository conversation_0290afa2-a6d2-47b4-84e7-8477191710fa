<template>
  <n-drawer v-model:show="active" :width="430" :autoFocus="false">
    <n-drawer-content :native-scrollbar="false">
      <template #header>
        <div class="header">
          <span class="text-[18px]">设备详情</span>
          <div class="btn-close" @click="handleClose"></div>
        </div>
      </template>
      <div class="flex justify-center">
        <ComTabF :tab-list="tabList" :tab="curTab" @change="handleTabChange" />
      </div>
      <component :is="curComp" :listItme="listItme" />
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import ComTabF from '@/components/tab/ComRadioTabF.vue';
import detailinfo from './detailinfo.vue';
import historyList from './historyList.vue';

const listItme = ref<any>({});
const tabList = [
  {
    name: '1',
    label: '设备信息',
    component: detailinfo,
  },
  {
    name: '2',
    label: '历史事件',
    component: historyList,
  },
];
const curTab = ref<any>('1');

const active = ref(false);

const curComp = ref<any>(tabList[0].component);

function handleTabChange(val: string) {
  curTab.value = val;
  curComp.value = tabList.find((item) => item.name === val)?.component;
}

function handleClose() {
  active.value = false;
  curComp.value = tabList[0].component;
  curTab.value = tabList[0].name;
}
const open = (data: any) => {
  console.log(data, 'data-----');
  listItme.value = data;
  active.value = true;
};

watch(
  () => active.value,
  (val) => {
    if (!val) {
      active.value = false;
      curComp.value = tabList[0].component;
      curTab.value = tabList[0].name;
    }
  }
);

defineExpose({ open });
defineOptions({ name: 'detailComp' });
</script>

<style scoped lang="scss">
:deep(.n-drawer-header) {
  padding: 0 !important;
}
.header {
  position: relative;
  width: 100%;
  height: 38px;
  display: flex;
  align-items: center;
  font-size: 18px;
  line-height: 1.25;
  padding-left: 24px;
  box-sizing: border-box;
  background: url('../../assets/header_bg.png') 0 0 no-repeat;
  background-size: 100% 100%;
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  .btn-close {
    position: absolute;
    right: 16px;
    width: 14px;
    height: 14px;
    background: url('../../assets/close_icon.png') 0 0 no-repeat;
    cursor: pointer;
  }
}
.content {
  padding: 16px;
}
</style>
