<template>
  <div class="flex flex-col">
    <div class="mt-[20px]">
      <n-form-item label="报警时间" label-placement="left">
        <n-date-picker v-model:value="range" type="daterange" clearable @update:value="dateRangeChange" />
      </n-form-item>
    </div>
    <div class="flex-1">
      <n-scrollbar style="height: 75vh">
        <div v-if="tableList.length">
          <div v-for="(item, index) in tableList" :key="index" class="item" @click="itemClick">
            <div class="itemL">
              <div class="itemL-T">【{{ item.eventTypeName || '--' }}】</div>
              <div class="itemL-M">{{ item.deviceTime }}</div>
              <div class="itemL-B" :style="{ background: videoResultObj[+item.disposeStatus - 1].color }">
                <!-- 巡检异常处置状态 1：解决，2：未解决 -->
                {{ videoResultObj[+item.disposeStatus - 1].lable }}
              </div>
            </div>
            <div class="w-[148px] h-[100px]">
              <img v-if="item.videoUrl" :src="item.videoUrl" class="w-full h-full object-cover" />
              <div class="w-full h-full object-cover" v-else><AkImage class="w-full h-full" /></div>
            </div>
          </div>
        </div>
        <Empty v-else></Empty>
      </n-scrollbar>
      <!-- 固定在底部 -->
      <div class="flex justify-end mt-[10px]">
        <PaginationComp></PaginationComp>
      </div>
    </div>
  </div>
  <detailsComp ref="detailsRef"></detailsComp>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import { useNaivePagination, useNPaginationComp } from '@/common/hooks/useNaivePagination';
import detailsComp from './details/index.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { queryVideoTaskDisposeByVideoDeviceIdAPI } from '../../fetchData';
import Empty from '@/components/empty/index.vue';
import { useStore } from '@/store';
import { storeToRefs } from 'pinia';
import { AkImage } from '@kalimahapps/vue-icons';

// 巡检处置视频结果 1：待处置，2：已处置
const videoResultObj: any = [
  { lable: '已处置', color: '#67C23A' },
  { lable: '未处置', color: '#F56C6C' },
];

const store = useStore();
const { treeAct } = storeToRefs(store);
const props = defineProps({
  listItme: {
    type: Object,
    default: () => {},
  },
});
const [loading, run] = useAutoLoading(true);

const paginationOpt = useNaivePagination(() => getTableData(), {
  pageSizes: [10, 20, 30, 40, 50],
  size: 'small',
  simple: true,
});

const PaginationComp = useNPaginationComp(paginationOpt.pagination);

const range = ref<any>([new Date().getTime() - 30 * 24 * 60 * 60 * 1000, new Date().getTime()]);

const filterForm = ref<any>({
  timeBegin: dayjs(range.value[0]).format('YYYY-MM-DD 00:00:00'),
  timeEnd: dayjs(range.value[1]).format('YYYY-MM-DD 23:59:59'),
});
const tableList = ref<any>([]);
function getTableData() {
  const query = {
    ...filterForm.value,
    pageNo: paginationOpt.pagination.page,
    pageSize: paginationOpt.pagination.pageSize,
    deptId: treeAct.value?.id,
    deviceId: props.listItme.deviceId,
  };
  run(queryVideoTaskDisposeByVideoDeviceIdAPI(query)).then((res: any) => {
    tableList.value = res.data.rows || [];
    console.log(tableList.value, '-=-=-=-=-=-=-=-=-=-=-=历史列表数据');
    paginationOpt.updateTotal(res.data.total || 0);
  });
}
function dateRangeChange(val: any) {
  if (val && val.length === 2) {
    filterForm.value.timeBegin = dayjs(val[0]).format('YYYY-MM-DD 00:00:00');
    filterForm.value.timeEnd = dayjs(val[1]).format('YYYY-MM-DD 23:59:59');
  } else {
    filterForm.value.timeBegin = null;
    filterForm.value.timeEnd = null;
  }
  paginationOpt.pagination.page = 1;
  getTableData();
}
getTableData();

const detailsRef = ref();
function itemClick(itme: any) {
  console.log('itemClick');
  detailsRef.value?.open(itme);
}

defineOptions({ name: 'historyComp' });
</script>

<style scoped lang="scss">
@mixin font($font-size) {
  font-family:
    Alibaba PuHuiTi 2,
    Alibaba PuHuiTi 20;
  font-weight: 500;
  font-size: $font-size;
  color: #ffffff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
.item {
  width: 100%;
  height: 115px;
  background: rgba(24, 40, 71, 0.45);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #48669b;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;

  .itemL {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .itemL-T {
      @include font(16px);
    }
    .itemL-M {
      @include font(12px);
    }
    .itemL-B {
      @apply w-[60px] h-[20px] text-[#fff] text-[12px];
      text-align: center;
    }
  }
}
</style>
