<template>
  <div :class="$style.deviceList" class="com-g-row-1a">
    <!--    <com-loading v-if="loading"></com-loading>-->
    <n-scrollbar v-if="tableData.length > 0" x-scrollable class="flex-1">
      <n-grid :x-gap="24" :y-gap="24" :cols="3" class="min-w-max">
        <n-gi class="min-w-[528px] w-[528px]" v-for="(itme, index) in tableData" :key="index">
          <div class="item">
            <div class="item-content">
              <div class="item-content-title">
                <div class="title" :class="[itme.onlineState == '0' ? 'online' : 'offline']">
                  {{ itme.onlineState == '0' ? '在线' : itme.onlineState == '1' ? '离线' : '未接入' }}
                </div>
              </div>
              <div class="pl-[20px] text-[16px] font-[500] ellipsis" :title="itme.buildingName">
                {{ itme.buildingName }}
              </div>
              <div class="flex justify-between pl-[20px] pr-[20px] pt-[10px]">
                <div class="w-[65%] h-[200px] relative">
                  <iframe
                    v-if="itme.videoPlayUrl"
                    :src="getiframeSrc(itme.videoPlayUrl)"
                    class="w-full h-full"
                    frameborder="0"
                    allow="autoplay; fullscreen"
                  ></iframe>
                  <div v-else class="w-full h-full">
                    <div class="equiVideo"><img src="../../assets/video-icon.png" class="img" /></div>
                    <img src="../../assets/equi.jpg" style="width: 100%; height: 100%" alt="" />
                  </div>
                  <!--                  <div class="equiVideo">-->
                  <!--                    <img src="../../assets/video-icon.png" class="img" />-->
                  <!--                  </div>-->
                  <!--                  <img src="../../assets/equi.jpg" style="width: 100%; height: 100%" alt="" />-->
                </div>
                <div class="w-[35%] h-[200px] pl-[10px] flex flex-col relative">
                  <div>视频算法</div>
                  <n-scrollbar
                    v-if="itme.algoList?.length > 0"
                    style="margin-bottom: 50px"
                    content-style="overflow: hidden;"
                  >
                    <div
                      v-for="(algo, index) in itme.algoList"
                      :key="index"
                      class="mt-[10px] mb-[10px] suanfaitmes"
                      :style="algo.dictStyle"
                      :title="algo.algoName"
                    >
                      {{ algo.algoName }}
                      <!-- <span class="suanfaitmes" :title="algo.algoName">{{ algo.algoName }}</span> -->
                    </div>
                  </n-scrollbar>
                  <Empty v-else></Empty>
                  <div class="selectinfo_btn" @click="handleClick(itme)">查看详情</div>
                </div>
              </div>
            </div>
          </div>
        </n-gi>
      </n-grid>
    </n-scrollbar>
    <Empty v-else></Empty>
    <PaginationComp class="justify-end pt-[10px]" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useNaivePagination, useNPaginationComp } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { ACTION, generateGradientColor } from '@/views/inspect-device/constant';
import Empty from '@/components/empty/index.vue';
import { storeToRefs } from 'pinia';
import { useStore } from '@/store';
import { getDevicePageAPI } from '../../fetchData';
import { getVideoPlayerUrl } from '@/components/player/util.ts';

const emits = defineEmits(['action']);
const store = useStore();
const { treeAct } = storeToRefs(store);

const paginationOpt = useNaivePagination(() => getTableData(), { pageSizes: [6, 12, 30, 60, 90] });
const PaginationComp = useNPaginationComp(paginationOpt.pagination);
paginationOpt.pagination.pageSize = 6;
const [loading, run] = useAutoLoading(true);

const tableData = ref<any[]>([]);
let filterData: IObj<any> = {}; // 搜索条件
const paramsData = ref({});
function getTableData() {
  paramsData.value = {
    ...filterData,
    pageNo: paginationOpt.pagination.page,
    pageSize: paginationOpt.pagination.pageSize,
    deptId: treeAct.value?.id,
    // deviceId: treeAct.value?.deptIds,
  };
  console.log(paramsData.value, '-=-=-=-=-=-=-=-=-=-=-=paramsData.value1111');

  run(getDevicePageAPI({ ...paramsData.value })).then((res: any) => {
    tableData.value = res.data.rows || [];
    console.log(tableData.value, '-=-=-=-=-=-=-=-=-=-=-=设备列表数据');
    paginationOpt.updateTotal(res.data.total || 0);
  });
}
// 详情
function handleClick(itme: any) {
  console.log('click');
  emits('action', {
    action: ACTION.DETAIL,
    data: { ...itme },
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  paginationOpt.pagination.page = 1;
  getTableData();
}

function getiframeSrc(url: string) {
  return getVideoPlayerUrl(url, undefined, { autoplay: false });
}
defineExpose({ getTableDataWrap, getTableData });
defineOptions({ name: 'tbaleComp' });
</script>
<style module lang="scss">
.deviceList {
  row-gap: 20px;
  padding: 10px;
}
</style>
<style scoped lang="scss">
.min-w-max {
  min-width: max-content;
}
.ellipsis {
  @apply overflow-hidden text-ellipsis whitespace-nowrap;
}

.item {
  @apply w-[528px] min-w-[528px] h-[275px];
  cursor: pointer; /* 添加鼠标指针样式 */
  background-image: url('../../assets/tablebg.png');
  background-size: 100% 100%;
  .item-content {
    .item-content-title {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .title {
        text-align: center;
        font-size: 12px;
        min-width: 44px;
        height: 20px;
        color: #fff;
        border-bottom-left-radius: 10px;
      }
    }
    .suanfaitmes {
      width: fit-content;
      max-width: 160px;
      padding: 0 6px 0 6px;
      border-radius: 12px 12px 12px 12px !important;
    }
    .selectinfo_btn {
      position: absolute;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      bottom: 0;
      width: 88px;
      height: 30px;
      position: absolute;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      bottom: 0;

      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      background: #005bff80;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #3371dc;

      // background: linear-gradient(180deg, #0d1e3b 0%, #2251a1 100%);
      // border-radius: 2px 2px 2px 2px;
      // border: 1px solid #3371dc;

      &:hover {
        /* 添加悬停效果 */
        background: linear-gradient(180deg, #2251a1 0%, #0d1e3b 100%);
        box-shadow: 0 0 5px rgba(51, 113, 220, 0.8);
      }
    }
  }
}
.equiVideo {
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -16px;
  width: 42px;
  height: 42px;
  cursor: pointer;
  .img {
    width: 100%;
    height: 100%;
  }
}
.online {
  background-color: #156732ff;
}
.offline {
  background-color: #c0c0c0;
}

// // 定义一个 Sass 函数来生成渐变色
// @function generate-gradient($color) {
//   @return linear-gradient(180deg, $color, adjust-color($color, $lightness: -20%));
// }
// .generate-gradient{

// }

// // 使用函数生成不同颜色的渐变
// .i1 {
//   background: generate-gradient(#e4780d);
// }
// .i2 {
//   background: generate-gradient(#4773db);
// }
// .i3 {
//   background: generate-gradient(#d62d2d);
// }
// .i4 {
//   background: generate-gradient(#954bbfff);
// }
// .i5 {
//   background: generate-gradient(#eb0496ff);
// }
// .i6 {
//   background: generate-gradient(#02c17cff);
// }
// .i7 {
//   background: generate-gradient(#0ad4e8ff);
// }
// .i8 {
//   background: generate-gradient(#fdc40dff);
// }
</style>
