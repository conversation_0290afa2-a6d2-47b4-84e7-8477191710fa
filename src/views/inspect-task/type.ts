import { ACTION } from './constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  abnormalNum: number;
  planId: string;
  planName: string;
  progressNum: number;
  taskId: string;
  taskPlanEndTime: string;
  taskPlanStartTime: string;
  taskStatus: number;
  taskStatusName: string;
  videoDeviceNum: number;
  videoUrlNum: number;
  videoUrls: string[];
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
// export type IDetailRes = IPageItem;

export interface IInspectTaskOverViewStatistics {
  taskCompletedCount: number;
  taskInProgressCount: number;
  taskOverDueCount: number;
  taskToBeginCount: number;
  taskTotal: number;
}

export interface ITaskTopDetail {
  /**
   * 视频巡检点位数
   */
  allNum: number;
  /**
   * 楼栋id
   */
  buildingId: string;
  /**
   * 所属单位
   */
  deptId: string;
  /**
   * 所属单位
   */
  deptIds: string;
  /**
   * 所属单位名称
   */
  deptName: string;
  /**
   * 点子档案单位id
   */
  erecordUnitId: string;
  /**
   * 计划ID
   */
  planId: string;
  /**
   * 计划名称
   */
  planName: string;
  /**
   * 任务实际结束时间
   */
  taskEndTime: string;
  taskPlanEndTime: string;
  /**
   * 任务计划开始时间
   */
  taskPlanStartTime: string;
  /**
   * 任务实际开始时间
   */
  taskStartTime: string;
  /**
   * 任务状态 0：待开始，1：未执行/逾期，2：进行中，3，已完成
   */
  taskStatus: number;
  taskStatusName: string;
}

export interface ITaskInspectionDetailStatistic {
  /**
   * 巡检点总数量
   */
  allNum: number;
  /**
   * 已完成位数
   */
  finishedNum: number;
  /**
   * 异常事件数
   */
  hazardNum: number;
  /**
   * 超期未执行位数
   */
  overdueNum: number;
  /**
   * 进行中数量
   */
  useNum: number;
  /**
   * 待开始数量
   */
  waitOpenNum: number;

  /**
   * 巡检进度百分比
   */
  progress: number;
}

export interface ITaskInspectionDetailPageItem {
  /**
   * 楼栋ID
   */
  buildingId: string;
  /**
   * 楼栋名称
   */
  buildingName: string;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 创建人名称
   */
  createdByName: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 逻辑删除标志1-是，0-否
   */
  delFlag: number;
  /**
   * 设备地址
   */
  deviceAddress: string;
  /**
   * 处置ID
   */
  disposeId: string;
  /**
   * 自动检查-检查信息
   */
  disposeList: any[];
  /**
   * 巡检异常处置状态 1：解决，2：未解决
   */
  disposeStatus: number;
  /**
   * 异常事件
   */
  eventTypeNames: string;
  /**
   * 楼层ID
   */
  floorId: string;
  /**
   * 楼层名称
   */
  floorName: string;
  /**
   * 隐患分类
   */
  hazardClass: string;
  /**
   * 隐患描述
   */
  hazardDesc: string;
  /**
   * 隐患等级
   */
  hazardGrade: string;
  /**
   * 隐患ID
   */
  hazardId: string;
  /**
   * 隐患位置
   */
  hazardPosition: string;
  /**
   * 主键
   */
  id: string;
  /**
   * 纬度
   */
  latitude: number;
  /**
   * 经度
   */
  longitude: number;
  /**
   * 设备编码
   */
  loopDeviceNum: string;
  /**
   * 厂商编码
   */
  manufacturerCode: string;
  /**
   * 设备在离线状态（0：在线，1：离线，2：未接入）
   */
  onlineState: string;
  /**
   * 位置编号
   */
  positionNo: number;
  /**
   * 备注
   */
  remark: string;
  /**
   * 任务ID
   */
  taskId: string;
  /**
   * 修改人
   */
  updatedBy: string;
  /**
   * 修改人名称
   */
  updatedByName: string;
  /**
   * 修改人名称
   */
  updatedName: string;
  /**
   * 修改时间
   */
  updateTime: string;
  /**
   * 巡检视频地址
   */
  videoAddress: string;
  /**
   * 巡检图片
   */
  videoDesc: string;
  /**
   * 是否完成 0：未完成 1：完成
   */
  videoFinished: number;
  /**
   * 巡检视频ID
   */
  videoId: string;
  /**
   * 视频纬度
   */
  videoLatitude: number;
  /**
   * 视频经度
   */
  videoLongitude: number;
  /**
   * 巡检视频名称
   */
  videoName: string;
  /**
   * 设备编号
   */
  videoNum: string;
  /**
   * 巡检视频结果 0：正常，1：待巡检，2：异常
   */
  videoResult: number;
  /**
   * 巡检视频排序
   */
  videoResultName: string;
  videoSort: number;
  /**
   * 上报来源 0：未知 1：手工 2：自动
   */
  videoSource: number;
  /**
   * 巡检时间
   */
  videoTime: string;
  /**
   * 巡检图片
   */
  videoUrl: string;
  /**
   * 租户id
   */
  zhId: string;

  deviceId?: string;
  deviceName?: string;
  [props: string]: any;
}

export type ITaskInspectionDetailPageListRes = IPageRes<ITaskInspectionDetailPageItem>;

export interface ITaskInspectionPageItem {
  /**
   * 楼栋ID
   */
  buildingId: string;
  /**
   * 楼栋名称
   */
  buildingName: string;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 逻辑删除标志1-是，0-否
   */
  delFlag: number;
  /**
   * 设备地址
   */
  deviceAddress: string;
  /**
   * 处置ID
   */
  disposeId: string;
  /**
   * 巡检异常处置状态 1：解决，2：未解决
   */
  disposeStatus: number;
  /**
   * 巡检异常处置状态 1：解决，2：未解决
   */
  disposeStatusName: string;
  /**
   * 隐患类型
   */
  eventType: string;
  /**
   * 隐患类型名称
   */
  eventTypeName: string;
  /**
   * 楼层ID
   */
  floorId: string;
  /**
   * 楼层名称
   */
  floorName: string;
  /**
   * 主键
   */
  id: string;
  /**
   * 设备编码
   */
  loopDeviceNum: string;
  /**
   * 厂商编码
   */
  manufacturerCode: string;
  /**
   * 位置编号
   */
  positionNo: number;
  /**
   * 任务设备表Id
   */
  taskDeviceId: string;
  /**
   * 任务ID
   */
  taskId: string;
  /**
   * 修改人
   */
  updatedBy: string;
  /**
   * 巡检视频地址
   */
  videoAddress: string;
  /**
   * 巡检描述
   */
  videoDesc: string;
  /**
   * 巡检视频ID
   */
  videoId: string;
  /**
   * 巡检视频名称
   */
  videoName: string;
  /**
   * 设备标号
   */
  videoNum: string;
  /**
   * 巡检视频结果 0：正常，1：待巡检，2：异常
   */
  videoResult: number;
  /**
   * 巡检视频结果 0：正常，1：待巡检，2：异常
   */
  videoResultName: string;
  /**
   * 巡检视频排序
   */
  videoSort: number;
  /**
   * 巡检时间
   */
  videoTime: string;
  /**
   * 巡检图片
   */
  videoUrl: string;
  /**
   * 租户id
   */
  zhId: string;
}

export type ITaskInspectionPageRes = IPageRes<ITaskInspectionPageItem>;

export interface ITaskInspectListByFloorItem {
  floorName: string;
  floorId: string;
  deviceList: ITaskInspectionDetailPageItem[];
}
