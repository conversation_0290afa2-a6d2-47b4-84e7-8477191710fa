<template>
  <div :class="$style.InspectTaskListItem" class="com-g-row-a1">
    <n-ellipsis :class="$style.title">
      {{ data.planName }}
    </n-ellipsis>

    <div class="com-g-row-1a">
      <template v-if="data.taskStatus === 0 || data.taskStatus === 1">
        <div :class="$style.noStart"></div>
      </template>
      <template v-else>
        <n-image-group :render-toolbar="renderCustomToolbar">
          <div :class="$style.imageGroup">
            <div v-for="i in 6" :key="i">
              <template v-if="data.videoUrls && data.videoUrls.length > 6 && i === 6">
                <div class="n-image" :class="$style.more" @click="emits('toDetail', data)">
                  {{ data.videoUrls.length - 6 }}+
                </div>
              </template>
              <template v-else-if="data.videoUrls && data.videoUrls[i - 1]">
                <n-image
                  :src="getFullThumbnailUrl(data.videoUrls[i - 1], '126x90')"
                  :preview-src="getFullFileUrl(data.videoUrls[i - 1])"
                  alt=""
                />
              </template>
              <template v-else>
                <n-image :src="itemNoPic" alt="" preview-disabled class="pointer-events-none" />
              </template>
            </div>
          </div>
        </n-image-group>
      </template>

      <div class="text-[14px] pt-[10px]">
        <div class="mb-[5px]">
          <p>巡检时间：{{ getFormatTimeRange(data.taskPlanStartTime, data.taskPlanEndTime) }}</p>
          <div class="my-[5px]">
            <n-progress
              type="line"
              :processing="data.taskStatus == 2"
              color="#1C6FFF"
              indicator-text-color="#fff"
              indicator-placement="inside"
              :percentage="data.progressNum || 0"
            />
          </div>
        </div>
        <n-flex justify="space-between" align="center">
          <div>
            <span class="text-[#f32e2e]">异常情况：{{ getAbnormalDisplay() }}</span>
            <span class="text-[#00b2ff] ml-[20px]">视频点位：{{ data.videoDeviceNum }}个</span>
          </div>
          <n-button type="primary" size="small" @click="emits('toDetail', data)">查看详情</n-button>
        </n-flex>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { IPageItem } from '../type.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl';
import itemNoPic from '../assets/item-no-pic.png';
import { getFormatTimeRange } from '@/utils/format.ts';

const props = defineProps({
  data: {
    type: Object as () => IPageItem,
    default: () => {},
  },
});
const emits = defineEmits(['toDetail']);

function getAbnormalDisplay() {
  // 巡检状态，0：待开始，1：未执行/逾期，2：进行中，3：已完成
  // 如果是 2、3 则显示具体数字 否则 --
  if (props.data.taskStatus == 2 || props.data.taskStatus == 3) {
    return `${props.data.abnormalNum || 0}个`;
  }

  return '--';
}

defineOptions({ name: 'InspectTaskListItem' });
</script>

<style module lang="scss">
.InspectTaskListItem {
  .title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    max-width: 100%;
  }

  .imageGroup {
    display: grid;
    grid-template-columns: repeat(3, minmax(86px, 1fr));
    grid-template-rows: repeat(2, minmax(50px, 140px));
    gap: vw(10);
    justify-content: center;
    justify-items: center;

    > div {
      display: flex;
      justify-content: center;
      align-items: center;
      aspect-ratio: 1.4/1;
      border-radius: 2px;
      overflow: hidden;
    }

    img {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.15);
    }

    .more {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      border: 1px solid var(--skin-bd1);
    }
  }

  .noStart {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: url('../assets/item-no-start.png') 0 0 no-repeat;
    background-size: 100% 100%;
  }
}
</style>
