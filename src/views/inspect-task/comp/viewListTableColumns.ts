import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '楼层',
    key: 'floorName',
    align: 'center',
  },
  {
    title: '位置',
    key: 'deviceAddress',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '巡检状态',
    key: 'videoResultName',
    align: 'center',
  },
  {
    title: '巡检时间',
    key: 'videoTime',
    align: 'center',
    width: 200,
  },
  {
    title: '巡检结果',
    key: 'eventTypeName',
    align: 'center',
  },
  {
    title: '处置状态',
    key: 'disposeStatusName',
    align: 'center',
  },
];
