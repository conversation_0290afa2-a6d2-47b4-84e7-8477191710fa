<template>
  <div :class="[$style.InspectTaskViewMap, 'com-g-col-a1']">
    <div :class="[$style.areaLeft, 'com-g-row-a1']">
      <Cards class="justify-center" />

      <n-scrollbar content-class="h-full" :style="{ maxHeight: '500px' }">
        <InspectList :list-data="listData" />
      </n-scrollbar>
    </div>

    <div :class="$style.areaRight">
      <FloorGis :type="gisType" :build-id="buildId" :floor-id="floorId" :default-inspect-list="inspectList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Cards from './InfoCardSmall.vue';
import InspectList from './ViewMapInspectList.vue';
import FloorGis from '@/gis-floor/floorGis.vue';
import { getDeviceListByTask } from '../fetchData';
import { ITaskInspectListByFloorItem, ITaskInspectionDetailPageItem } from '../type';
import { useRoute } from 'vue-router';
import { EGisType } from '@/gis-floor/constant';
import { IDeviceRow } from '@/gis-floor/type';

defineOptions({ name: 'InspectTaskViewMap' });

const gisType = EGisType.INSPECTDETAIL;

const route = useRoute();

const listData = ref<ITaskInspectListByFloorItem[]>([]);

const inspectList = computed<ITaskInspectionDetailPageItem[]>(() => {
  const _list = listData.value.reduce((preValue: ITaskInspectionDetailPageItem[], item) => {
    return [...preValue, ...item.deviceList];
  }, []);

  const _inspectList = _list.map((item) => ({
    ...item,
    deviceId: item.videoId,
    deviceName: item.videoName,
  }));
  return _inspectList;
});
const buildId = computed(() => inspectList.value[0]?.buildingId);
const floorId = computed(() => inspectList.value[0]?.floorId);

const getData = () => {
  getDeviceListByTask({
    pageNo: 1,
    pageSize: -1,
    taskId: route.query.taskId,
  }).then((res) => {
    if (res.data.length > 0) {
      listData.value = res.data || [];
    }
  });
};
getData();
</script>

<style module lang="scss">
.InspectTaskViewMap {
  width: 100%;
  height: 100%;
  column-gap: 20px;

  .areaLeft {
    width: 372px;
    border: 1px solid var(--skin-bd2);
    background-color: var(--skin-bg2);
    gap: 24px;
    padding-top: 24px;
  }

  .areaRight {
    // border: 1px solid var(--skin-bd2);
    // background-color: var(--skin-bg2);
  }
}
</style>
