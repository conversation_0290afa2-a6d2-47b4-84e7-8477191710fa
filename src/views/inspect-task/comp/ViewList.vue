<template>
  <div :class="$style.InspectTaskViewList">
    <Filter @action="actionFn" />

    <n-data-table
      :class="$style.table"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :render-cell="useEmptyCell"
    />

    <!--  导出按钮  -->
    <n-button type="primary" :class="$style.exportBtn" @click="handleExport" :loading="exportLoading">导出</n-button>

    <!--  弹窗  -->
    <InspectInfoDia v-model:show="showDia" :data="inspectInfoDiaData" />
  </div>
</template>

<script setup lang="ts">
import Filter from './ViewListFilter.vue';
import { cols } from './viewListTableColumns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { getTaskInspectionPage as pageList, getTaskInspectionPageExport as pageExport } from '../fetchData.ts';
import { IObj } from '@/types';
import { IActionData, ITaskInspectionPageItem } from '@/views/inspect-task/type.ts';
import { ref, h, VNode } from 'vue';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { ACTION } from '@/views/inspect-task/constant.ts';
import InspectInfoDia from '@/views/inspect-com/InspectInfoDia.vue';
import { useStore } from '@/store';

const [loading, search] = useAutoLoading(false);
const [exportLoading, exportWrapFn] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<ITaskInspectionPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);
const showDia = ref(false);
const inspectInfoDiaData = ref({});
const ui = useStore();

let filterData: IObj<any> = {}; // 搜索条件

function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    getTableDataWrap(val.data);
  }
}

function handleShowDetailDia(data: any) {
  showDia.value = true;
  inspectInfoDiaData.value = data;
}

function handleExport() {
  exportWrapFn(pageExport(filterData, ui.userInfo.token || ''));
}

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleShowDetailDia(row),
        },
        { default: () => '巡检详情' }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// init
setColumns();

defineOptions({ name: 'InspectTaskViewList' });
</script>

<style module lang="scss">
.InspectTaskViewList {
  position: relative;
  row-gap: 20px;
  padding: 0;

  .table {
    height: calc(100vh - 500px);
  }

  .exportBtn {
    position: absolute;
    right: 0;
    top: -54px;
    width: 80px;
  }
}
</style>
