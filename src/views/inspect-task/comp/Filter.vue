<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="4" label="计划名称">
          <n-input v-model:value="filterForm.planName" maxlength="100" clearable />
        </n-form-item-gi>

        <n-form-item-gi :span="6" label="巡检时间">
          <n-date-picker
            class="w-full"
            v-model:value="filterForm.rangeDateTime"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item-gi>

        <n-form-item-gi label="任务状态" :span="4">
          <n-select
            v-model:value="filterForm.taskStatus"
            :options="rwztOpt"
            label-field="dictLabel"
            value-field="dictValue"
            clearable
          />
        </n-form-item-gi>

        <n-form-item-gi label="巡检结果" :span="4">
          <n-select
            v-model:value="filterForm.videoResult"
            :options="xjspjgOpt"
            label-field="dictLabel"
            value-field="dictValue"
            clearable
            placeholder="全部"
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { ACTION } from '../constant';
import { formatTimestamp } from '@/utils/format.ts';
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { IObj } from '@/types';
import { $dict } from '@/views/common/dict/dict.ts';

const props = defineProps({});
const emits = defineEmits(['action']);

const filterForm = ref(initForm());
const { rwztOpt } = $dict.useRwzt();
const { xjspjgOpt } = $dict.useXjspjg(['0', '2']);

function initForm() {
  return {
    unitId: '', // 单位id
    planId: '', // 计划id
    planName: '', // 计划名称
    taskStatus: null, // 巡检状态
    videoResult: null, // 巡检视频结果
    rangeDateTime: <null | [number, number]>null, // 时间
  };
}

function getFilterForm() {
  const { rangeDateTime } = filterForm.value;

  return trimObjNull(
    Object.assign({}, filterForm.value, {
      rangeDateTime: null,
      taskStartTime: rangeDateTime ? formatTimestamp(rangeDateTime[0]) : null, // 开始时间
      taskEndTime: rangeDateTime ? formatTimestamp(rangeDateTime[1]) : null, // 结束时间
    })
  );
}

function doHandle(action: ACTION, extData?: IObj<any>) {
  emits('action', {
    action: action,
    data: Object.assign(getFilterForm(), extData),
  });
}

function reset() {
  filterForm.value = initForm();
}

// onMounted(() => {
//   doHandle(ACTION.SEARCH);
// });

watch(
  () => filterForm.value,
  () => {
    doHandle(ACTION.SEARCH);
  },
  { deep: true }
);

defineExpose({
  reset,
  emit(data: IObj<any>, propsData: IObj<any>, source?: string) {
    // 来自卡片
    if (source === 'InfoCardTask') {
      filterForm.value.taskStatus = data.taskStatus;
      return;
    }

    // 模式不同数据来源有区分
    if (propsData.pageMode === 'NARROW') {
      filterForm.value.unitId = propsData.unitId;
      filterForm.value.planId = propsData.planId;
    } else {
      filterForm.value.unitId = data.id;
    }
  },
});

defineOptions({ name: 'InspectTaskFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
