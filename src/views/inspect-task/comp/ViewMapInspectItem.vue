<template>
  <div :class="$style['inspect-item']">
    <div :class="$style['header']">
      <span :class="$style['index']">{{ info.videoSort }}.</span>
      <n-ellipsis :class="$style['title']">
        {{ info.deviceAddress || '--' }}
      </n-ellipsis>
      <span :class="[$style['status'], getStatusClass(info.videoResult)]">
        {{ info.videoResultName || '--' }}
      </span>
    </div>

    <div v-if="info.videoResult == 2 && info.eventTypeNames" :class="$style['eventNames']" @click="eventHandle">
      <n-ellipsis>
        {{ info.eventTypeNames }}
      </n-ellipsis>
    </div>

    <div :class="$style.time">巡检时间：{{ info.videoTime || '--' }}</div>

    <div v-if="info.videoDesc" :class="$style.imageWrap">
      <n-image
        class="w-full h-full"
        :render-toolbar="renderCustomToolbar"
        :src="getFullThumbnailUrl(info.videoDesc, '135x80')"
        :preview-src="getFullFileUrl(info.videoDesc)"
        alt=""
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { VIDEO_RESULT_STATUS_CLASS_MAP } from '@/views/common/contant.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { ITaskInspectionDetailPageItem } from '../type';

defineOptions({ name: 'ViewMapInspectItem' });

interface IProps {
  info: Partial<ITaskInspectionDetailPageItem>;
}

const props = withDefaults(defineProps<IProps>(), {
  info: () => ({}),
});

const getStatusClass = (videoResult: number | undefined) => {
  const _res = videoResult || 0;
  return VIDEO_RESULT_STATUS_CLASS_MAP[_res];
};

const eventHandle = () => {
  // toDo 打开事件弹框
};
</script>

<style module lang="scss">
.inspect-item {
  @apply flex flex-col gap-[10px] mb-[20px] text-[14px];
  line-height: 20px;
  color: var(--skin-t1);
}

.header {
  @apply flex flex-row flex-nowrap items-center;

  .title {
    @apply flex-1 ml-[4px] mr-[8px];
  }
}

.eventNames {
  cursor: pointer;
  color: #fa5151;
  overflow: hidden;
}

.time {
  width: 100%;
  height: 28px;
  line-height: 28px;
  font-size: 14px;
  padding: 0 7px;
  backdrop-filter: blur(3px);
}

.imageWrap {
  display: flex;
  justify-content: center;
  margin-left: 12%;

  img {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.15);
  }
}
</style>
