<template>
  <div class="com-g-row-a1">
    <div v-if="pageMode === 'NARROW'" />
    <ComBread v-else :data="breadData" />

    <div class="com-g-col-a1">
      <div v-if="pageMode === 'NARROW'" />
      <ComOrgTreeWrap v-else @change="handleRefreshComp" />

      <div class="com-g-row-aa1">
        <InfoCardTask ref="infoCardTaskCompRef" @change="handleInfoCardTaskChange" />
        <Filter ref="filterCompRef" @action="actionFn" />
        <List class="com-table-container min-h-0" ref="listCompRef" @action="actionFn" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import Filter from './comp/Filter.vue';
import InfoCardTask from './comp/InfoCardTask.vue';
import List from './comp/List.vue';
import { ACTION, PROVIDE_KEY } from './constant.ts';
import { IActionData } from './type.ts';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { IObj } from '@/types';
import { nextTick, provide, Ref, ref } from 'vue';

const props = defineProps({
  pageMode: String as () => 'NARROW', // 页面模式
  planId: String,
  unitId: String,
});

const breadData: IBreadData[] = [{ name: '智能巡检' }, { name: '巡检任务管理' }];
const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} });
const filterCompRef = ref();
const listCompRef = ref();
const infoCardTaskCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    listCompRef.value?.getDataWrap(data);
  } else {
    listCompRef.value?.getData();
  }
}

function handleRefreshComp(data?: IObj<any>) {
  infoCardTaskCompRef.value?.emit(data, props);
  filterCompRef.value?.emit(data, props);
}

function handleInfoCardTaskChange(data: IObj<any>) {
  filterCompRef.value?.emit(data, props, 'InfoCardTask');
}

// init
nextTick(() => {
  // 如果是 narrow 模式， 主动刷新数据， 否则通过 orgTree 变化触发刷新
  if (props.pageMode === 'NARROW') {
    handleRefreshComp();
  }
});

defineOptions({ name: 'InspectTaskIndex' });
</script>

<style module lang="scss"></style>
