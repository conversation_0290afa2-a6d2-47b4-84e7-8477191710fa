import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import {
  IInspectTaskOverViewStatistics,
  IPageListRes,
  ITaskInspectionDetailPageListRes,
  ITaskInspectionDetailStatistic,
  ITaskInspectionPageRes,
  ITaskTopDetail,
  ITaskInspectListByFloorItem,
} from './type.ts';
import { fileDownloader } from '@/utils/fileDownloader.ts';

export function pageList(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.intelligent.inspectTaskPagelist, query);

  return $http.post<IPageListRes>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

export function getStatistics(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.inspectTaskOverViewStatistics, query);

  return $http.get<IInspectTaskOverViewStatistics>(url, { data: { _cfg: { showTip: true } } });
}

export function getTaskTopDetail(query: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.taskTopDetail, query);

  return $http.get<ITaskTopDetail>(url, { data: { _cfg: { showTip: true } } });
}

export function getTaskInspectionDetailStatistic(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.intelligent.taskInspectionDetailStatistic, query);

  return $http.post<ITaskInspectionDetailStatistic>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

export function getTaskInspectionDetailPageList(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.intelligent.taskInspectionDetail, query);

  return $http.post<ITaskInspectionDetailPageListRes>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

export function getTaskInspectionPage(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.intelligent.taskInspectionPage, query);

  return $http.post<ITaskInspectionPageRes>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

export function getTaskInspectionPageExport(query: IObj<any>, token: string) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.taskInspectionPageExport, query);

  return fileDownloader(url, {
    headers: [{ name: 'token', value: token }],
  });
}

export function getFloorList(query: { unitId: string; buildId: string }) {
  const url = api.getUrl(api.type.intelligent, api.name.intelligent.getFloorList, { ...query });

  return $http.get<{ floorId: string; floorName: string }[]>(url, { data: { _cfg: { showTip: true } } });
}

export function getDeviceListByTask(params: any) {
  const { url, data } = api.getComParams(api.type.intelligent, api.name.intelligent.getDeviceListByTask, params);
  return $http.post<ITaskInspectListByFloorItem[]>(url, {
    data: { _cfg: { showTip: true }, ...data },
  });
}
