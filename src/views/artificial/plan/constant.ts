import type { ICardAItem } from '@/components/card/type';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EDIT = 'EDIT',
  MORE = 'MORE',
  ADD = 'ADD',
  DEL = 'DEL',
  USING = 'USING',
  STOPUSING = 'STOPUSING',
  TREECHANGE = 'TREECHANGE',
  CARDCHANGE = 'CARDCHANGE',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAILS]: '详情',
  [ACTION.EDIT]: '编辑',
  [ACTION.MORE]: '更多',
  [ACTION.ADD]: '新建计划',
  [ACTION.DEL]: '删除',
  [ACTION.USING]: '启用',
  [ACTION.STOPUSING]: '停用',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片筛选',
};

//planState  "检查状态(1:未发布;2:进行中;3:已结束;4:已停用)")
export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: 0,
  },
  {
    label: '使用中（个）',
    value: 0,
    id: 2,
  },
  {
    label: '待启用（个）',
    value: 0,
    id: 1,
  },
  {
    label: '已停用（个）',
    value: 0,
    id: 4,
  },
  {
    label: '已结束（个）',
    value: 0,
    id: 3,
  },
];

//计划状态 "检查状态(1:未发布;2:进行中;3:已结束;4:已停用)")
export const PLAN_STATE = {
  LAB: ['', '待启用', '使用中', '已结束', '已停用'],
  COLOR: ['', '#F39600', '#527CFF', '#B2B2B2', '#FA5151'],
};

export const iFrequencyType = ['小时', '日', '周', '月', '季度', '年'];
