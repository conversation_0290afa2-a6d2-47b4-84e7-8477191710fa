import type { IPageDataRes, IDetail, IPlanNum, ISubmit } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//列表
export function planList(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.pagePlan, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}
//统计
export function planRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.recordPlan, query);
  return $http.get<IPlanNum>(url, { data: { _cfg: { showTip: true } } });
}
//详情
export function planDetailApi(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.infoPlan, query);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
