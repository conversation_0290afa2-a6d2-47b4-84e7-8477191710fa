<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="flexHeight"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
    :scroll-x="1165"
  />
</template>

<script lang="ts" setup>
import type { IDetail } from '../../type';
import { ACTION, ACTION_LABEL, PLAN_STATE } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton } from 'naive-ui';
import { h, ref, toRaw, VNode, computed } from 'vue';
import { planList } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(false);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
const flexHeight = computed(() => {
  return tableData.value.length === 0 ? false : true;
});

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };
  search(planList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
// planState  "检查状态(1:未发布;2:进行中;3:已结束;4:已停用)")  ,
function getTableDataWrap(data: IObj<any>) {
  loading.value = true;
  filterData =
    Object.assign(
      {},
      { deptId: data.deptId, deptName: data.deptName, planName: data.planName, planState: data.planStatus }
    ) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);
  //1:未发布;2:进行中;3:已结束;4:已停用
  columns.value.push({
    title: '计划状态',
    key: 'planState',
    align: 'left',
    width: 120,
    render(row) {
      const index = row.planState as number;
      return h(
        NButton,
        {
          size: 'small',
          color: PLAN_STATE.COLOR[index],
        },
        PLAN_STATE.LAB[index]
      );
    },
  });
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    align: 'left',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () => emits('action', { action: ACTION.DETAILS, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
