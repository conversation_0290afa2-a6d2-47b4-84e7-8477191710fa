<template>
  <!-- 状态结果详情 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <com-loading v-if="loading"></com-loading>
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="../../../assets/edit-icon.png" alt="" />
          <span class="header-title">计划详情</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <div class="card-item">
          <div class="lab">所属单位：</div>
          <div class="val">{{ unitName }}</div>
        </div>
        <div class="card-item">
          <div class="lab">计划名称：</div>
          <div class="val">{{ detailData?.planName || '--' }}</div>
        </div>
        <div class="card-item">
          <div class="lab">起止日期：</div>
          <div class="val">
            {{ ymrDateFn(detailData?.planStartDate as string) }} 至 {{ ymrDateFn(detailData?.planEndDate as string) }}
          </div>
        </div>
        <div class="card-item">
          <div class="lab">巡检频次：</div>
          <div class="val">{{ frequency }}</div>
        </div>
        <div class="card-item">
          <div class="lab">巡检点位：</div>
          <div class="val">
            <p v-for="point in detailData?.checkPointList" :key="point.id" style="margin-bottom: 10px">
              {{ point.location }}
            </p>
          </div>
        </div>
        <div class="card-item">
          <div class="lab">最近编辑人员：</div>
          <div class="val">{{ detailData?.updateByName || '--' }}</div>
        </div>
        <div class="card-item">
          <div class="lab">最近编辑时间：</div>
          <div class="val">{{ detailData?.updateTime || '--' }}</div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { planDetailApi } from '../../fetchData';
import type { IDetail } from '../../type';
import type { FormInst, CascaderOption } from 'naive-ui';
import ComLoading from '@/components/loading/comLoading.vue';
import { ACTION, iFrequencyType } from '../../constant';
import { ymrDateFn } from '@/views/common/utils.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(true);
const emits = defineEmits(['action']);
const active = ref(false);

const detailData = ref<IDetail>();

function open(id: string) {
  active.value = true;
  getDetails(id);
}
function close() {
  active.value = false;
  detailData.value = {} as IDetail;
}

function getDetails(id: string | number) {
  run(planDetailApi({ planId: id })).then((res) => {
    detailData.value = res.data;
  });
}

const frequency = computed(() => {
  const fT = detailData.value?.frequencyType as number;
  const f = detailData.value?.frequency;
  return fT == 99 ? '不重复' : `每${f || '--'}${iFrequencyType[fT] || '--'}一次`;
});
const unitName = computed(() => {
  let name = '';
  if (detailData.value?.unitList) {
    detailData.value?.unitList.forEach((item: any) => {
      name = name + item.unitName;
    });
  }
  return name || '--';
});

defineExpose({
  open,
  close,
});

defineOptions({ name: 'artificialPlanDetail' });
</script>

<style lang="scss">
.com-detail-drawer {
  .plan-frequency {
    margin-left: 10px;
  }
}
</style>
