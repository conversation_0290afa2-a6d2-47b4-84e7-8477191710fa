import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export type IPageDataRes = IPageRes<IDetail>;

export type IRelVideos = {
  deviceId: string;
  deviceName: string;
  deviceSort: number;
  id: string;
  planId: string;
};

// 分页列表数据
export interface IDetail {
  /**
   * 是否允许编辑(1:允许编辑；2：不允许编辑)
   */
  allowUpdate: string;

  /**
   * 检查要求
   */
  checkDemand: string;

  /**
   * 检查结束操作(1:是;2:否)
   */
  checkEndOperate: string;

  /**
   * 检查执行方式(1:不需要逐一执行每个检查项;2:需要逐一执行每个检查项)
   */
  checkExecuteMethod: string;

  /**
   * 检查点位集合
   */
  checkPointList: Point[];

  /**
   * 检查范围（1：区域；2：设备；3：点位）
   */
  checkRange: string;

  /**
   * 检查表id
   */
  checkTableId: string;

  /**
   * 检查表名称
   */
  checkTableName: string;

  /**
   * 设备/点位检查表设置（1：使用设备/点位检查表；2：使用统一检查表）
   */
  checkTableSet: string;

  /**
   * 创建人
   */
  createBy: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 创建单位id
   */
  createUnitId: string;

  /**
   * 删除标志，number:未删除，1:已删除
   */
  delFlag: number;

  /**
   * 设备集合
   */
  deviceList: [];

  /**
   * 检查时段: 结束时间
   */
  endTime: string;

  /**
   * 附件集合
   */
  fileList: [];
  /**
   * 频次
   */
  frequency: string;

  /**
   * 计划频次类型：number:时;1:日;2:周;3:月;4:季度;5:年;99:不重复
   */
  frequencyType: number;

  /**
   * 是否需要检查人现场打卡(1:是;2:否)
   */
  isNeedClock: string;

  /**
   * 计划结束日期
   */
  planEndDate: string;

  /**
   * 计划id
   */
  planId: string;

  /**
   * 计划名称
   */
  planName: string;

  /**
   * 计划开始日期
   */
  planStartDate: string;

  /**
   * 检查状态(1:未发布;2:进行中;3:已结束;4:已停用)
   */
  planState: string;

  /**
   * 计划类型(1:督察检查;2:专项检查;3:自行检查)
   */
  planType: string;

  /**
   * 检查时段: 开始时间
   */
  startTime: string;

  /**
   *单位集合
   */
  unitList: Unit[];
  /**
   * 修改人
   */
  updateBy: string;

  /**
   * 修改时间
   */
  updateTime: string;

  /**
   * 检查人员集合
   */
  userList: [];
  /**
   * 租户id
   */
  zhId: string;
  updateByName: string;
}

export interface Point {
  /**
   * 主键,存储 UUID
   */
  id: string;
  /**
   * 检查表id
   */
  checkTableId: string;
  /**
   * 二维码名称
   */
  qrCodeName: string;
  /**
   * 二维码 URL
   */
  qrCodeUrl: string;
  /**
   * 点位 ID
   */
  pointId: string;

  buildingFloor: string;

  /**
   * 楼栋
   */
  buildingId: string;

  /**
   * 楼栋
   */
  building: string;

  /**
   * 楼栋
   */
  floorId: string;

  /**
   * 楼层
   */
  floor: string;
  /**
   * 位置
   */
  location: string;

  /**
   * 经度
   */
  longitude: string;

  /**
   * 纬度
   */
  latitude: string;
  /**
   * 是否重点部位，0:否，1:是
   */
  isKeyArea: boolean;
  /**
   * 责任人
   */
  responsiblePerson: string;
  /**
   * 电话
   */
  phoneNumber: string;
  /**
   * 备注
   */
  remark: string;
  /**
   * 创建人
   */
  createBy: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 修改人
   */
  updateBy: string;
  /**
   * 修改时间
   */
  updateTime: string;

  zhId: string;

  // checkPointContentList: HazardCheckPointContent[];
}
/**
 * 	检查计划单位
 */
export interface Unit {
  id: string;
  planId: string;
  superviseUnitId: string;
  superviseUnitName: string;
  unitId: string;
  unitName: string;
}
//统计
export interface IPlanNum {
  activePlanCount: number; //使用中计划数
  completedPlanCount: number; //已结束计划数
  disabledPlanCount: number; //已停用计划数
  pendingPlanCount: number; //待发布计划数
  planTotal: number; //计划总数
}

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export interface IDevice {
  deviceId: string;
  deviceName: string;
  deviceSort: string;
}
export interface ISubmit {
  deptId: string | number | null;
  deptIds: string | number | null;
  deptName: string | number | null;
  id?: string | number | null;
  planStartDate: number | string | null;
  planEndDate: number | string | null;
  planFrequency: string | number | null; //计划巡检频次
  planFrequencyUnit: number | string | null; //计划巡检频次单位 1：小时，2：天，3：周，4：月，5：季度，6：年
  planName: string | null;
  planType: number | string; //计划类型 number：重复 1：不重复
  relVideos: any[];
}
