<template>
  <div :class="[$style.videoEqui]" class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="treeAct">
      <div class="layer-strenth" v-if="store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <com-card :list="cardList" :act="planStatus" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
    </div>
    <detail @action="actionFn" ref="detailRef" />
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, CARDLIST } from './constant';
import type { IActionData } from './type';
import ComCard from '@/components/card/ComCardA.vue';
import Detail from './comp/detail/Detail.vue';
import type { ICardAItem } from '@/components/card/type';
import { planRecordApi } from './fetchData';
import strengthImage from '@/assets/strenth.png';
import { useStore } from '@/store';

const store = useStore();

//树结构--操作
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
});
const treeAct = ref<any>(null);
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    treeData.value = [...(v as TreeOption[])];
    if (treeData.value.length) {
      treeAct.value = treeData.value[0];
      listRecord(treeAct.value.id);
    }
  },
  {
    immediate: true,
  }
);

function treeChange(v: TreeOption) {
  console.log('接收到tree v', v.id);
  treeAct.value = v;
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id, deptName: v.text } });
}
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });

const tableCompRef = ref();
const detailRef = ref();
const cardList = ref<ICardAItem[]>([...CARDLIST]);

const planStatus = ref(2);
//统计
function listRecord(id?: string) {
  const params = {
    deptId: id || '',
  };
  planRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.planTotal;
    cardList.value[1].value = res.data.activePlanCount;
    cardList.value[2].value = res.data.pendingPlanCount;
    cardList.value[3].value = res.data.disabledPlanCount;
    cardList.value[4].value = res.data.completedPlanCount;
  });
}

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    deptId: treeAct.value?.id ? treeAct.value.id : '',
    deptName: treeAct.value?.text ? treeAct.value.text : '',
    planStatus: planStatus.value == 0 ? '' : planStatus.value,
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      listRecord(currentAction.value.data.deptId);
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片筛选');
      const cardId = currentAction.value.data.cardId;
      planStatus.value = cardId;
      currentAction.value.data.planStatus = cardId == 0 ? '' : cardId;
      handleSearch(currentAction.value.data);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      detailRef.value.open(currentAction.value.data.planId);
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'VideoPlanIndex' });
</script>

<style module lang="scss">
.videoEqui {
  width: 100%;
  height: 100%;
}
</style>
