import type { IPageDataRes, IDetail, ITaskNum } from './type';
import { $http } from '@tanzerfe/http';

import { api } from '@/api';
import { IObj } from '@/types';

//列表
export function taskListApi(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.pageTask, query);
  return $http.get<IPageDataRes>(url, { data: { _cfg: { showTip: true } } });
}
//统计
export function taskRecordApi(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.recordTask, query);
  return $http.get<ITaskNum>(url, { data: { _cfg: { showTip: true } } });
}
//详情
export function taskDetailApi(query: IObj<any>) {
  const url = api.getUrl(api.type.artificial, api.artificial.detailTask, query);
  return $http.get<IDetail>(url, { data: { _cfg: { showTip: true } } });
}
