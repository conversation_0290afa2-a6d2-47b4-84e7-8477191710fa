<template>
  <div class="com-layer-le-ri com-layer-container">
    <div class="layer-le" v-if="store.userInfo.unitOrgType == '2'" :class="{ layerVisible: layerVisible }">
      <com-tree :data="treeData" @action="treeChange"></com-tree>
    </div>
    <div class="layer-ri" v-if="treeAct">
      <div class="layer-strenth" v-if="isShowIcon && store.userInfo.unitOrgType == '2'" @click="layerShow">
        <img :src="strengthImage" />
      </div>
      <com-card :list="cardList" :act="taskStatus" @action="actionFn"></com-card>
      <div class="layer-ri-inner">
        <com-filter class="com-table-filter" @action="actionFn" />
        <div class="layer-cont">
          <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
        </div>
      </div>
    </div>
  </div>
  <task-detail ref="taskDetailRef" @action="actionFn"></task-detail>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, watch } from 'vue';
import ComTree from '@/components/tree/index.vue';
import ComFilter from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import TaskDetail from './details/Task.vue';
import { ACTION, CARDLIST } from './constant';
import type { IActionData } from './type';
import ComCard from '@/components/card/ComCardA.vue';
import type { ICardAItem } from '@/components/card/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { taskRecordApi } from './fetchData';
import strengthImage from '@/assets/strenth.png';
import { useStore } from '@/store';

const store = useStore();

const [loading, run] = useAutoLoading(false);
const emits = defineEmits(['changeBread']);
//树结构--操作
const props = defineProps({
  treeList: {
    type: Array,
    default: () => [],
  },
});
const treeAct = ref<any>(null);
const isShowIcon = ref(true);
const treeData = ref<TreeOption[]>([]);
watch(
  () => props.treeList,
  (v) => {
    treeData.value = [...(v as TreeOption[])];
    if (treeData.value.length) {
      treeAct.value = treeData.value[0];
      listRecord(treeAct.value.id);
    }
  },
  {
    immediate: true,
  }
);

function treeChange(v: TreeOption) {
  console.log('接收到tree v', v.id);
  treeAct.value = v;
  actionFn({ action: ACTION.TREECHANGE, data: { deptId: v.id, deptName: v.text } });
}
const layerVisible = ref(false);
function layerShow() {
  layerVisible.value = !layerVisible.value;
}
const currentAction = ref<IActionData>({ action: ACTION.NONE, data: {} });

const taskDetailRef = ref();
const tableCompRef = ref();

const taskStatus = ref(3);
const cardList = ref<ICardAItem[]>([...CARDLIST]);
//统计
function listRecord(id?: string) {
  const params = {
    deptId: id || '',
  };
  taskRecordApi(params).then((res) => {
    cardList.value[0].value = res.data.taskTotal;
    cardList.value[1].value = res.data.inProgressTaskCount;
    cardList.value[2].value = res.data.pendingTaskCount;
    cardList.value[3].value = res.data.completedTaskCount;
    cardList.value[4].value = res.data.overdueTaskCount;
  });
}

function actionFn(val: IActionData) {
  console.log(val, currentAction.value, 'actionList-----');
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    taskState: taskStatus.value == 0 ? '' : taskStatus.value,
    deptId: treeAct.value?.id || '',
    deptName: treeAct.value?.text || '',
    ...val.data,
  };
  switch (val.action) {
    case ACTION.SEARCH:
      console.log('搜索');
      handleSearch(currentAction.value.data);
      break;
    case ACTION.TREECHANGE:
      console.log('树结构选择');
      handleSearch(currentAction.value.data);
      listRecord(currentAction.value.data.deptId);
      break;
    case ACTION.DETAILS:
      console.log('详情');
      isShowIcon.value = false;
      layerVisible.value = true;
      taskDetailRef.value?.open(val.data.idD, val.data.taskStutasD);
      emits('changeBread', { text: '任务详情', isAdd: true });
      break;
    case ACTION.EXPORT:
      console.log('导出');
      break;
    case ACTION.CARDCHANGE:
      console.log('卡片切换筛选');
      taskStatus.value = val.data.cardId;
      currentAction.value.data.taskState = val.data.cardId == 0 ? '' : val.data.cardId;
      handleSearch(currentAction.value.data);
      break;
    case ACTION.REFRESH:
      console.log(currentAction.value.data, '刷新当前页面');
      isShowIcon.value = true;
      layerVisible.value = false;
      emits('changeBread', { text: '任务详情', isAdd: false });
      break;
  }
}
function handleSearch(data?: Record<string, any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'ArtTaskIndex' });
</script>

<style module lang="scss"></style>
