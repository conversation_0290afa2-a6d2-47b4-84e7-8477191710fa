import { ACTION } from './constant';
import type { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据
export interface IDetail {
  taskId: string;
  taskName: string;

  /**
   * 计划id
   */
  planId: string;

  /**
   * 计划名称
   */
  planName: string;

  /**
   * 计划类型(1:督察检查:string;2:专项检查:string;3:自行检查)
   */
  planType: string;

  /**
   * 任务状态(1:待开始:string;2:进行中:string;3:已完成)
   */
  taskState: string;

  /**
   * 计划频次类型：0:时:string;1:日:string;2:周:string;3:月:string;4:季度:string;5:年:string;99:不重复
   */
  frequencyType: string;

  /**
   * 频次
   */
  frequency: string;

  /**
   * 任务计划开始日期
   */
  planStartTime: string;

  /**
   * 任务计划结束日期
   */
  planEndTime: string;
  /**
   * 任务计划开始日期
   */
  planBeginTime: string;
  /**
   * 任务计划结束日期
   */
  planOverTime: string;
  /**
   * 开始时间
   */
  beginTime: string;

  /**
   * 完成时间
   */
  finishTime: string;

  /**
   * 检查范围（1：区域；2：设备；3：点位）
   */
  checkRange: string;

  /**
   * 租户id
   */
  zhId: string;

  /**
   * 设备/点位检查表设置（1：使用设备/点位检查表；2：使用统一检查表）
   */
  checkTableSet: string;

  /**
   * 检查表id
   */
  checkTableId: string;

  /**
   * 检查表名称
   */
  checkTableName: string;

  /**
   * 检查执行方式(1:不需要逐一执行每个检查项:string;2:需要逐一执行每个检查项)
   */
  checkExecuteMethod: string;

  /**
   * 检查要求
   */
  checkDemand: string;

  /**
   * 检查结束操作(1:是:string;2:否)
   */
  checkEndOperate: string;

  /**
   * 是否需要检查人现场打卡(1:是:string;2:否)
   */
  isNeedClock: string;

  /**
   * 创建人
   */
  createBy: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 修改人
   */
  updateBy: string;

  /**
   * 修改时间
   */
  updateTime: string;

  /**
   * 删除标志，0:未删除，1:已删除
   */
  delFlag: number;

  /**
   * 创建单位id
   */
  createUnitId: string;

  /**
   * 发现异常数
   */
  essentialFactorNum: number;

  /**
   * 单位集合
   */
  unitlist: [];

  /**
   * 检查人员集合
   */
  userlist: [];

  /**
   * 设备集合
   */
  devicelist: [];

  /**
   * 点位集合
   */
  pointList: Point[];

  /**
   * 任务时效状态（1：正常；2：逾期）
   */
  timeState: string;
  /**
   * 任务状态
   */
  taskStatus: string;
}

export interface Point {
  checkPointContentList: [];
  checkState: number;
  planTaskPointList: [];

  /**
   * 主键,存储 UUID
   */
  id: string;
  /**
   * 检查表id
   */
  checkTableId: string;
  /**
   * 二维码名称
   */
  qrCodeName: string;
  /**
   * 二维码 URL
   */
  qrCodeUrl: string;
  /**
   * 点位 ID
   */
  pointId: string;

  buildingFloor: string;

  /**
   * 楼栋
   */
  buildingId: string;

  /**
   * 楼栋
   */
  building: string;

  /**
   * 楼栋
   */
  floorId: string;

  /**
   * 楼层
   */
  floor: string;
  /**
   * 位置
   */
  location: string;

  /**
   * 经度
   */
  longitude: string;

  /**
   * 纬度
   */
  latitude: string;
  /**
   * 是否重点部位，0:否，1:是
   */
  isKeyArea: boolean;
  /**
   * 责任人
   */
  responsiblePerson: string;
  /**
   * 电话
   */
  phoneNumber: string;
  /**
   * 备注
   */
  remark: string;
  /**
   * 创建人
   */
  createBy: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 修改人
   */
  updateBy: string;
  /**
   * 修改时间
   */
  updateTime: string;

  zhId: string;

  // checkPointContentList: HazardCheckPointContent[];
}

export interface ITaskNum {
  completedTaskCount: number;
  inProgressTaskCount: number;
  overdueTaskCount: number;
  pendingTaskCount: number;
  taskTotal: number;
}

export type IPageDataRes = IPageRes<IDetail>;

export type ITgeType = 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error';

export type IButType = 'default' | 'tertiary' | 'primary' | 'success' | 'info' | 'warning' | 'error';

export type IProgressStatus = 'default' | 'success' | 'error' | 'warning' | 'info';
