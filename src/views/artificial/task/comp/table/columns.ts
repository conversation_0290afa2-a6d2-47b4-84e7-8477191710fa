import { DataTableColumn } from 'naive-ui';
import { ymrDateFn } from '@/views/common/utils.ts';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '计划名称',
    key: 'planName',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '所属单位',
    key: 'createUnitName',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划开始日期',
    key: 'planStartTime',
    width: 180,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return `${ymrDateFn(row.planStartTime as string)}`;
    },
  },
  {
    title: '实际开始-结束时间',
    key: 'beginTime',
    width: 260,
    render(row) {
      return `${row.beginTime} ~ ${row.finishTime}`;
    },
  },
  {
    title: '发现异常数量',
    key: 'essentialFactorNum',
    width: 120,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.essentialFactorNum == '' ? '0' : row.essentialFactorNum;
    },
  },
];
