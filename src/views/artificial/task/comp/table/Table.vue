<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="flexHeight"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
    :scroll-x="1200"
  />
</template>

<script lang="ts" setup>
import type { IProgressStatus, IDetail } from '../../type';
import { ACTION, ACTION_LABEL, TASK_STATE } from '../../constant';
import { cols } from '../../comp/table/columns';
import { DataTableColumns, NButton, NProgress } from 'naive-ui';
import { h, ref, toRaw, VNode, computed } from 'vue';
import { taskListApi } from '../../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { IObj } from '@/types';
// import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const emits = defineEmits(['action']);
const flexHeight = computed(() => {
  return tableData.value.length === 0 ? false : true;
});

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IDetail[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(taskListApi(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, paramsFn(data)) || {};
  pagination.page = 1;
  getTableData();
}
//参数筛选
function paramsFn(data: IObj<any>) {
  const param = ['pageNo', 'pageSize', 'taskState', 'deptId', 'planName'];
  let curData = { ...data };
  //Object.keys(data).forEach
  for (let key in data) {
    if (!param.includes(key)) {
      delete curData[key];
    }
  }
  return curData;
}

function setColumns() {
  columns.value.push(...cols);
  //巡检进度
  // columns.value.splice(columns.value.length - 1, 0, {
  //   title: '巡检进度',
  //   key: 'progressNum',
  //   align: 'left',
  //   width: 160,
  //   render(row) {
  //     if (!row.progressNum && row.progressNum !== 0) {
  //       return h('span', {}, '--');
  //     }
  //     const curPro = row.progressNum as number;
  //     return h(NProgress, {
  //       color: curPro < 100 ? '#527CFF' : '#00B578',
  //       status: curPro < 100 ? 'default' : 'success',
  //       size: 'small',
  //       type: 'line',
  //       percentage: curPro,
  //       class: 'com-prog',
  //     });
  //   },
  // });
  //状态栏
  columns.value.push({
    title: '任务状态',
    key: 'taskState',
    align: 'left',
    width: 120,
    render(row) {
      const index = row.taskState as number;
      const timeState = row.timeState as number;
      if (timeState == 2) {
        return h(
          NButton,
          {
            size: 'small',
            color: TASK_STATE.COLOR[4],
          },
          TASK_STATE.LAB[4]
        );
      } else {
        return h(
          NButton,
          {
            size: 'small',
            color: TASK_STATE.COLOR[index],
          },
          TASK_STATE.LAB[index]
        );
      }
    },
  });

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          color: 'rgba(82,124,255,0.1)',
          size: 'small',
          class: 'com-action-button1',
          onClick: () =>
            emits('action', { action: ACTION.DETAILS, data: { idD: row.taskId, taskStutasD: row.taskState } }),
        },
        { default: () => ACTION_LABEL.DETAILS }
      ),
    ],
  ];
  return acList;
  //加线
  //return useActionDivider(acList);
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'VideoEquiTable' });
</script>

<style module lang="scss"></style>
