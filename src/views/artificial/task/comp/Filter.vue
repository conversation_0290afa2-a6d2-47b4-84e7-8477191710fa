<template>
  <n-form :show-feedback="false" label-placement="left">
    <div class="flex justify-between">
      <n-form-item label="计划名称:">
        <n-input placeholder="请输入计划名称" v-model:value="filterForm.planName" clearable class="!w-[260px]" />
      </n-form-item>
      <div class="w-[12%] flex justify-end">
        <n-button type="primary" @click="doHandle(ACTION.EXPORT)">
          {{ ACTION_LABEL.EXPORT }}
        </n-button>
      </div>
    </div>
  </n-form>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
const emits = defineEmits(['action']);
const filterForm = ref(initForm());

function initForm() {
  return {
    planName: '',
  };
}
function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'VideoTaskFilter' });
</script>

<style module lang="scss"></style>
