<template>
  <div class="task-detail-le">
    <div class="title">基本信息</div>
    <div class="cont">
      <div class="text">
        <div class="lab">所属单位：</div>
        <div class="val">{{ unitName }}</div>
      </div>
      <div class="text">
        <div class="lab">计划名称：</div>
        <div class="val">{{ detailData.planName || '--' }}</div>
      </div>
      <div class="text">
        <div class="lab">起止日期：</div>
        <div class="val">
          {{ ymrDateFn(detailData.planBeginTime) || '--' }}至{{ ymrDateFn(detailData.planOverTime) || '--' }}
        </div>
      </div>
      <div class="text">
        <div class="lab">巡检频次：</div>
        <div class="val">
          <span v-if="detailData.frequencyType === '99'">不重复</span>
          <span v-else
            >每{{ iFrequencyType[detailData.frequencyType] || '--' }}{{ detailData.frequency || '--' }}次</span
          >
        </div>
      </div>
      <div class="text">
        <div class="lab">巡检位置：</div>
        <div class="val">
          <div v-for="item in detailData.pointList" :key="item.id">
            {{ item.location }}
            <!-- {{ item.buildingFloor }} -->
            <!-- <n-tag
              :color="{ color: !item.checkState ? '#F39600' : colors[item.checkState - 1], textColor: '#fff' }"
              :bordered="false"
              class="com-table-tag"
              >{{ !item.checkState ? '未检查' : item.checkState === 1 ? '正常' : '异常' }}</n-tag
            > -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, Ref, ref, watch, inject } from 'vue';
import { DETAILS } from '../constant';
import { iFrequencyType } from '../../constant';
import { ymrDateFn } from '@/views/common/utils.ts';

const colors = ['#FA5151', '#00B578'];

const props = defineProps({
  data: {
    type: Object,
    defalut: () => DETAILS,
  },
});
const detailData = computed(() => {
  console.log('详情数据', JSON.stringify(props.data));
  return props.data ? props.data : DETAILS;
});
const unitName = computed(() => {
  let name = '';
  if (detailData.value.unitlist) {
    detailData.value.unitlist.forEach((item: any) => {
      name = name + item.unitName;
    });
  }
  return name || '--';
});

defineOptions({ name: 'VideoTaskDetailLe' });
</script>

<style lang="scss">
.task-detail-le {
  width: 434px;
  height: 100%;
  border-right: 1px solid #c8ced9;
  padding: 20px 24px;
  font-weight: 400;
  font-size: 14px;
  color: #222222;
  line-height: 20px;
  overflow-y: auto;
  .title {
    font-weight: 700;
  }
  .text {
    margin-top: 16px;
    display: flex;
    .lab {
      flex-shrink: 0;
    }
    p {
      margin-bottom: 8px;
    }
  }
}
</style>
