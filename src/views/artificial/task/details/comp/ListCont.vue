<template>
  <div class="list-cont">
    <n-grid :x-gap="0" :y-gap="0" :cols="props.cols">
      <n-grid-item>
        <div class="list-item">
          <div class="tips-le">{{ detailData.createTime }}</div>
          <div class="tips-ri">全部正常</div>
          <div class="text">6号集气站污水处理区域</div>
          <img src="" style="width: auto; height: 100%; margin: 0 auto" />
        </div>
      </n-grid-item>
      <n-grid-item>
        <div class="list-item">
          <div class="tips-le">2024-09-12 14:23:32</div>
          <div class="tips-ri">全部正常</div>
          <div class="text" @click="toDetail(1)">6号集气站污水处理区域</div>
          <div class="cont" @click="toDetail(1)">
            <img src="@/assets/noData.png" style="width: auto; height: 100%; margin: 0 auto" />
          </div>
        </div>
      </n-grid-item>
    </n-grid>
  </div>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, inject } from 'vue';
import { TASKACTION, DETAILS } from '../constant';
import type { IActionData } from '../type';
const emits = defineEmits(['action']);
const props = defineProps({
  cols: {
    type: Number,
    default: 1,
  },
  data: {
    type: Object,
    defalut: () => DETAILS,
  },
});
const detailData = computed(() => {
  return props.data ? props.data : DETAILS;
});
function toDetail(v: number | string) {
  emits('action', { action: TASKACTION.DETAILS, data: { statusId: v } });
}
//开始巡检
function startAction() {}
defineOptions({ name: 'VideoTaskDetailListCont' });
</script>

<style lang="scss">
.list-cont {
  padding: 0 16px;
  flex: 1;
  .list-item {
    height: 210px;
    border-radius: 8px 8px 8px 8px;
    margin: 8px;
    background: #c4c6cc url(../../assets/sp.png) no-repeat center center/ 62px auto;
    overflow: hidden;
    position: relative;
    .tips-le {
      height: 20px;
      background: rgba(0, 0, 0, 0.65);
      border-radius: 8px 0px 0px 0px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      position: absolute;
      left: 0;
      top: 0;
      padding: 0 5px;
    }
    .tips-ri {
      height: 20px;
      background: #00b578;
      border-radius: 0px 8px 0px 8px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      right: 0px;
      top: 0px;
      padding: 0 5px;
      position: absolute;
    }
    .text {
      height: 30px;
      background: rgba(0, 0, 0, 0.5);
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 30px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 0 16px;
    }
    .cont {
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }
}
</style>
