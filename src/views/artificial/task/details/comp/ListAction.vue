<template>
  <div class="task-detail-top">
    <div class="top">
      <div class="title">巡检结果</div>
      <div class="action">
        <n-button v-if="detailData.taskStatus != 3" type="primary" size="small" @click="startAction">开始巡检</n-button>
        <n-button
          v-else
          :disabled="detailData.taskStatus !== 3"
          type="primary"
          size="small"
          :color="TASK_STATE.COLOR[detailData.taskStatus]"
          >任务回溯</n-button
        >
      </div>
    </div>
    <div class="desc">
      <div class="desc-item">巡检点数：<span class="num">10</span>个</div>
      <div class="desc-item">已巡点总数：<span class="num green">10</span>个</div>
      <div class="desc-item">未巡点总数：<span class="num red">10</span>个</div>
      <div class="desc-item">结果正常点位数：<span class="num green">10</span>个</div>
      <div class="desc-item">结果异常点位数：<span class="num red">10</span>个</div>
      <div class="list-action">
        <div class="block" v-for="b in 3" :key="b" :class="{ act: colsNum === b }" @click="closChange(b)">
          <div class="block-item" v-for="i in cols[b - 1]" :key="i"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { TASKACTION, DETAILS } from '../constant';
import { ACTION, ACTION_LABEL, TASK_STATE } from '../../constant';

const emits = defineEmits(['action']);
const props = defineProps({
  data: {
    type: Object,
    defalut: () => DETAILS,
  },
});
const detailData = computed(() => {
  return props.data ? props.data : DETAILS;
});

const cols = ref([1, 4, 9]);
const colsNum = ref(3);
function closChange(c: number) {
  colsNum.value = c;
  emits('action', { action: TASKACTION.LAYER, data: { colsNum: c } });
}
//开始巡检
function startAction() {
  emits('action', { action: TASKACTION.STARTINSPECTION, data: { taskStatus: 1 } });
}

defineOptions({ name: 'VideoTaskDetailListAction' });
</script>

<style lang="scss">
.task-detail-top {
  .top {
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
  }
  .desc {
    padding: 24px 24px 12px;
    display: flex;
    .desc-item {
      margin-right: 32px;
    }
    .num {
      color: #212121;
      &.green {
        color: #2ba471;
      }
      &.red {
        color: #d54941;
      }
    }
  }
  .list-action {
    margin-left: auto;
    display: flex;
    .block {
      width: 24px;
      height: 24px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #c7cdd9;
      margin-left: 10px;
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-around;
      // align-content: center;
      padding: 4px;
      &.act {
        background-color: #527cff;
        .block-item {
          background-color: #fff;
        }
      }
      &:first-child {
        flex: 1;
        .block-item {
          width: 12px;
          height: 12px;
        }
      }
      &:nth-child(2) {
        .block-item {
          width: 6px;
          height: 6px;
        }
      }
      &:nth-child(3) {
        padding: 2px;
        .block-item {
          width: 5px;
          height: 5px;
        }
      }
    }
    .block-item {
      background: #afb5bf;
      border-radius: 1px 1px 1px 1px;
      margin: 0.5px 0.5px;
    }
  }
}
</style>
