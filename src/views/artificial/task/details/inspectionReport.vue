<template>
  <!-- 上报 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="task-status-detail">
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="status-detail-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">11111</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="status-detail-cont">
        <n-form
          ref="formRef"
          :model="formModel"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          :size="size"
          :style="{
            maxWidth: '470px',
          }"
        >
          <n-form-item label="隐患来源" path="inputValue">
            <n-input v-model:value="formModel.inputValue" placeholder="Input" />
          </n-form-item>
          <n-form-item label="上报设备" path="inputValue1">
            <n-input v-model:value="formModel.inputValue1" placeholder="Input" />
          </n-form-item>
          <n-form-item label="上报时间" path="datetimeValue">
            <n-date-picker v-model:value="formModel.datetimeValue" type="datetime" />
          </n-form-item>
          <n-form-item label="巡检结果" path="radioGroupValue">
            <n-radio-group v-model:value="formModel.radioGroupValue" name="radiogroup1">
              <n-space>
                <n-radio value="1"> 无异常 </n-radio>
                <n-radio value="2"> 有异常</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="隐患位置" path="inputValue2">
            <n-input v-model:value="formModel.inputValue2" placeholder="Input" />
          </n-form-item>
          <n-form-item label="隐患描述" path="inputValue3">
            <n-input v-model:value="formModel.inputValue3" placeholder="Input" />
          </n-form-item>
          <n-form-item label="隐患等级" path="radioGroupValue1">
            <n-radio-group v-model:value="formModel.radioGroupValue1" name="radiogroup1">
              <n-space>
                <n-radio value="1"> 一般隐患 </n-radio>
                <n-radio value="2"> 重大隐患</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="隐患等级" path="radioGroupValue2">
            <n-radio-group v-model:value="formModel.radioGroupValue2" name="radiogroup1">
              <n-space>
                <n-radio value="1"> 一般隐患 </n-radio>
                <n-radio value="2"> 重大隐患</n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item>
          <n-form-item label="现场照片">
            <img-upload :data="imgData" @update="handleUpdate" :size="10" mode="browse" tips="仅支持xxx"></img-upload>
          </n-form-item>
        </n-form>
      </div>
      <template #footer>
        <div class="status-footer">
          <n-button type="primary" @click="close" color="#DCDFE6"> 取消 </n-button>
          <n-button type="primary" @click="handleValidateButtonClick">确定 </n-button>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { TASKACTION } from './constant';
import { taskDetailApi } from '../fetchData';
import { ImgUpload } from '@/components/upload';
import { IUploadRes } from '@/components/upload/type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);
const emits = defineEmits(['action']);

import type { FormInst, FormItemRule, UploadFileInfo } from 'naive-ui';
const formRef = ref<FormInst | null>(null);

const size = ref('medium');
const formModel = ref({
  inputValue: null,
  inputValue1: null,
  inputValue2: null,
  inputValue3: null,
  datetimeValue: null,
  radioGroupValue: null,
  radioGroupValue1: null,
  radioGroupValue2: null,
});
const rules = {
  inputValue: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 inputValue',
  },
  inputValue1: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 textareaValue',
  },
  inputValue2: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 textareaValue',
  },
  inputValue3: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入 textareaValue',
  },

  datetimeValue: {
    type: 'number',
    required: true,
    trigger: ['blur', 'change'],
    message: '请输入 datetimeValue',
  },

  radioGroupValue: {
    required: true,
    trigger: 'change',
    message: '请选择 radioGroupValue',
  },
  radioGroupValue1: {
    required: true,
    trigger: 'change',
    message: '请选择 radioGroupValue',
  },
  radioGroupValue2: {
    required: true,
    trigger: 'change',
    message: '请选择 radioGroupValue',
  },
};
const imgData = ref([]);
// 上传回调
function handleUpdate(res: IUploadRes[]) {
  console.log('上传回调', res);
  if (!res || !res.length) return;
  const ids = res.map((item) => item.fjTywysbm);
  console.log('ids:', ids.join());
}
// const showModal = ref(false);
// const previewImageUrl = ref('');
// const previewFileList = ref<UploadFileInfo[]>([
//   //   {
//   //     id: 'react',
//   //     name: '我是react.png',
//   //     status: 'finished',
//   //     url: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
//   //   }
// ]);
// function handlePreview(file: UploadFileInfo) {
//   console.log(file, '-------');
//   const { url } = file;
//   previewImageUrl.value = url as string;
//   showModal.value = true;
// }
function handleValidateButtonClick(e: MouseEvent) {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      //   message.success('验证成功');
    } else {
      console.log(errors);
      //   message.error('验证失败');
    }
  });
}

const active = ref(false);
const taskAct = ref<string | number>();
const detailData = ref<any>();

function open(data: any) {
  console.log(data, '打开上报异常弹框');
  taskAct.value = data;
  active.value = true;
}
function close() {
  active.value = false;
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
  formModel.value = {
    inputValue: null,
    inputValue1: null,
    inputValue2: null,
    inputValue3: null,
    datetimeValue: null,
    radioGroupValue: null,
    radioGroupValue1: null,
    radioGroupValue2: null,
  };
}
function toPro(v: number | string) {
  emits('action', { action: TASKACTION.PROCESSES, data: { statusId: v } });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskInspReport' });
</script>

<style lang="scss">
.task-status-detail {
  .n-drawer-header__main {
    width: 100%;
  }
  .status-detail-header {
    display: flex;
    width: 100%;
    .header-icon {
      width: 18px;
      height: auto;
      margin-right: 8px;
    }
    .header-title {
      font-weight: 600;
      font-size: 16px;
      color: #18191a;
    }
    .btn-close {
      margin-left: auto;
    }
  }
  .status-detail-cont {
    padding: 20px 24px;
    .img-upload-wrap {
      display: flex;
      width: 430px;
    }
  }
  .status-footer {
    display: flex;
    justify-content: flex-end;
    .n-button {
      width: 88px;
      padding: 0px;
      &:first-child {
        color: #606266;
        border: 1px solid #dcdfe6;
        margin-right: 12px;
      }
    }
  }
}
</style>
