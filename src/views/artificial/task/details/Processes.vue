<template>
  <!-- 处理过程查看 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="task-status-detail">
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="status-detail-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">11111</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="status-detail-cont">处理过程</div>
      <!-- <template #footer>
        <n-button>Footer</n-button>
      </template> -->
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, PropType } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';

import { taskDetailApi } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);

const active = ref(false);
const taskAct = ref<string | number>();
const detailData = ref<any>();

function open(data: any) {
  console.log(data, '打开详情的处理过程弹框');
  taskAct.value = data;
  active.value = true;
  //todo taskAct.value.id 详情内容获取
}
function close() {
  active.value = false;
}

function getDetails(id: string | number) {
  // detailData.value = {
  //   createTime: '2024-09-16T02:51:13.081Z',
  //   createdBy: 'string',
  //   delFlag: 0,
  //   deptId: 'string',
  //   deptName: 'string',
  //   id: '1111',
  //   planId: 'string',
  //   planName: 'string',
  //   recordPoint: 'string',
  //   taskEndTime: '2024-09-16T02:51:13.081Z',
  //   taskPlanEndTime: '2024-09-16T02:51:13.081Z',
  //   taskPlanStartTime: '2024-09-16T02:51:13.081Z',
  //   taskStartTime: '2024-09-16T02:51:13.081Z',
  //   taskStatus: 0,
  //   updateTime: '2024-09-16T02:51:13.081Z',
  //   updatedBy: 'string',
  //   zhId: 'string',
  //   pro: 0,
  //   list: [], //中间列表
  // };
  // //以上为模拟数据
  run(taskDetailApi({ id })).then((res) => {
    detailData.value = res.data;
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskStatuDetail' });
</script>

<style lang="scss">
.task-status-detail {
  .n-drawer-header__main {
    width: 100%;
  }
  .status-detail-header {
    display: flex;
    width: 100%;
    .header-icon {
      width: 18px;
      height: auto;
      margin-right: 8px;
    }
    .header-title {
      font-weight: 600;
      font-size: 16px;
      color: #18191a;
    }
    .btn-close {
      margin-left: auto;
      cursor: pointer;
    }
  }
  .status-detail-cont {
    padding: 20px 24px;
    .status-img {
      width: 100%;
      height: 266px;
      border-radius: 4px;
      background: #f0f0f0;
      overflow: hidden;
      .img {
        width: auto;
        height: 100%;
        margin: 0 auto;
      }
      .no-img {
        width: auto;
        height: 80px;
        margin: 20% auto 0;
      }
    }
    .status-card {
      min-height: 242px;
      border-radius: 4px;
      border: 1px solid #ebeef5;
      padding: 20px;
      margin-top: 16px;
      line-height: 20px;
      .card-title {
        font-weight: 600;
        font-size: 16px;
        color: #222222;
      }
      .card-item {
        margin-top: 16px;
        display: flex;
      }
      .ope {
        margin-left: auto;
        color: #527cff;
        cursor: pointer;
      }
    }
  }
}
</style>
