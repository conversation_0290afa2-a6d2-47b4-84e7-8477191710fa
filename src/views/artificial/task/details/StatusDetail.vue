<template>
  <!-- 状态结果详情 -->
  <n-drawer v-model:show="active" width="520" :close-on-esc="false" :mask-closable="false" class="com-detail-drawer">
    <n-drawer-content :native-scrollbar="false" :body-content-style="{ padding: '0px' }">
      <template #header>
        <div class="com-detail-drawer-header">
          <img class="header-icon" src="./assets/edit-icon.png" alt="" />
          <span class="header-title">11111</span>
          <div class="btn-close" @click="close">
            <IconClose class="icon" />
          </div>
        </div>
      </template>
      <div class="com-detail-drawer-cont">
        <div class="status-img">
          <img v-if="false" src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg" class="img" />
          <img v-else src="@/assets/noData.png" class="no-img" />
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">设备信息（人工巡检的时候显示上报人员）</div>
          <div class="card-item">
            <div class="lab">上报设备：</div>
            <div class="val">四合一气体检测设备</div>
          </div>
          <div class="card-item">
            <div class="lab">上报时间：</div>
            <div class="val">2024-09-01 12:23:32</div>
          </div>
          <div class="card-item">
            <div class="lab">设备编号：</div>
            <div class="val">2024-09-01 12:23:32</div>
          </div>
          <div class="card-item">
            <div class="lab">设备位置：</div>
            <div class="val">调度中心</div>
            <div class="ope">查看位置（显示gis图）</div>
          </div>
          <div class="card-item">
            <div class="lab">品牌型号：</div>
            <div class="val">2024-09-01 12:23:32</div>
          </div>
          <div class="card-item">
            <div class="lab">安装日期：</div>
            <div class="val">2024-09-01 12:23:32</div>
          </div>
          <div class="card-item">
            <div class="lab">天泽防灾智盒ID：</div>
            <div class="val">2222222222</div>
          </div>
        </div>
        <div class="com-detail-drawer-card">
          <div class="card-title">巡检结果</div>
          <div class="card-item">
            <div class="lab">人员离在岗智能识别：</div>
            <div class="val">正常</div>
          </div>
          <div class="card-item">
            <div class="lab">通道占用智能识别：</div>
            <div class="val">异常</div>
            <div class="ope" @click="toPro(1)">处理过程</div>
          </div>
          <div class="card-item">
            <div class="lab">烟火智能识别：</div>
            <div class="val"></div>
          </div>
          <div class="card-item">
            <div class="lab">人员抽烟智能识别：</div>
            <div class="val"></div>
          </div>
          <div class="card-item">
            <div class="lab">脱掉安全帽只能识别：</div>
            <div class="val"></div>
          </div>
        </div>
      </div>
      <!-- <template #footer>
        <n-button>Footer</n-button>
      </template> -->
    </n-drawer-content>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, PropType } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { TASKACTION } from './constant';
import { taskDetailApi } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);
const emits = defineEmits(['action']);
const active = ref(false);
const taskAct = ref<string | number>();
const detailData = ref<any>();

function open(data: any) {
  console.log(data, '打开详情弹框');
  taskAct.value = data;
  active.value = true;
  //todo taskAct.value.id
  getDetails(1);
}
function close() {
  active.value = false;
}
function toPro(v: number | string) {
  emits('action', { action: TASKACTION.PROCESSES, data: { statusId: v } });
}
function getDetails(id: string | number) {
  // detailData.value = {
  //   createTime: '2024-09-16T02:51:13.081Z',
  //   createdBy: 'string',
  //   delFlag: 0,
  //   deptId: 'string',
  //   deptName: 'string',
  //   id: '1111',
  //   planId: 'string',
  //   planName: 'string',
  //   recordPoint: 'string',
  //   taskEndTime: '2024-09-16T02:51:13.081Z',
  //   taskPlanEndTime: '2024-09-16T02:51:13.081Z',
  //   taskPlanStartTime: '2024-09-16T02:51:13.081Z',
  //   taskStartTime: '2024-09-16T02:51:13.081Z',
  //   taskStatus: 0,
  //   updateTime: '2024-09-16T02:51:13.081Z',
  //   updatedBy: 'string',
  //   zhId: 'string',
  //   pro: 0,
  //   list: [], //中间列表
  // };
  // //以上为模拟数据
  run(taskDetailApi({ id })).then((res) => {
    detailData.value = res.data;
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskStatuDetail' });
</script>

<style lang="scss">
.com-detail-drawer {
  .status-detail-cont {
    .status-img {
      width: 100%;
      height: 266px;
      border-radius: 4px;
      background: #f0f0f0;
      overflow: hidden;
      .img {
        width: auto;
        height: 100%;
        margin: 0 auto;
      }
      .no-img {
        width: auto;
        height: 80px;
        margin: 20% auto 0;
      }
    }
  }
}
</style>
