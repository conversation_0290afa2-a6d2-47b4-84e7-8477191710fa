import type { IDetail } from '../type';
export const enum PROVIDE_KEY {
  detailData = 'detailData',
}
export const enum TASKACTION {
  NONE = 'NONE',
  LAYER = 'LAYER',
  DETAILS = 'DETAILS',
  BROWSE = 'BROWSE',
  PROCESSES = 'PROCESSES',
  STARTINSPECTION = 'STARTINSPECTION',
  REFRESH = 'REFRESH',
}

export const ACTION_LABEL: { [key in TASKACTION]: string } = {
  [TASKACTION.NONE]: '',
  [TASKACTION.LAYER]: '布局更改',
  [TASKACTION.DETAILS]: '任务的状态详情',
  [TASKACTION.BROWSE]: '浏览',
  [TASKACTION.PROCESSES]: '结果状态详情下的处理过程弹框',
  [TASKACTION.STARTINSPECTION]: '开始巡检',
  [TASKACTION.REFRESH]: '刷新页面',
};

export const DETAILS: IDetail = {
  beginTime: 'string',
  checkDemand: 'string',
  checkEndOperate: 'string',
  checkExecuteMethod: 'string',
  checkRange: 'string',
  checkTableId: 'string',
  checkTableName: 'string',
  checkTableSet: 'string',
  createBy: 'string',
  createTime: '2024-09-22T10:17:14.749Z',
  createUnitId: 'string',
  delFlag: 0,
  devicelist: [],
  essentialFactorNum: 0,
  finishTime: 'string',
  frequency: 'string',
  frequencyType: 'string',
  isNeedClock: 'string',
  planEndTime: 'string',
  planId: 'string',
  planName: 'string',
  planStartTime: 'string',
  planType: 'string',
  pointList: [],
  taskId: 'string',
  taskName: 'string',
  taskState: 'string',
  timeState: 'string',
  updateBy: 'string',
  updateTime: '2024-09-22T10:17:14.749Z',
  zhId: 'string',
  unitlist: [],
  userlist: [],
  taskStatus: 'string',
};
