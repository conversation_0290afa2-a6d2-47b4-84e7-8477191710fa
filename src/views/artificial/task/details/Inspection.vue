<template>
  <n-modal
    v-model:show="active"
    class="com-modal"
    preset="dialog"
    :close-on-esc="false"
    :mask-closable="false"
    :show-icon="false"
    :closable="false"
  >
    <template #header>
      <div class="com-modal-header">
        <img class="header-icon" src="./assets/edit-icon.png" alt="" />
        <span class="header-title">11111</span>
        <div class="btn-close" @click="close">
          <IconClose class="icon" />
        </div>
      </div>
    </template>
    <div class="com-modal-cont">
      <div class="com-modal-video"></div>
      <div class="com-modal-ope">
        <n-button type="primary" color="#FA5151" class="btn" @click="reportFn(1)"> 上报异常 </n-button>
        <n-button type="primary" @click="reportFn(2)"> 无异常 </n-button>
      </div>
    </div>
    <inspection-report ref="inspReportRef"></inspection-report>
  </n-modal>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, PropType } from 'vue';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';
import { taskDetailApi } from '../fetchData';
import InspectionReport from './inspectionReport.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);

const active = ref(false);
const detailList = ref<any>([]);
const inspReportRef = ref();

function open(data: any) {
  console.log(data, '打开巡检上报弹框,接收是一个上报LIST');
  active.value = true;
  detailList.value = data;
}
function close() {
  active.value = false;
}
function reportFn(type: number) {
  //type 上报异常 1 无2
  inspReportRef.value.open();
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskInspectionReport' });
</script>

<style lang="scss">
.task-inspect {
  width: 800px !important;
  height: 590px;
  padding: 0px;
  .n-dialog__content {
    margin: 0 !important;
  }
  .task-inspect-header {
    display: flex;
    height: 52px;
    border-bottom: 1px solid #ebeef5;
    align-items: center;
    width: 100%;
    .header-icon {
      width: 18px;
      height: 12px;
      margin-right: 8px;
      margin-left: 24px;
    }
    .header-title {
      font-weight: 600;
      font-size: 16px;
      color: #18191a;
    }
    .btn-close {
      margin-left: auto;
      margin-right: 24px;
      cursor: pointer;
    }
  }
  .task-inspect-cont {
    padding: 20px 24px 24px;
    .task-video {
      height: 430px;
      background-color: #f0f0f0;
    }
    .task-inspect-ope {
      margin-top: 23px;
      text-align: center;
      .btn {
        margin-right: 16px;
      }
    }
  }
}
</style>
