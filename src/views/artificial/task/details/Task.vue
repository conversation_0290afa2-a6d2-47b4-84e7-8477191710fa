<template>
  <n-drawer
    v-model:show="active"
    to="#drawer-target"
    :close-on-esc="false"
    :mask-closable="false"
    :show-mask="false"
    :block-scroll="true"
    class="task-detail"
  >
    <n-drawer-content :body-content-style="{ padding: '0px' }">
      <div class="task-detail-wrap">
        <div class="task-detail-head">
          <div class="head-back" @click="close"><AkArrowBackThickFill /></div>
          <div class="head-title">{{ ymrDateFn(detailData.planStartTime) }} {{ detailData.planName }}</div>
          <div class="head-state" :style="{ backgroundColor: TASK_STATE.COLOR[parseInt(detailData.taskState)] }">
            {{ TASK_STATE.LAB[parseInt(detailData.taskState)] }}
          </div>
        </div>
        <div class="task-detail-cont">
          <con-le :data="detailData"></con-le>
          <!-- <div class="task-detail-ri">
            <list-action @action="actionFn" :data="detailData"></list-action>
            <list-cont :cols="colsNum" :data="detailData" @action="actionFn"></list-cont>
          </div> -->
        </div>
      </div>
    </n-drawer-content>
    <!-- 状态详情 -->
    <status-detail ref="statusDetailRef" @action="actionFn"></status-detail>
    <!-- 状态详情下的处理过程 -->
    <processes ref="statusProRef"></processes>
    <!-- 巡检上报视屏弹框 -->
    <inspection ref="inspectRef"></inspection>
  </n-drawer>
</template>

<script lang="ts" setup>
import { computed, provide, Ref, ref, watch, PropType } from 'vue';
import { TASK_STATE } from '../constant';
import { PROVIDE_KEY, TASKACTION, DETAILS } from './constant';
import type { IActionData } from './type';
import type { IDetail } from '../type';
import ConLe from './comp/ContLe.vue';
import ListAction from './comp/ListAction.vue';
import ListCont from './comp/ListCont.vue';
import ComPag from '@/components/pag/ComPag.vue';
import StatusDetail from './StatusDetail.vue';
import Processes from './Processes.vue';
import Inspection from './Inspection.vue';
import { AkArrowBackThickFill } from '@kalimahapps/vue-icons';
import { taskDetailApi } from '../fetchData';
import { ymrDateFn } from '@/views/common/utils.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(false);

const active = ref(false);
const taskAct = ref();
const colsNum = ref(3);
const detailData = ref<IDetail>(DETAILS);
const total = ref(0);
const statusDetailRef = ref();
const statusProRef = ref();
const inspectRef = ref();
const emits = defineEmits(['action']);
function open(detailId: string, detailStatus: number) {
  console.log(detailId, '打开详情弹框');
  taskAct.value = {
    id: detailId,
    status: detailStatus,
  };
  active.value = true;
  //todo
  getDetails(taskAct.value.id);
}
function close() {
  active.value = false;
  emits('action', { action: TASKACTION.REFRESH, data: {} });
}
const currentAction = ref<IActionData>({ action: TASKACTION.NONE, data: {} });

function actionFn(val: IActionData) {
  currentAction.value.action = val.action;
  currentAction.value.data = {
    ...currentAction.value.data,
    ...val.data,
  };
  switch (val.action) {
    case TASKACTION.LAYER:
      console.log('布局更改');
      colsNum.value = val.data.colsNum;
      break;
    case TASKACTION.BROWSE:
      console.log('浏览');
      break;
    case TASKACTION.DETAILS:
      console.log('状态详情');
      statusDetailRef.value.open(val.data);
      break;
    case TASKACTION.PROCESSES:
      console.log('状态详情下的处理过程');
      statusProRef.value.open(val.data);
      break;
    case TASKACTION.STARTINSPECTION:
      console.log('开始巡检');
      inspectRef.value.open([]);
      break;
  }
}

function getDetails(id: string | number) {
  taskDetailApi({ id }).then((detail) => {
    detailData.value = detail.data;
  });
}

defineExpose({
  open,
  close,
});

defineOptions({ name: 'VideoTaskDetail' });
</script>

<style lang="scss">
.task-detail {
  width: 100% !important;
  background: #eef7ff;
  border: 1px solid #ffffff;
  border-radius: 9px !important;
  .task-detail-wrap {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    min-width: 1358px;
  }
  .task-detail-head {
    height: 56px;
    background: #dce4f4;
    border-radius: 9px 9px 0px 0px;
    padding: 0 23px;
    display: flex;
    align-items: center;
    color: #242526;
    font-size: 16px;
    flex-shrink: 0;
    .head-back {
      cursor: pointer;
    }
    .head-title {
      margin-left: 16px;
      margin-right: 16px;
    }
    .head-state {
      padding: 0 4px;
      height: 20px;
      line-height: 20px;
      background: #527cff;
      border-radius: 3px 3px 3px 3px;
      font-weight: 400;
      font-size: 12px;
      color: #fff;
    }
  }
  .task-detail-cont {
    flex: 1;
    display: flex;
    overflow: auto;
    .task-detail-ri {
      flex: 1;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      min-width: 500px;
    }
  }
}
</style>
