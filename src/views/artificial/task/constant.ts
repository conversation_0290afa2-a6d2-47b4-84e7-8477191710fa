import type { ICardAItem } from '@/components/card/type';
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  DETAILS = 'DETAILS',
  EXPORT = 'EXPORT',
  TREECHANGE = 'TREECHANGE',
  CARDCHANGE = 'CARDCHANGE',
  REFRESH = 'REFRESH',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.SEARCH]: '搜索',
  [ACTION.DETAILS]: '详情',
  [ACTION.EXPORT]: '导出',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片切换筛选',
  [ACTION.REFRESH]: '刷新页面',
};
//任务状态(1:待开始;2:进行中;3:已完成,4逾期)"
export const TASK_STATE = {
  LAB: ['', '待开始', '进行中', '已完成', '已逾期'],
  COLOR: ['', '#F39600', '#527CFF', '#00B578', '#FA5151'],
};

//统计卡片
export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: 0,
  },
  {
    label: '进行中（个）',
    value: 0,
    id: 2,
  },
  {
    label: '待开始（个）',
    value: 0,
    id: 1,
  },
  {
    label: '已完成（个）',
    value: 0,
    id: 3,
  },
  {
    label: '已逾期（个）',
    value: 0,
    id: 4,
  },
];

//计划频次类型:0:时;1:日;2:周;3:月;4:季度;5:年;99:不重复")
export const iFrequencyType = ['小时', '日', '周', '月', '季度', '年'];
