<template>
  <div class="com-warp-col">
    <com-loading v-if="loading"></com-loading>
    <com-bread :data="breadData"></com-bread>
    <div class="com-warp-col-main" id="drawer-target">
      <com-tab @tab-action="tabChange" :tab="tabAct" :tab-list="tabData"></com-tab>
      <div style="background: none" class="com-warp-col-container">
        <plan v-if="tabAct === 'plan'" :tree-list="treeData"></plan>
        <task v-if="tabAct === 'task'" :tree-list="treeData" @changeBread="changeBreadFn"></task>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { TreeOption } from 'naive-ui';
import { ref, onMounted } from 'vue';
import ComLoading from '@/components/loading/comLoading.vue';
import ComTab from '@/components/tab/ComRadioTabE.vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import plan from './plan/index.vue';
import task from './task/index.vue';
import { tabData } from './setData';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
const [loading, run] = useAutoLoading(true);
import { postTreeList } from '../common/fetchData';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();
const breadData = ref<IBreadData[]>([{ name: '智能巡检' }, { name: '人工智能巡检' }]);
import { useStore } from '@/store';
const store = useStore();

const tabAct = ref('');
tabAct.value = route.query.tab ? (route.query.tab as string) : 'task';

function tabChange(v: string) {
  tabAct.value = v;
  router.replace(`artificial?tab=${v}`);
}

const treeParam = ref({
  //不要下级单位,1要，0不要
  needChildUnit: '1',
  //是否包含自己,1包含 0不包含
  needself: '1',
  //机构id=10000,顶级是-1
  orgCode: store.userInfo.orgCode,
});

const treeData = ref<TreeOption[]>([]);
function treeList() {
  run(postTreeList(treeParam.value))
    .then((res) => {
      treeData.value = res.data || [];
    })
    .catch(() => {});
}
function changeBreadFn(v: { text: string; isAdd: boolean }) {
  console.log('任务详情', v);
  v.isAdd
    ? breadData.value.splice(breadData.value.length, 0, { name: v.text })
    : breadData.value.splice(breadData.value.length - 1, 1);
}
treeList();
onMounted(() => {
  console.log(route.query, 'query');
});
defineOptions({ name: 'ArtificialIndex' });
</script>

<style module lang="scss">
.videoWrap {
  display: flex;
  flex-direction: column;
  position: relative;
  .videoWrapMain {
    display: flex;
    flex-direction: column;
    position: relative;
    flex: 1;
  }
  .container {
    flex: 1;
    // background: #eef7ff;
    position: relative;
    // border-radius: 9px;
    border-bottom-left-radius: 9px;
    border-bottom-right-radius: 9px;
  }
}
</style>
