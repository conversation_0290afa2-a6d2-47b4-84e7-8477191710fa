/**
 * 公共配色
 * 命名规则: 以 skin-c 开头
 */

@use "sass:color";

// 转换十六进制颜色值为 RGB 或 RGBA 字符串
@function hex-to-rgb($hex, $alpha: null) {
    $r: color.channel($hex, "red", $space: rgb);
    $g: color.channel($hex, "green", $space: rgb);
    $b: color.channel($hex, "blue", $space: rgb);

    @if $alpha == null {
        @return #{$r}, #{$g}, #{$b}; // 返回 "r, g, b"
    } @else {
        @return #{$r}, #{$g}, #{$b}, #{$alpha}; // 返回 "r, g, b, a"
    }
}


:root {
    // primary
    $color-primary: #4570f6;

    // com
    --skin-c1: #4570f6;
    --skin-c2: #20a32d;
    --skin-c3: #ff3232;
    --skin-c4: #00fefe;
    --skin-c5: #093c6c;

    // 以下定义的 -rgb 值 仅供 plugin-custom-opacity 插件使用
    --skin-c1-rgb: #{hex-to-rgb(#4570f6)};
    --skin-c2-rgb: #{hex-to-rgb(#20a32d)};
    --skin-c3-rgb: #{hex-to-rgb(#ff3232)};
    --skin-c4-rgb: #{hex-to-rgb(#00fefe)};
    --skin-c5-rgb: #{hex-to-rgb(#093c6c)};
}
