//上下布局
.com-warp-col {
    display: flex;
    flex-direction: column;
    position: relative;
    .com-warp-col-main {
        display: flex;
        flex-direction: column;
        position: relative;
        flex: 1;
    }
    .com-warp-col-container {
        flex: 1;
        background: #eef7ff;
        position: relative;
        border-bottom-left-radius: 9px;
        border-bottom-right-radius: 9px;
        // border: 1px solid #ffffff;
        border-top: 0px;
    }
}
//左右布局
.com-layer-le-ri {
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 100%;
    .layer-le {
        width: 323px;
        padding: 20px;
        border-right: 1px solid #c8ced9;
        overflow: hidden;
        overflow-y: auto;
        position: relative;
        //延伸线
        // &::after{
        //   position: absolute;
        //   content: "";
        //   right: -1px;
        //   top:-10px;
        //   width: 1px;
        //   height:20px;
        //   background-color:#c8ced9;
        // }
    }
    .layer-ri {
        flex: 1;
        overflow: hidden;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }
    .layer-cont {
        flex: 1;
        overflow: auto;
    }
}
.com-layer-container {
    width: 100%;
    overflow: visible;
    background: none !important;
    .layer-le {
        width: 323px;
        border-right: none !important;
        //background: #eef7ff;
        margin-right: 20px;
        border-radius: 5px;
        transition: 0.3s;
        flex-shrink: 0;
    }
    .layerVisible {
        width: 0;
        padding: 0;
        margin-right: 0;
    }
    .layer-ri {
        position: relative;
        //background: #eef7ff;
        border-radius: 5px;
        overflow: hidden;
        .layer-strenth {
            position: fixed;
            // left: 0;
            top: 50%;
            z-index: 999;
            transform: translate(-50%, -50%);
            img {
                width: 32px;
                height: 32px;
                cursor: pointer;
            }
        }
        .layer-ri-inner {
            flex: 1;
            overflow: hidden;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            height: inherit;
        }
    }
}

.layer-le, .layer-ri {
    background: rgba(var(--skin-bg1-rgb), 0.2);
}

.com-action-button1 {
    --n-text-color: var(--com-primary-color) !important;
    border: 1px solid var(--com-primary-color) !important;
    --n-text-color-hover: var(--com-primary-color) !important;
    --n-color-pressed: var(--n-color) !important;
    --n-text-color-pressed: var(--com-primary-color) !important;
    --n-text-color-focus: var(--com-primary-color) !important;
    // margin-left: 5px !important;
    margin-right: 10px !important;
    // --n-text-color-disabled: #FFF;

    &:not(.n-button--disabled):hover {
        opacity: 0.8;
    }
    &.n-button.n-button--disabled {
        color: #a6a6a6 !important;
        background: rgba(229, 229, 229, 0.4) !important;
        border: 1px solid #bcc2cc !important;
    }
    // &.n-button:not(.n-button--disabled):active{
    //  opacity: 0.6;
    // }
    &.n-button:not(.n-button--disabled):focus {
        // background-color: var(--n-color-focus);
        // color: var(--n-text-color-focus);
        opacity: 0.6;
    }
    &.n-button .n-button__content ~ .n-button__icon {
        margin-left: 0px;
    }
    //cont slot
    //  &.n-button .n-base-wave{
    //   background: var(--n-color);
    //  }
}

//进度条
.com-prog {
    &.n-progress.n-progress--line .n-progress-content {
        width: 120px;
    }
    &.n-progress .n-progress-icon {
        --n-icon-color: #00b578 !important;
        width: 40px !important;
    }
    &.n-progress .n-progress-graph .n-progress-graph-line .n-progress-graph-line-rail {
        --n-rail-color: #ced3e0 !important;
    }
    // &.n-progress.n-progress--line .n-progress-icon.n-progress-icon--as-text{
    //   width: 50px !important;
    // }
}

//详情抽屉样式
.com-detail-drawer {
    background-color: #fff !important;
    position: relative;
    .n-drawer-header__main {
        width: 100%;
    }
    .com-detail-drawer-header {
        display: flex;
        width: 100%;
        .header-icon {
            width: 18px;
            height: auto;
            margin-right: 8px;
        }
        .header-title {
            font-weight: 600;
            font-size: 16px;
            color: #18191a;
        }
        .btn-close {
            margin-left: auto;
            cursor: pointer;
        }
    }
    .com-detail-drawer-cont {
        padding: 20px 24px;
        .com-detail-drawer-card {
            border-radius: 4px;
            border: 1px solid #ebeef5;
            padding: 20px;
            margin-bottom: 16px;
            line-height: 20px;
            .card-title {
                font-weight: 600;
                font-size: 16px;
                color: #222222;
                margin-bottom: 16px;
            }
            .ope {
                margin-left: auto;
                color: #527cff;
                cursor: pointer;
            }
        }
        .card-item {
            margin-bottom: 16px;
            display: flex;
        }
    }
    .com-detail-drawer-footer {
        display: flex;
        justify-content: flex-end;
        .n-button {
            width: 88px;
            padding: 0px;
            &:first-child {
                color: #606266;
                border: 1px solid #dcdfe6;
                margin-right: 12px;
            }
        }
    }
}
//弹框
.com-modal {
    width: 800px !important;
    height: 590px;
    padding: 0px;
    display: flex;
    flex-direction: column;
    &.n-dialog {
        padding: 0px;
    }
    .n-dialog__content {
        margin: 0 !important;
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .com-modal-header {
        display: flex;
        height: 52px;
        border-bottom: 1px solid #ebeef5;
        align-items: center;
        width: 100%;
        .header-icon {
            width: 18px;
            height: 12px;
            margin-right: 8px;
            margin-left: 24px;
        }
        .header-title {
            font-weight: 600;
            font-size: 16px;
            color: #18191a;
        }
        .btn-close {
            margin-left: auto;
            margin-right: 24px;
            cursor: pointer;
        }
    }
    .com-modal-cont {
        padding: 20px 24px 24px;
        flex: 1;
        display: flex;
        flex-direction: column;
        .com-modal-video {
            // height: 430px;
            background-color: #f0f0f0;
            flex: 1;
        }
        .com-modal-ope {
            margin-top: 23px;
            text-align: center;
            .btn {
                margin-right: 16px;
            }
        }
    }
}
