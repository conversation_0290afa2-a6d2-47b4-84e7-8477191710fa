// digital 数码字体
//@font-face {
//  font-family: 'digital';
//  src: url('src/css/font/digital-7.ttf');
//  font-weight: normal;
//  font-style: normal;
//}

// youshe 优设标题黑字体
@font-face {
  font-family: 'youshe';
  src: url('src/css/font/youshebiaotihei.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'dinPro';
  src: url("./dinPro.ttf");
  font-weight: normal;
  font-style: normal;
}