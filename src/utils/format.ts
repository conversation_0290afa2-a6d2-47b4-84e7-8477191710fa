import { dayjs } from '@/utils/dayjs.ts';

/**
 * 格式化时间戳为指定格式
 * @param timestamp {number} - 时间戳（毫秒）
 * @param format {string} - 格式化字符串（默认：'YYYY-MM-DD HH:mm:ss'）
 * @returns {string} - 返回格式化后的日期字符串
 */
export function formatTimestamp(timestamp: number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(timestamp).format(format);
}

/**
 * 格式化时间范围显示
 * @param startTime {string} - 开始时间（格式：'YYYY-MM-DD HH:mm:ss'）
 * @param endTime {string} - 结束时间（格式：'YYYY-MM-DD HH:mm:ss'）
 * @returns {string} - 返回格式化后的时间范围字符串
 *
 * 规则：
 * - 如果开始时间和结束时间的年月日相同，显示：'2025-09-08 12:12:56 ~ 14:12:56'
 * - 如果开始时间和结束时间的年月日不同，显示：'2025-09-08 12:12:56 ~ 2025-09-09 14:12:56'
 * - 如果没有开始时间，返回：'--'
 * - 如果没有结束时间，返回开始时间
 */
export function getFormatTimeRange(startTime?: string, endTime?: string): string {
  // 期望格式 2025-09-08 12:12:56 ~ 14:12:56
  if (!startTime) {
    return '--';
  }

  if (!endTime) {
    return startTime;
  }

  // 提取开始时间和结束时间的日期部分
  const startDate = startTime.split(' ')[0] || '';
  const endDate = endTime.split(' ')[0] || '';
  const endTimeOnly = endTime.split(' ')[1] || '';

  if (!endTimeOnly) {
    return startTime;
  }

  // 如果年月日不同，显示完整的时间范围
  if (startDate !== endDate) {
    return `${startTime} ~ ${endTime}`;
  }

  // 如果年月日相同，保持原有格式
  return `${startTime} ~ ${endTimeOnly}`;
}
