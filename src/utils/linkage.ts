// 用于联动gis屏
import { misToGisBridgeService } from '@/service/bridge/misToGisBridge';
export function linkageFun(riskId: any) {
  if (riskId) {
    misToGisBridgeService.init();
    misToGisBridgeService.sendEvents('show_gis_risk_pointer', { riskId: riskId });
  }
}

export function changeGisUnitId(unitId: any) {
  if (unitId) {
    misToGisBridgeService.init();
    misToGisBridgeService.sendEvents('screen_set_unit_Id', { unitId: unitId });
  }
}
